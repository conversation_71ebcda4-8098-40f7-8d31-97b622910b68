# PowerMonitor Pro React Dashboard

This is a React-based implementation of the PowerMonitor Pro dashboard, providing a modern, responsive, and interactive user interface for monitoring electrical parameters.

## Features

### Core Features
- Real-time data visualization with Chart.js
- Responsive design for all device sizes
- Dark/light theme support
- Interactive charts with zoom, pan, and hover functionality
- Data export in CSV and JSON formats
- Customizable dashboard settings
- Historical data viewing

### New Advanced Features
- **Interactive Data Exploration**: Enhanced zoom, pan, and data inspection tools
- **Customizable Dashboard**: Drag, drop, resize, and configure widgets
- **Advanced Analytics**: Trend lines, moving averages, anomaly detection, and forecasting
- **Theme Builder**: Create and save custom color themes
- **Performance Optimizations**: Data aggregation, lazy loading, and worker threads
- **Mobile Optimization**: Touch-friendly controls and responsive layouts
- **Accessibility Improvements**: High contrast mode and keyboard navigation

## Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- XAMPP (for PHP backend)

## Installation

1. Navigate to the React project directory:
   ```
   cd frontend/react
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm start
   ```

4. Build for production:
   ```
   npm run build
   ```

## Usage

### Development Mode

1. Start the XAMPP server
2. Start the React development server:
   ```
   npm start
   ```
3. Open `http://localhost/online data logger/frontend/react_dashboard.php` in your browser

### Production Mode

1. Build the React application:
   ```
   npm run build
   ```
2. Start the XAMPP server
3. Open `http://localhost/online data logger/frontend/react_dashboard.php` in your browser

## Project Structure

```
frontend/react/
├── public/                 # Static files
├── src/                    # Source code
│   ├── components/         # React components
│   │   ├── dashboard/      # Dashboard-specific components
│   │   ├── layout/         # Layout components
│   │   ├── modals/         # Modal components
│   │   │   ├── AnalyticsModal.js    # Analytics configuration
│   │   │   ├── CustomizeModal.js    # Dashboard customization
│   │   │   ├── ThemeBuilderModal.js # Theme customization
│   │   │   └── ...
│   │   ├── ui/             # UI components
│   │   │   ├── LoadingSpinner.js    # Loading indicator
│   │   │   └── ...
│   │   └── widgets/        # Widget components
│   ├── contexts/           # React contexts
│   │   ├── AnalyticsContext.js      # Analytics state management
│   │   ├── DashboardContext.js      # Dashboard customization state
│   │   ├── ThemeContext.js          # Theme state management
│   │   └── ...
│   ├── utils/              # Utility functions
│   │   ├── dataUtils.js             # Data processing utilities
│   │   ├── layoutUtils.js           # Layout management utilities
│   │   ├── performanceUtils.js      # Performance optimization utilities
│   │   ├── uiUtils.js               # UI helper functions
│   │   └── ...
│   ├── styles/             # CSS styles
│   ├── App.js              # Main App component
│   └── index.js            # Entry point
├── package.json            # Dependencies and scripts
└── webpack.config.js       # Webpack configuration
```

## Customization

### Theme

You can customize the theme in two ways:
1. Modify the CSS variables in `src/styles/global.css`
2. Use the new Theme Builder in the dashboard interface

### Chart Configuration

Chart configurations can be modified in the `GraphWidget.js` component.

### Dashboard Layout

The dashboard layout can now be customized in three ways:
1. Use the drag-and-drop interface in the dashboard (click "Customize" in the sidebar)
2. Modify the grid layout in `DashboardGrid.js`
3. Edit the layout configuration in `src/utils/layoutUtils.js`

## Advanced Features Guide

### Dashboard Customization

1. Click the "Customize" button in the sidebar
2. Toggle "Edit Layout" to enable drag and drop
3. Drag widgets to rearrange them
4. Resize widgets by dragging their corners
5. Add or remove widgets from the available list
6. Click "Save Layout" to store your configuration

### Analytics Features

1. Click the "Analytics" button in the sidebar
2. Enable/disable analytics features:
   - Moving Average: Smooths data to show trends
   - Trend Line: Shows overall direction
   - Anomaly Detection: Highlights unusual values
   - Forecasting: Predicts future values
3. Adjust settings for each feature
4. Apply changes to see results on the dashboard

### Theme Builder

1. Click the "Theme Builder" button in the sidebar
2. Choose a primary color or select from presets
3. Preview the theme in light or dark mode
4. Click "Apply Theme" to update the dashboard

### Mobile Usage

1. The sidebar automatically collapses on small screens
2. Tap the menu button to expand/collapse the sidebar
3. Widgets stack vertically for better mobile viewing
4. Touch-optimized controls for all interactions

## Backend Integration

The React dashboard integrates with the existing PHP backend through API calls. The main endpoints used are:

- `../backend/get_latest_data.php` - Fetches the latest data
- `../backend/get_historical_data.php` - Fetches historical data

## Troubleshooting

### CORS Issues

If you encounter CORS issues during development, make sure the webpack dev server proxy is correctly configured in `webpack.config.js`.

### Data Not Loading

Check the browser console for any errors. Make sure the XAMPP server is running and the backend API endpoints are accessible.

### Performance Issues

If you experience performance issues:
- Reduce the time window for live data in dashboard settings
- Enable data aggregation in the settings
- Close other browser tabs/applications
- Check browser console for memory usage

### Layout Problems

If you encounter layout issues:
- Reset to default layout using the "Reset Layout" button in Customize modal
- Clear browser cache and local storage
- Try a different browser
- Check if you're in edit mode (toggle it off)

### Analytics Features Not Working

If analytics features aren't working properly:
- Make sure you have sufficient data points (at least 10)
- Check browser console for errors
- Try disabling and re-enabling the feature
- Refresh the page

### Build Issues

If you encounter build issues, try clearing the node_modules folder and reinstalling dependencies:

```
rm -rf node_modules
npm install
```

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
