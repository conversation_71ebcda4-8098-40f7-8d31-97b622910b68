import {
  debounce,
  throttle,
  memoize,
  aggregateData
} from '../performanceUtils';

describe('performanceUtils', () => {
  describe('debounce', () => {
    jest.useFakeTimers();
    
    it('should debounce function calls', () => {
      const mockFn = jest.fn();
      const debouncedFn = debounce(mockFn, 100);
      
      // Call the debounced function multiple times
      debouncedFn();
      debouncedFn();
      debouncedFn();
      
      // Function should not have been called yet
      expect(mockFn).not.toHaveBeenCalled();
      
      // Fast-forward time
      jest.advanceTimersByTime(100);
      
      // Function should have been called once
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
    
    it('should use the latest arguments', () => {
      const mockFn = jest.fn();
      const debouncedFn = debounce(mockFn, 100);
      
      // Call with different arguments
      debouncedFn('first');
      debouncedFn('second');
      debouncedFn('third');
      
      // Fast-forward time
      jest.advanceTimersByTime(100);
      
      // Function should have been called with the latest arguments
      expect(mockFn).toHaveBeenCalledWith('third');
    });
  });
  
  describe('throttle', () => {
    jest.useFakeTimers();
    
    it('should throttle function calls', () => {
      const mockFn = jest.fn();
      const throttledFn = throttle(mockFn, 100);
      
      // Call the throttled function
      throttledFn();
      
      // Function should have been called immediately
      expect(mockFn).toHaveBeenCalledTimes(1);
      
      // Call again
      throttledFn();
      throttledFn();
      
      // Function should not have been called again
      expect(mockFn).toHaveBeenCalledTimes(1);
      
      // Fast-forward time
      jest.advanceTimersByTime(100);
      
      // Call again
      throttledFn();
      
      // Function should have been called again
      expect(mockFn).toHaveBeenCalledTimes(2);
    });
  });
  
  describe('memoize', () => {
    it('should cache function results', () => {
      const mockFn = jest.fn(x => x * 2);
      const memoizedFn = memoize(mockFn);
      
      // Call with the same argument multiple times
      const result1 = memoizedFn(5);
      const result2 = memoizedFn(5);
      
      // Results should be the same
      expect(result1).toBe(10);
      expect(result2).toBe(10);
      
      // Function should have been called only once
      expect(mockFn).toHaveBeenCalledTimes(1);
      
      // Call with a different argument
      const result3 = memoizedFn(10);
      
      // Result should be different
      expect(result3).toBe(20);
      
      // Function should have been called again
      expect(mockFn).toHaveBeenCalledTimes(2);
    });
  });
  
  describe('aggregateData', () => {
    it('should return the same data if length is less than maxPoints', () => {
      const data = [
        { x: 1, y: 10 },
        { x: 2, y: 20 },
        { x: 3, y: 30 }
      ];
      
      const result = aggregateData(data, 5);
      
      // Result should be the same as input
      expect(result).toEqual(data);
    });
    
    it('should aggregate data correctly', () => {
      const data = [
        { x: 1, y: 10 },
        { x: 2, y: 20 },
        { x: 3, y: 30 },
        { x: 4, y: 40 },
        { x: 5, y: 50 }
      ];
      
      const result = aggregateData(data, 2);
      
      // Result should have 2 points
      expect(result).toHaveLength(2);
      
      // First point should be the average of first 3 points
      expect(result[0].x).toBe(1);
      expect(result[0].y).toBeCloseTo(20);
      expect(result[0].aggregated).toBe(true);
      expect(result[0].count).toBe(3);
      
      // Second point should be the average of last 2 points
      expect(result[1].x).toBe(4);
      expect(result[1].y).toBeCloseTo(45);
      expect(result[1].aggregated).toBe(true);
      expect(result[1].count).toBe(2);
    });
  });
});
