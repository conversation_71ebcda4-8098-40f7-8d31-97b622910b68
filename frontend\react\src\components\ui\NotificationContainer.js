import React from 'react';
import styled, { keyframes } from 'styled-components';

const NotificationContainer = ({ notifications, removeNotification }) => {
  return (
    <Container>
      {notifications.map(notification => (
        <Notification 
          key={notification.id} 
          className={notification.type}
        >
          <NotificationMessage>{notification.message}</NotificationMessage>
          <NotificationClose onClick={() => removeNotification(notification.id)}>
            <span className="material-icons-round">close</span>
          </NotificationClose>
        </Notification>
      ))}
    </Container>
  );
};

const slideIn = keyframes`
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

const Container = styled.div`
  position: fixed;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: 380px;
  
  @media (max-width: 768px) {
    max-width: calc(100% - 2rem);
  }
`;

const Notification = styled.div`
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  padding: 1rem 1.25rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  animation: ${slideIn} 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  position: relative;
  overflow: hidden;
  max-width: 100%;
  border: 1px solid var(--border-color);
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0.25rem;
  }
  
  &.info::before {
    background: var(--gradient-primary);
  }
  
  &.success::before {
    background: var(--gradient-success);
  }
  
  &.warning::before {
    background: var(--gradient-warning);
  }
  
  &.error::before {
    background: var(--gradient-accent);
  }
`;

const NotificationMessage = styled.span`
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
  flex: 1;
`;

const NotificationClose = styled.button`
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1.125rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 50%;
  transition: all var(--transition-speed) ease;
  
  &:hover {
    background-color: var(--hover-color);
    color: var(--accent-color);
  }
`;

export default NotificationContainer;
