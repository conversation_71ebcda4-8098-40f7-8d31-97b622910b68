import {
  generateTheme,
  applyTheme,
  isLightColor
} from '../uiUtils';

describe('uiUtils', () => {
  describe('generateTheme', () => {
    it('should generate a light theme by default', () => {
      const theme = generateTheme('#2563eb');
      
      // Primary color should be set
      expect(theme.primaryColor).toBe('#2563eb');
      
      // Should have light theme colors
      expect(theme.backgroundColor).toBe('#f8fafc');
      expect(theme.cardColor).toBe('#ffffff');
      expect(theme.textColor).toBe('#0f172a');
    });
    
    it('should generate a dark theme when specified', () => {
      const theme = generateTheme('#2563eb', true);
      
      // Primary color should be set
      expect(theme.primaryColor).toBe('#2563eb');
      
      // Should have dark theme colors
      expect(theme.backgroundColor).toBe('#0f172a');
      expect(theme.cardColor).toBe('#1e293b');
      expect(theme.textColor).toBe('#f8fafc');
    });
    
    it('should generate complementary colors', () => {
      const theme = generateTheme('#ff0000');
      
      // Primary color should be set
      expect(theme.primaryColor).toBe('#ff0000');
      
      // Should have generated secondary and accent colors
      expect(theme.secondaryColor).not.toBe('#ff0000');
      expect(theme.accentColor).not.toBe('#ff0000');
      expect(theme.warningColor).not.toBe('#ff0000');
    });
  });
  
  describe('applyTheme', () => {
    beforeEach(() => {
      // Reset document.documentElement.style
      document.documentElement.style.cssText = '';
    });
    
    it('should apply theme to CSS variables', () => {
      const theme = {
        primaryColor: '#2563eb',
        backgroundColor: '#f8fafc',
        textColor: '#0f172a'
      };
      
      applyTheme(theme);
      
      // CSS variables should be set
      expect(document.documentElement.style.getPropertyValue('--primary-color')).toBe('#2563eb');
      expect(document.documentElement.style.getPropertyValue('--background-color')).toBe('#f8fafc');
      expect(document.documentElement.style.getPropertyValue('--text-color')).toBe('#0f172a');
    });
    
    it('should convert camelCase to kebab-case', () => {
      const theme = {
        primaryColor: '#2563eb',
        primaryDark: '#1d4ed8',
        textSecondary: '#475569'
      };
      
      applyTheme(theme);
      
      // CSS variables should be set with kebab-case
      expect(document.documentElement.style.getPropertyValue('--primary-color')).toBe('#2563eb');
      expect(document.documentElement.style.getPropertyValue('--primary-dark')).toBe('#1d4ed8');
      expect(document.documentElement.style.getPropertyValue('--text-secondary')).toBe('#475569');
    });
  });
  
  describe('isLightColor', () => {
    it('should identify light colors', () => {
      expect(isLightColor('#ffffff')).toBe(true);
      expect(isLightColor('#f8fafc')).toBe(true);
      expect(isLightColor('#e2e8f0')).toBe(true);
      expect(isLightColor('#cbd5e1')).toBe(true);
    });
    
    it('should identify dark colors', () => {
      expect(isLightColor('#000000')).toBe(false);
      expect(isLightColor('#0f172a')).toBe(false);
      expect(isLightColor('#1e293b')).toBe(false);
      expect(isLightColor('#334155')).toBe(false);
    });
    
    it('should handle invalid colors', () => {
      expect(isLightColor('invalid')).toBe(true); // Default to true for invalid colors
    });
  });
});
