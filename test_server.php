<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Server Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 5px;
            cursor: pointer;
            border-radius: 4px;
        }
        pre {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>ESP32 Server Connection Test</h1>
    
    <div class="card">
        <h2>Server Information</h2>
        <p>Server IP: <?php echo $_SERVER['SERVER_ADDR']; ?></p>
        <p>Server Name: <?php echo $_SERVER['SERVER_NAME']; ?></p>
        <p>Server Port: <?php echo $_SERVER['SERVER_PORT']; ?></p>
        <p>PHP Version: <?php echo phpversion(); ?></p>
    </div>
    
    <div class="card">
        <h2>Test Connection</h2>
        <button id="testConnectionBtn">Test Connection</button>
        <button id="testDataEndpointBtn">Test Data Endpoint</button>
        <div id="connectionResult"></div>
    </div>
    
    <div class="card">
        <h2>Send Test Data</h2>
        <button id="sendTestDataBtn">Send Test Data</button>
        <div id="sendResult"></div>
    </div>
    
    <div class="card">
        <h2>Request Log</h2>
        <button id="viewLogBtn">View Request Log</button>
        <pre id="logContent"><?php 
            $logFile = 'backend/esp32_request_log.txt';
            if (file_exists($logFile)) {
                $log = file_get_contents($logFile);
                echo htmlspecialchars($log);
            } else {
                echo "Log file not found";
            }
        ?></pre>
    </div>
    
    <script>
        document.getElementById('testConnectionBtn').addEventListener('click', async function() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = 'Testing connection...';
            
            try {
                const response = await fetch('backend/test_connection.php');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                resultDiv.innerHTML = `<p class="success">Connection successful!</p>
                                      <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">Connection failed: ${error.message}</p>`;
            }
        });
        
        document.getElementById('testDataEndpointBtn').addEventListener('click', async function() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = 'Testing data endpoint...';
            
            try {
                const response = await fetch('backend/receive_data.php');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                resultDiv.innerHTML = `<p class="success">Data endpoint test successful!</p>
                                      <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">Data endpoint test failed: ${error.message}</p>`;
            }
        });
        
        document.getElementById('sendTestDataBtn').addEventListener('click', async function() {
            const resultDiv = document.getElementById('sendResult');
            resultDiv.innerHTML = 'Sending test data...';
            
            const testData = {
                voltage_1: 400 + Math.random() * 10,
                voltage_2: 400 + Math.random() * 10,
                voltage_3: 400 + Math.random() * 10,
                current_1: 10 + Math.random() * 2,
                current_2: 10 + Math.random() * 2,
                current_3: 10 + Math.random() * 2,
                pf_1: 0.9 + Math.random() * 0.1,
                pf_2: 0.9 + Math.random() * 0.1,
                pf_3: 0.9 + Math.random() * 0.1,
                kva_1: 20 + Math.random() * 2,
                kva_2: 20 + Math.random() * 2,
                kva_3: 20 + Math.random() * 2,
                total_kva: 60 + Math.random() * 5,
                total_kw: 55 + Math.random() * 5,
                total_kvar: 25 + Math.random() * 3,
                frequency: 50 + Math.random() * 0.2
            };
            
            try {
                const response = await fetch('backend/receive_data.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                resultDiv.innerHTML = `<p class="success">Test data sent successfully!</p>
                                      <pre>Sent: ${JSON.stringify(testData, null, 2)}</pre>
                                      <pre>Response: ${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">Failed to send test data: ${error.message}</p>`;
            }
        });
        
        document.getElementById('viewLogBtn').addEventListener('click', async function() {
            const logContent = document.getElementById('logContent');
            
            try {
                const response = await fetch('backend/esp32_request_log.txt');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const text = await response.text();
                logContent.textContent = text;
            } catch (error) {
                logContent.textContent = `Error loading log: ${error.message}`;
            }
        });
    </script>
</body>
</html>
