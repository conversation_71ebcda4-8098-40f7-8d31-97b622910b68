<?php
// Test script to send sample data to the backend
// This simulates ESP32 sending data

$serverUrl = "http://localhost/online%20data%20logger/backend/receive_data.php";

// Sample test data (non-zero values)
$testData = [
    "voltage_1" => 230.5,
    "voltage_2" => 231.2,
    "voltage_3" => 229.8,
    "current_1" => 5.1,
    "current_2" => 5.2,
    "current_3" => 5.0,
    "pf_1" => 0.92,
    "pf_2" => 0.93,
    "pf_3" => 0.91,
    "kva_1" => 1150.5,
    "kva_2" => 1151.2,
    "kva_3" => 1149.8,
    "total_kva" => 3450.5,
    "total_kw" => 3277.8,
    "total_kvar" => 1076.5,
    "frequency" => 50.1
];

// Zero values test data
$zeroData = [
    "voltage_1" => 0.0,
    "voltage_2" => 0.0,
    "voltage_3" => 0.0,
    "current_1" => 0.0,
    "current_2" => 0.0,
    "current_3" => 0.0,
    "pf_1" => 0.0,
    "pf_2" => 0.0,
    "pf_3" => 0.0,
    "kva_1" => 0.0,
    "kva_2" => 0.0,
    "kva_3" => 0.0,
    "total_kva" => 0.0,
    "total_kw" => 0.0,
    "total_kvar" => 0.0,
    "frequency" => 0.0
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Data Sender</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Data Sender</h1>
        <p>This tool sends test data to your backend to verify if the dashboard can display values.</p>
        
        <div style="margin: 20px 0;">
            <button class="button" onclick="sendTestData('normal')">Send Normal Test Data</button>
            <button class="button" onclick="sendTestData('zero')">Send Zero Values</button>
            <button class="button" onclick="sendTestData('random')">Send Random Data</button>
            <button class="button" onclick="sendMultiple()">Send 5 Records</button>
        </div>
        
        <div id="results"></div>
        
        <div style="margin-top: 30px;">
            <h3>📊 Quick Links:</h3>
            <a href="backend/get_latest_data.php" target="_blank" class="button">View Latest Data API</a>
            <a href="frontend/advanced_dashboard.php" target="_blank" class="button">Open Dashboard</a>
            <a href="check_data.php" target="_blank" class="button">Check Database</a>
        </div>
        
        <div style="margin-top: 20px;">
            <h4>Test Data Preview:</h4>
            <h5>Normal Data:</h5>
            <pre><?php echo json_encode($testData, JSON_PRETTY_PRINT); ?></pre>
            
            <h5>Zero Data:</h5>
            <pre><?php echo json_encode($zeroData, JSON_PRETTY_PRINT); ?></pre>
        </div>
    </div>

    <script>
        const testData = <?php echo json_encode($testData); ?>;
        const zeroData = <?php echo json_encode($zeroData); ?>;
        
        async function sendTestData(type) {
            const resultsDiv = document.getElementById('results');
            
            let dataToSend;
            switch(type) {
                case 'normal':
                    dataToSend = testData;
                    break;
                case 'zero':
                    dataToSend = zeroData;
                    break;
                case 'random':
                    dataToSend = generateRandomData();
                    break;
            }
            
            resultsDiv.innerHTML += `<div class="info">Sending ${type} data...</div>`;
            
            try {
                const response = await fetch('backend/receive_data.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(dataToSend)
                });
                
                const result = await response.text();
                
                if (response.ok) {
                    resultsDiv.innerHTML += `
                        <div class="success">✅ ${type} data sent successfully!</div>
                        <pre>${result}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML += `
                        <div class="error">❌ Failed to send ${type} data (Status: ${response.status})</div>
                        <pre>${result}</pre>
                    `;
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="error">❌ Error sending ${type} data: ${error.message}</div>
                `;
            }
        }
        
        function generateRandomData() {
            return {
                voltage_1: 220 + Math.random() * 20,
                voltage_2: 220 + Math.random() * 20,
                voltage_3: 220 + Math.random() * 20,
                current_1: Math.random() * 10,
                current_2: Math.random() * 10,
                current_3: Math.random() * 10,
                pf_1: 0.8 + Math.random() * 0.2,
                pf_2: 0.8 + Math.random() * 0.2,
                pf_3: 0.8 + Math.random() * 0.2,
                kva_1: 1000 + Math.random() * 500,
                kva_2: 1000 + Math.random() * 500,
                kva_3: 1000 + Math.random() * 500,
                total_kva: 3000 + Math.random() * 1000,
                total_kw: 2800 + Math.random() * 800,
                total_kvar: 1000 + Math.random() * 500,
                frequency: 49.5 + Math.random() * 1
            };
        }
        
        async function sendMultiple() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `<div class="info">Sending 5 random records...</div>`;
            
            for (let i = 0; i < 5; i++) {
                await sendTestData('random');
                await new Promise(resolve => setTimeout(resolve, 500)); // Wait 500ms between sends
            }
            
            resultsDiv.innerHTML += `<div class="success">✅ Finished sending 5 records!</div>`;
        }
    </script>
</body>
</html>
