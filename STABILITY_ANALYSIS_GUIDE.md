# Electrical System Stability Analysis Guide

## Overview

The Stability Analysis module provides comprehensive analysis of electrical system stability using advanced statistical methods and industry-standard metrics. This tool helps identify potential issues, assess system performance, and provide actionable recommendations for improving electrical system reliability.

## Features

### 1. Comprehensive Analysis Types

#### **Voltage Stability Analysis**
- **Phase Balance Assessment**: Analyzes voltage balance across all three phases
- **Quality Assessment**: Evaluates voltage quality against nominal values (400V)
- **Anomaly Detection**: Identifies overvoltage and undervoltage events using statistical thresholds
- **Deviation Analysis**: Calculates percentage deviation from nominal voltage

#### **Current Stability Analysis**
- **Load Pattern Analysis**: Identifies steady, moderate, or variable load patterns
- **Current Balance**: Assesses current balance across phases
- **Anomaly Detection**: Detects overcurrent and undercurrent conditions
- **Load Factor Calculation**: Measures load efficiency and utilization

#### **Power Stability Analysis**
- **Power Factor Assessment**: Evaluates power factor quality and efficiency impact
- **Total Power Analysis**: Analyzes kW, kVA, and kVAR stability
- **Trend Analysis**: Identifies increasing, decreasing, or stable power trends
- **Efficiency Metrics**: Calculates improvement potential

#### **Frequency Stability Analysis**
- **Deviation Analysis**: Measures frequency deviation from nominal (50Hz)
- **Rate of Change**: Analyzes frequency stability and fluctuations
- **Quality Assessment**: Evaluates frequency quality against industry standards
- **Grid Stability Indicators**: Provides insights into grid connection quality

### 2. Statistical Metrics

#### **Basic Statistics**
- Mean, Median, Mode
- Standard Deviation and Variance
- Min/Max values and Range
- Percentiles (25th, 75th, 95th)
- Coefficient of Variation

#### **Stability Metrics**
- **Stability Score**: 0-100 scale indicating system stability
- **Stability Classification**: Excellent, Good, Fair, Poor, Critical
- **Rate of Change Analysis**: Measures parameter fluctuation rates
- **Anomaly Percentage**: Percentage of readings outside normal range

### 3. Advanced Analysis Features

#### **Phase Balance Analysis**
- Calculates imbalance percentage across phases
- Identifies which phases are over/under loaded
- Provides balance quality rating
- Recommends load redistribution strategies

#### **Load Pattern Recognition**
- **Steady Load**: Low variability (CV < 10%)
- **Moderate Load**: Medium variability (CV 10-20%)
- **Variable Load**: High variability (CV > 20%)
- Peak-to-average ratio calculation

#### **Anomaly Detection**
- Uses statistical thresholds (2-sigma, 2.5-sigma)
- Identifies outliers and unusual patterns
- Categorizes anomaly types and severity
- Provides anomaly frequency analysis

### 4. Visualization Components

#### **Stability Score Dashboard**
- Circular progress indicators for overall stability
- Individual metric scores with color coding
- Grade-based assessment (A+ to F)
- Real-time score updates

#### **Trend Analysis Charts**
- Radar charts for multi-parameter comparison
- Time-series plots for trend visualization
- Phase balance bar charts
- Statistical distribution histograms

#### **Interactive Controls**
- Time range selection (1 hour to 30 days)
- Analysis type filtering
- Real-time parameter updates
- Export capabilities

### 5. Recommendations Engine

#### **Immediate Actions**
- Critical issues requiring immediate attention
- Safety-related recommendations
- Emergency response procedures

#### **Preventive Measures**
- Regular monitoring schedules
- Maintenance recommendations
- System optimization suggestions

#### **Optimization Opportunities**
- Power factor correction strategies
- Load balancing improvements
- Energy efficiency enhancements

## Technical Implementation

### Backend API (`stability_analysis.php`)

#### **Endpoints**
```
GET /backend/stability_analysis.php
Parameters:
- type: comprehensive|voltage|current|power|frequency
- start: Start timestamp (YYYY-MM-DD HH:MM:SS)
- end: End timestamp (YYYY-MM-DD HH:MM:SS)
- window: Analysis window in minutes (default: 60)
```

#### **Response Format**
```json
{
  "voltage_stability": {
    "voltage_1": {
      "statistics": {...},
      "stability": {...},
      "quality_assessment": {...},
      "anomalies": {...}
    },
    "phase_balance": {...}
  },
  "stability_score": {
    "score": 85,
    "grade": "B+",
    "factors": {...}
  },
  "recommendations": {...},
  "metadata": {...}
}
```

### Frontend Implementation

#### **Vite.js Integration**
- Modern ES modules with hot reload
- SCSS preprocessing for advanced styling
- Optimized builds with code splitting
- Chart.js integration for visualizations

#### **Key Components**
- `stability.js`: Main analysis logic
- `stability.scss`: Futuristic UI styling
- `stability_analysis.php`: HTML structure
- Chart components for data visualization

### Database Schema

The analysis uses the existing `electrical_data` table:
```sql
- voltage_1, voltage_2, voltage_3 (FLOAT)
- current_1, current_2, current_3 (FLOAT)
- pf_1, pf_2, pf_3 (FLOAT)
- kva_1, kva_2, kva_3 (FLOAT)
- total_kw, total_kva, total_kvar (FLOAT)
- frequency (FLOAT)
- timestamp (DATETIME)
```

## Usage Instructions

### 1. Accessing Stability Analysis
- Navigate to the main dashboard
- Click "Stability Analysis" in the sidebar
- Or visit `/frontend/stability_analysis.php` directly

### 2. Running Analysis
1. Select analysis type (Comprehensive recommended)
2. Choose time range for analysis
3. Click "Run Analysis" button
4. Review results and recommendations

### 3. Interpreting Results

#### **Stability Scores**
- **90-100**: Excellent stability, no action needed
- **80-89**: Good stability, minor optimizations possible
- **70-79**: Fair stability, preventive measures recommended
- **60-69**: Poor stability, immediate attention required
- **<60**: Critical stability issues, urgent action needed

#### **Quality Ratings**
- **Good**: Within acceptable limits
- **Fair**: Approaching limits, monitoring recommended
- **Poor**: Outside acceptable limits, action required

### 4. Acting on Recommendations
- Review immediate actions for critical issues
- Implement preventive measures for long-term stability
- Consider optimization opportunities for efficiency gains

## Best Practices

### 1. Regular Monitoring
- Run comprehensive analysis weekly
- Monitor critical parameters daily
- Set up automated alerts for anomalies

### 2. Trend Analysis
- Compare results over time
- Identify seasonal patterns
- Track improvement after interventions

### 3. Documentation
- Record analysis results
- Document corrective actions taken
- Maintain historical performance data

## Troubleshooting

### Common Issues
1. **No Data Found**: Check time range and database connectivity
2. **Analysis Timeout**: Reduce time range or increase server timeout
3. **Chart Not Loading**: Verify Chart.js dependencies and canvas elements

### Performance Optimization
- Use appropriate time ranges for analysis
- Consider data sampling for large datasets
- Implement caching for frequently accessed results

## Future Enhancements

### Planned Features
- Machine learning-based anomaly detection
- Predictive maintenance recommendations
- Integration with external monitoring systems
- Advanced harmonic analysis
- Power quality compliance reporting

### API Extensions
- Real-time streaming analysis
- Webhook notifications for critical events
- Integration with SCADA systems
- Mobile app support

## Support and Maintenance

For technical support or feature requests, please refer to the main project documentation or contact the development team.

---

*This stability analysis system provides professional-grade electrical monitoring capabilities suitable for industrial, commercial, and utility applications.*
