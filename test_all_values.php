<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All Values Display - Online Data Logger</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.warning { background: #ffc107; color: #212529; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 200px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; text-align: center; }
        .stat-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 14px; color: #6c757d; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Test All Values Display</h1>
        <p>This tool tests if the dashboard shows ALL values from the database in graphs.</p>
        
        <div class="stats" id="statsContainer">
            <div class="stat-card">
                <div class="stat-value" id="totalRecords">--</div>
                <div class="stat-label">Total Records</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="apiRecords">--</div>
                <div class="stat-label">API Returns</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="timeRange">--</div>
                <div class="stat-label">Time Range</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="dataTypes">--</div>
                <div class="stat-label">Data Types</div>
            </div>
        </div>
        
        <div style="margin: 20px 0;">
            <button class="button" onclick="generateTestData()">Generate Test Data (50 records)</button>
            <button class="button success" onclick="generateVariedData()">Generate Varied Data (100 records)</button>
            <button class="button warning" onclick="generateZeroMixedData()">Generate Zero/Mixed Data</button>
            <button class="button" onclick="checkDatabaseStats()">Check Database Stats</button>
            <button class="button" onclick="testAPIResponse()">Test API Response</button>
        </div>
        
        <div id="results"></div>
        
        <div style="margin: 20px 0;">
            <h3>🎯 Dashboard Test</h3>
            <p>After generating test data:</p>
            <ol>
                <li><strong>Open Dashboard:</strong> <a href="frontend/advanced_dashboard.php" target="_blank" class="button">Open Dashboard</a></li>
                <li><strong>Check Graphs:</strong> Should show ALL data points, not just recent ones</li>
                <li><strong>Check Console:</strong> Press F12 and look for "Total data points" messages</li>
                <li><strong>Verify Time Range:</strong> Graphs should show data across the full time range</li>
            </ol>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>✅ Expected Behavior</h3>
            <ul>
                <li><strong>All Values Displayed:</strong> Every record in database should appear on graphs</li>
                <li><strong>No Data Limiting:</strong> Graphs should not remove old data points</li>
                <li><strong>Complete Time Range:</strong> X-axis should show full time span of data</li>
                <li><strong>Zero Values Included:</strong> Zero values should be plotted as flat lines</li>
                <li><strong>Mixed Data Handling:</strong> Both zero and non-zero values should be visible</li>
            </ul>
        </div>
    </div>

    <script>
        async function generateTestData() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Generating 50 test records...</div>';
            
            try {
                const promises = [];
                const now = Date.now();
                
                for (let i = 0; i < 50; i++) {
                    // Create data points spread over last 2 hours
                    const timestamp = new Date(now - (i * 2 * 60 * 1000)); // Every 2 minutes
                    
                    const testData = {
                        voltage_1: 220 + Math.sin(i * 0.1) * 10,
                        voltage_2: 225 + Math.cos(i * 0.1) * 8,
                        voltage_3: 230 + Math.sin(i * 0.15) * 12,
                        current_1: 5 + Math.sin(i * 0.2) * 2,
                        current_2: 6 + Math.cos(i * 0.2) * 1.5,
                        current_3: 5.5 + Math.sin(i * 0.25) * 2.5,
                        pf_1: 0.85 + Math.sin(i * 0.1) * 0.1,
                        pf_2: 0.90 + Math.cos(i * 0.1) * 0.08,
                        pf_3: 0.88 + Math.sin(i * 0.12) * 0.09,
                        kva_1: 1100 + Math.sin(i * 0.1) * 100,
                        kva_2: 1200 + Math.cos(i * 0.1) * 80,
                        kva_3: 1150 + Math.sin(i * 0.15) * 120,
                        total_kva: 3450 + Math.sin(i * 0.1) * 200,
                        total_kw: 3200 + Math.cos(i * 0.1) * 150,
                        total_kvar: 1000 + Math.sin(i * 0.1) * 100,
                        frequency: 50 + Math.sin(i * 0.3) * 0.5
                    };
                    
                    promises.push(sendSingleRecord(testData));
                    
                    // Add small delay to avoid overwhelming server
                    if (i % 10 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }
                
                await Promise.all(promises);
                
                resultsDiv.innerHTML = `
                    <div class="success">✅ Generated 50 test records successfully!</div>
                    <div class="info">Records span 2 hours with varied electrical values</div>
                `;
                
                // Update stats
                setTimeout(checkDatabaseStats, 1000);
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Failed to generate test data: ${error.message}</div>
                `;
            }
        }
        
        async function generateVariedData() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Generating 100 varied records...</div>';
            
            try {
                const promises = [];
                const now = Date.now();
                
                for (let i = 0; i < 100; i++) {
                    // Create data points spread over last 4 hours
                    const timestamp = new Date(now - (i * 2.4 * 60 * 1000)); // Every 2.4 minutes
                    
                    // Create more varied data including some zeros
                    const testData = {
                        voltage_1: i % 10 === 0 ? 0 : 220 + Math.random() * 20,
                        voltage_2: i % 15 === 0 ? 0 : 225 + Math.random() * 15,
                        voltage_3: i % 20 === 0 ? 0 : 230 + Math.random() * 18,
                        current_1: i % 8 === 0 ? 0 : 3 + Math.random() * 4,
                        current_2: i % 12 === 0 ? 0 : 4 + Math.random() * 3,
                        current_3: i % 18 === 0 ? 0 : 3.5 + Math.random() * 3.5,
                        pf_1: i % 25 === 0 ? 0 : 0.8 + Math.random() * 0.2,
                        pf_2: i % 30 === 0 ? 0 : 0.85 + Math.random() * 0.15,
                        pf_3: i % 35 === 0 ? 0 : 0.82 + Math.random() * 0.18,
                        kva_1: i % 10 === 0 ? 0 : 1000 + Math.random() * 300,
                        kva_2: i % 15 === 0 ? 0 : 1100 + Math.random() * 250,
                        kva_3: i % 20 === 0 ? 0 : 1050 + Math.random() * 350,
                        total_kva: 3000 + Math.random() * 800,
                        total_kw: 2800 + Math.random() * 600,
                        total_kvar: 800 + Math.random() * 400,
                        frequency: i % 50 === 0 ? 0 : 49.5 + Math.random() * 1
                    };
                    
                    promises.push(sendSingleRecord(testData));
                    
                    // Add small delay
                    if (i % 10 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 50));
                    }
                }
                
                await Promise.all(promises);
                
                resultsDiv.innerHTML = `
                    <div class="success">✅ Generated 100 varied records successfully!</div>
                    <div class="info">Records include zeros, normal values, and varied patterns</div>
                `;
                
                // Update stats
                setTimeout(checkDatabaseStats, 1000);
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Failed to generate varied data: ${error.message}</div>
                `;
            }
        }
        
        async function generateZeroMixedData() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Generating zero/mixed data...</div>';
            
            try {
                const dataTypes = [
                    // All zeros
                    { voltage_1: 0, voltage_2: 0, voltage_3: 0, current_1: 0, current_2: 0, current_3: 0, pf_1: 0, pf_2: 0, pf_3: 0, kva_1: 0, kva_2: 0, kva_3: 0, total_kva: 0, total_kw: 0, total_kvar: 0, frequency: 0 },
                    // Mixed zeros and values
                    { voltage_1: 230, voltage_2: 0, voltage_3: 225, current_1: 0, current_2: 5, current_3: 0, pf_1: 0.9, pf_2: 0, pf_3: 0.85, kva_1: 1150, kva_2: 0, kva_3: 1100, total_kva: 2250, total_kw: 2000, total_kvar: 500, frequency: 50 },
                    // Normal values
                    { voltage_1: 230, voltage_2: 231, voltage_3: 229, current_1: 5, current_2: 5.2, current_3: 4.8, pf_1: 0.92, pf_2: 0.91, pf_3: 0.93, kva_1: 1150, kva_2: 1200, kva_3: 1100, total_kva: 3450, total_kw: 3200, total_kvar: 1000, frequency: 50.1 }
                ];
                
                const promises = [];
                const now = Date.now();
                
                for (let i = 0; i < 30; i++) {
                    const dataType = dataTypes[i % dataTypes.length];
                    promises.push(sendSingleRecord(dataType));
                    
                    if (i % 5 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }
                
                await Promise.all(promises);
                
                resultsDiv.innerHTML = `
                    <div class="success">✅ Generated 30 zero/mixed records successfully!</div>
                    <div class="info">Mix of all zeros, partial zeros, and normal values</div>
                `;
                
                // Update stats
                setTimeout(checkDatabaseStats, 1000);
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Failed to generate zero/mixed data: ${error.message}</div>
                `;
            }
        }
        
        async function sendSingleRecord(data) {
            const response = await fetch('backend/receive_data.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            return response.json();
        }
        
        async function checkDatabaseStats() {
            try {
                const response = await fetch('backend/get_latest_data.php?records=10000');
                const data = await response.json();
                
                if (Array.isArray(data) && data.length > 0) {
                    const totalRecords = data.length;
                    const oldestRecord = data[data.length - 1];
                    const newestRecord = data[0];
                    
                    const timeRange = Math.round((new Date(newestRecord.timestamp) - new Date(oldestRecord.timestamp)) / (1000 * 60));
                    
                    // Count different data types
                    let zeroCount = 0;
                    let normalCount = 0;
                    let mixedCount = 0;
                    
                    data.forEach(record => {
                        const hasZeros = record.voltage_1 == 0 || record.current_1 == 0 || record.frequency == 0;
                        const hasValues = record.voltage_1 > 0 || record.current_1 > 0 || record.frequency > 0;
                        
                        if (hasZeros && hasValues) mixedCount++;
                        else if (hasZeros) zeroCount++;
                        else normalCount++;
                    });
                    
                    document.getElementById('totalRecords').textContent = totalRecords;
                    document.getElementById('apiRecords').textContent = totalRecords;
                    document.getElementById('timeRange').textContent = timeRange + ' min';
                    document.getElementById('dataTypes').textContent = `${normalCount}N ${zeroCount}Z ${mixedCount}M`;
                    
                    document.getElementById('results').innerHTML = `
                        <div class="success">✅ Database Stats Updated</div>
                        <div class="info">
                            <strong>Total Records:</strong> ${totalRecords}<br>
                            <strong>Time Range:</strong> ${timeRange} minutes<br>
                            <strong>Normal Values:</strong> ${normalCount}<br>
                            <strong>Zero Values:</strong> ${zeroCount}<br>
                            <strong>Mixed Values:</strong> ${mixedCount}<br>
                            <strong>Oldest Record:</strong> ${oldestRecord.timestamp}<br>
                            <strong>Newest Record:</strong> ${newestRecord.timestamp}
                        </div>
                    `;
                } else {
                    document.getElementById('totalRecords').textContent = '0';
                    document.getElementById('apiRecords').textContent = '0';
                    document.getElementById('timeRange').textContent = '0 min';
                    document.getElementById('dataTypes').textContent = 'None';
                }
                
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <div class="error">❌ Failed to check database stats: ${error.message}</div>
                `;
            }
        }
        
        async function testAPIResponse() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Testing API response...</div>';
            
            try {
                const response = await fetch('backend/get_latest_data.php?records=1000');
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <div class="success">✅ API Response Test</div>
                    <div class="info">
                        <strong>Records Returned:</strong> ${Array.isArray(data) ? data.length : 1}<br>
                        <strong>Response Type:</strong> ${Array.isArray(data) ? 'Array' : 'Object'}<br>
                        <strong>First Record:</strong> ${data[0] ? 'Available' : 'None'}
                    </div>
                    <details>
                        <summary>Raw API Response (first 3 records)</summary>
                        <pre>${JSON.stringify(Array.isArray(data) ? data.slice(0, 3) : data, null, 2)}</pre>
                    </details>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ API test failed: ${error.message}</div>
                `;
            }
        }
        
        // Auto-update stats on page load
        checkDatabaseStats();
    </script>
</body>
</html>
