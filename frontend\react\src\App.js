import React, { useState, useEffect, lazy, Suspense } from 'react';
import styled from 'styled-components';
import Sidebar from './components/layout/Sidebar';
import MainContent from './components/layout/MainContent';
import NotificationContainer from './components/ui/NotificationContainer';
import StatusIndicator from './components/ui/StatusIndicator';
import LoadingSpinner from './components/ui/LoadingSpinner';
import ErrorBoundary from './components/error/ErrorBoundary';
import { useTheme } from './contexts/ThemeContext';
import { DashboardProvider } from './contexts/DashboardContext';
import { AnalyticsProvider } from './contexts/AnalyticsContext';

// Lazy-loaded components for better performance
const FullscreenWidget = lazy(() => import('./components/widgets/FullscreenWidget'));
const SettingsModal = lazy(() => import('./components/modals/SettingsModal'));
const ExportModal = lazy(() => import('./components/modals/ExportModal'));
const CustomizeModal = lazy(() => import('./components/modals/CustomizeModal'));
const AnalyticsModal = lazy(() => import('./components/modals/AnalyticsModal'));
const ThemeBuilderModal = lazy(() => import('./components/modals/ThemeBuilderModal'));

// Dashboard settings with defaults
const defaultSettings = {
  refreshRate: 2, // seconds
  timeWindow: 1, // minutes
  decimalPlaces: 3,
  chartTheme: 'professional',
  lineThickness: 0.5,
  showGridLines: true,
  voltageAlertHigh: 450,
  voltageAlertLow: 350,
  frequencyAlertHigh: 55,
  frequencyAlertLow: 45
};

const App = () => {
  const { darkMode } = useTheme();
  const [settings, setSettings] = useState(defaultSettings);
  const [notifications, setNotifications] = useState([]);
  const [status, setStatus] = useState({ message: '', type: '' });
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isCustomizeModalOpen, setIsCustomizeModalOpen] = useState(false);
  const [isAnalyticsModalOpen, setIsAnalyticsModalOpen] = useState(false);
  const [isThemeBuilderModalOpen, setIsThemeBuilderModalOpen] = useState(false);
  const [fullscreenWidget, setFullscreenWidget] = useState({
    isOpen: false,
    title: '',
    icon: '',
    chartId: '',
    sourceChartId: ''
  });
  const [isMobileView, setIsMobileView] = useState(window.innerWidth < 768);

  // Add notification
  const addNotification = (message, type = 'info') => {
    const id = Date.now();
    setNotifications(prev => [...prev, { id, message, type }]);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      removeNotification(id);
    }, 5000);
  };

  // Remove notification
  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // Update status
  const updateStatus = (message, type = 'info') => {
    setStatus({ message, type });

    // Only show status for warnings and errors
    if (type === 'warning' || type === 'error') {
      // Auto-hide after 5 seconds
      setTimeout(() => {
        setStatus({ message: '', type: '' });
      }, 5000);
    }
  };

  // Save settings
  const saveSettings = (newSettings) => {
    setSettings(newSettings);
    addNotification('Settings saved successfully', 'success');
  };

  // Reset settings
  const resetSettings = () => {
    setSettings(defaultSettings);
    addNotification('Settings reset to defaults', 'info');
  };

  // Toggle fullscreen widget
  const toggleFullscreenWidget = (isOpen, title = '', icon = '', chartId = '', sourceChartId = '') => {
    setFullscreenWidget({ isOpen, title, icon, chartId, sourceChartId });
  };

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);

    // Show initial notification on mount
    addNotification('Dashboard initialized', 'info');

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Function to get state for error recovery
  const getStateToSave = () => {
    return {
      settings,
      darkMode: useTheme().darkMode
    };
  };

  return (
    <ErrorBoundary
      message="An unexpected error occurred in the application."
      showDetails={true}
      getStateToSave={getStateToSave}
      onRetry={() => window.location.reload()}
    >
      <DashboardProvider>
        <AnalyticsProvider>
          <AppContainer className={isMobileView ? 'mobile-view' : ''}>
            <ErrorBoundary
              message="Navigation sidebar could not be loaded."
              fallback={
                <div className="sidebar-error-fallback">
                  <h3>Navigation Unavailable</h3>
                  <p>Please reload the page to restore navigation.</p>
                </div>
              }
            >
              <Sidebar
                onSettingsClick={() => setIsSettingsModalOpen(true)}
                onCustomizeClick={() => setIsCustomizeModalOpen(true)}
                onAnalyticsClick={() => setIsAnalyticsModalOpen(true)}
                onThemeBuilderClick={() => setIsThemeBuilderModalOpen(true)}
                isMobileView={isMobileView}
              />
            </ErrorBoundary>

            <ErrorBoundary
              message="Dashboard content could not be loaded."
              fallback={
                <div className="main-content-error-fallback">
                  <h3>Dashboard Unavailable</h3>
                  <p>We're having trouble loading the dashboard content. Please try again later.</p>
                  <button onClick={() => window.location.reload()}>Reload Dashboard</button>
                </div>
              }
            >
              <MainContent
                settings={settings}
                onExportClick={() => setIsExportModalOpen(true)}
                addNotification={addNotification}
                updateStatus={updateStatus}
                toggleFullscreenWidget={toggleFullscreenWidget}
                isMobileView={isMobileView}
              />
            </ErrorBoundary>

            <NotificationContainer
              notifications={notifications}
              removeNotification={removeNotification}
            />

            <StatusIndicator
              message={status.message}
              type={status.type}
            />

            <Suspense fallback={<LoadingSpinner />}>
              {fullscreenWidget.isOpen && (
                <ErrorBoundary message="Could not display the fullscreen widget.">
                  <FullscreenWidget
                    isOpen={fullscreenWidget.isOpen}
                    title={fullscreenWidget.title}
                    icon={fullscreenWidget.icon}
                    chartId={fullscreenWidget.chartId}
                    sourceChartId={fullscreenWidget.sourceChartId}
                    onClose={() => toggleFullscreenWidget(false)}
                  />
                </ErrorBoundary>
              )}

              {isSettingsModalOpen && (
                <ErrorBoundary message="Could not load settings.">
                  <SettingsModal
                    isOpen={isSettingsModalOpen}
                    onClose={() => setIsSettingsModalOpen(false)}
                    settings={settings}
                    saveSettings={saveSettings}
                    resetSettings={resetSettings}
                  />
                </ErrorBoundary>
              )}

              {isExportModalOpen && (
                <ErrorBoundary message="Could not load export functionality.">
                  <ExportModal
                    isOpen={isExportModalOpen}
                    onClose={() => setIsExportModalOpen(false)}
                    addNotification={addNotification}
                  />
                </ErrorBoundary>
              )}

              {isCustomizeModalOpen && (
                <ErrorBoundary message="Could not load customization options.">
                  <CustomizeModal
                    isOpen={isCustomizeModalOpen}
                    onClose={() => setIsCustomizeModalOpen(false)}
                    addNotification={addNotification}
                  />
                </ErrorBoundary>
              )}

              {isAnalyticsModalOpen && (
                <ErrorBoundary message="Could not load analytics options.">
                  <AnalyticsModal
                    isOpen={isAnalyticsModalOpen}
                    onClose={() => setIsAnalyticsModalOpen(false)}
                    addNotification={addNotification}
                  />
                </ErrorBoundary>
              )}

              {isThemeBuilderModalOpen && (
                <ErrorBoundary message="Could not load theme builder.">
                  <ThemeBuilderModal
                    isOpen={isThemeBuilderModalOpen}
                    onClose={() => setIsThemeBuilderModalOpen(false)}
                    addNotification={addNotification}
                  />
                </ErrorBoundary>
              )}
            </Suspense>
          </AppContainer>
        </AnalyticsProvider>
      </DashboardProvider>
    </ErrorBoundary>
  );
};

const AppContainer = styled.div`
  display: flex;
  min-height: 100vh;
  max-width: var(--content-width);
  margin: 0 auto;
  position: relative;
  transition: all var(--animation-medium) ease;

  &.mobile-view {
    flex-direction: column;

    /* Mobile-specific styles */
    .sidebar-collapsed {
      width: 100%;
      height: auto;
      position: relative;
      z-index: 100;
    }

    .main-content {
      margin-left: 0;
      max-width: 100%;
      padding: 1rem;
    }
  }
`;

export default App;
