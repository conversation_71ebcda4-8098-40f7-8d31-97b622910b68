<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Path Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>API Path Test</h1>
    <p>Testing different API paths from frontend folder...</p>
    
    <button onclick="testPath('../backend/get_latest_data.php')">Test ../backend/get_latest_data.php</button>
    <button onclick="testPath('backend/get_latest_data.php')">Test backend/get_latest_data.php</button>
    <button onclick="testPath('/online data logger/backend/get_latest_data.php')">Test absolute path</button>
    
    <div id="results"></div>

    <script>
        async function testPath(path) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `<div class="info">Testing: ${path}</div>`;
            
            try {
                const response = await fetch(path);
                const data = await response.text();
                
                if (response.ok) {
                    resultsDiv.innerHTML += `
                        <div class="success">✅ ${path} - Status: ${response.status}</div>
                        <pre>${data.substring(0, 500)}${data.length > 500 ? '...' : ''}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML += `
                        <div class="error">❌ ${path} - Status: ${response.status}</div>
                        <pre>${data}</pre>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="error">❌ ${path} - Error: ${error.message}</div>
                `;
            }
        }
    </script>
</body>
</html>
