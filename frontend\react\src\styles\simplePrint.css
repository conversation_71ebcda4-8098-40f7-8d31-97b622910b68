/**
 * Simple print styles for the dashboard
 */

@media print {
  /* General page setup */
  @page {
    size: landscape;
    margin: 1cm;
  }
  
  body {
    background-color: white !important;
    font-size: 12pt;
    line-height: 1.3;
  }
  
  /* Hide non-printable elements */
  button,
  .no-print {
    display: none !important;
  }
  
  /* Add print header and footer */
  .print-header {
    display: block !important;
    text-align: center;
    margin-bottom: 1cm;
    border-bottom: 1pt solid #ddd;
    padding-bottom: 0.5cm;
  }
  
  .print-header h1 {
    font-size: 18pt;
    margin: 0;
  }
  
  .print-header p {
    font-size: 12pt;
    color: #666;
    margin: 0.2cm 0 0;
  }
  
  .print-footer {
    display: block !important;
    text-align: center;
    margin-top: 1cm;
    border-top: 1pt solid #ddd;
    padding-top: 0.5cm;
    font-size: 10pt;
    color: #666;
  }
  
  /* Print-specific elements */
  .print-only {
    display: block !important;
  }
  
  /* Force background colors to print */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  /* Table styling for print */
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1cm;
  }
  
  th, td {
    border: 1px solid #ddd;
    padding: 0.2cm;
    text-align: left;
  }
  
  th {
    background-color: #f5f5f5;
    font-weight: bold;
  }
  
  /* Print timestamp */
  .print-timestamp {
    display: block !important;
    text-align: right;
    font-size: 10pt;
    color: #666;
    margin-top: 0.2cm;
  }
}
