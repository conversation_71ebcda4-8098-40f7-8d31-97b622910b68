/* Fallback styles for when Vite assets are not available */

:root {
    /* Light mode color palette */
    --futuristic-bg: #f8fafc;
    --futuristic-card-bg: rgba(255, 255, 255, 0.9);
    --futuristic-accent-1: #0ea5e9;
    --futuristic-accent-2: #3b82f6;
    --futuristic-accent-3: #8b5cf6;
    --futuristic-text: #1e293b;
    --futuristic-text-secondary: #64748b;
    --futuristic-border: rgba(148, 163, 184, 0.3);
    --futuristic-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --futuristic-glow: 0 0 15px rgba(14, 165, 233, 0.3);
    --futuristic-gradient: linear-gradient(135deg, #0ea5e9, #3b82f6, #8b5cf6);
    --futuristic-gradient-hover: linear-gradient(135deg, #0284c7, #2563eb, #7c3aed);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--futuristic-bg);
    color: var(--futuristic-text);
    line-height: 1.5;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Animated background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.03) 0%, rgba(15, 23, 42, 0) 50%);
    z-index: -1;
    animation: pulse-bg 15s infinite alternate;
}

@keyframes pulse-bg {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Dashboard container */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: linear-gradient(180deg, var(--futuristic-bg) 0%, #1e293b 100%);
    border-right: 1px solid var(--futuristic-border);
    box-shadow: var(--futuristic-shadow);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1030;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--futuristic-border);
    display: flex;
    justify-content: center;
    align-items: center;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--futuristic-text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
    background: rgba(6, 182, 212, 0.1);
    color: var(--futuristic-accent-1);
    border-left-color: var(--futuristic-accent-1);
}

.sidebar-nav li.active a {
    background: rgba(6, 182, 212, 0.15);
    color: var(--futuristic-accent-1);
    border-left-color: var(--futuristic-accent-1);
    box-shadow: inset 0 0 10px rgba(6, 182, 212, 0.1);
}

.sidebar-nav .material-icons-round {
    margin-right: 12px;
    font-size: 20px;
    color: var(--futuristic-accent-1);
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--futuristic-border);
}

.system-status {
    display: flex;
    align-items: center;
    margin: 15px 0;
    color: var(--futuristic-text-secondary);
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.online {
    background: var(--futuristic-accent-1);
    box-shadow: 0 0 10px var(--futuristic-accent-1);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(6, 182, 212, 0.7); }
    70% { box-shadow: 0 0 0 5px rgba(6, 182, 212, 0); }
    100% { box-shadow: 0 0 0 0 rgba(6, 182, 212, 0); }
}

.logout-button {
    display: flex;
    align-items: center;
    padding: 10px;
    color: var(--futuristic-text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.logout-button:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.logout-button .material-icons-round {
    margin-right: 8px;
    font-size: 18px;
}

/* Main content */
.main-content {
    flex: 1;
    padding: 20px;
    margin-left: 250px;
    width: calc(100% - 250px);
}

/* Status header */
.status-header {
    background: rgba(30, 41, 59, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--futuristic-shadow);
    border: 1px solid var(--futuristic-border);
}

.status-title {
    display: flex;
    align-items: center;
}

.header-logo-container {
    margin-right: 15px;
}

.header-logo {
    height: 40px;
}

.subtitle {
    font-size: 1.25rem;
    color: var(--futuristic-text-secondary);
    font-weight: 400;
}

.status-boxes {
    display: flex;
    gap: 15px;
}

.status-box {
    background: rgba(15, 23, 42, 0.7);
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    min-width: 180px;
    border: 1px solid var(--futuristic-border);
    transition: all 0.3s ease;
}

.status-box:hover {
    box-shadow: var(--futuristic-glow);
    transform: translateY(-2px);
}

.status-icon {
    background: rgba(6, 182, 212, 0.15);
    color: var(--futuristic-accent-1);
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.status-icon .material-icons-round {
    font-size: 24px;
}

.status-text {
    flex: 1;
}

.status-label {
    font-size: 0.75rem;
    color: var(--futuristic-text-secondary);
    margin-bottom: 2px;
}

.status-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--futuristic-text);
}

/* Containers */
.history-container,
.stability-analysis-container {
    background: rgba(30, 41, 59, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: var(--futuristic-shadow);
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid var(--futuristic-border);
    transition: all 0.3s ease;
}

.history-container:hover,
.stability-analysis-container:hover {
    box-shadow: var(--futuristic-glow);
}

/* Buttons */
.button {
    padding: 12px 18px;
    border: none;
    border-radius: 8px;
    background: var(--futuristic-gradient);
    color: white;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.button:hover {
    background: var(--futuristic-gradient-hover);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.button-primary {
    background: linear-gradient(135deg, #06b6d4, #0ea5e9);
}

.button-primary:hover {
    background: linear-gradient(135deg, #0891b2, #0284c7);
}

/* Loading indicator */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--futuristic-text-secondary);
}

.loading-spinner::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 3px solid rgba(6, 182, 212, 0.3);
    border-radius: 50%;
    border-top-color: var(--futuristic-accent-1);
    animation: spin 1s ease-in-out infinite;
    margin-left: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Messages */
.message {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 12px;
    background: rgba(15, 23, 42, 0.7);
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--futuristic-border);
}

.message.error {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: #ef4444;
    color: #fca5a5;
}

.message.success {
    background: rgba(16, 185, 129, 0.1);
    border-left-color: #10b981;
    color: #6ee7b7;
}

.message.warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: #f59e0b;
    color: #fcd34d;
}

.message.info {
    background: rgba(59, 130, 246, 0.1);
    border-left-color: #3b82f6;
    color: #93c5fd;
}

/* Responsive */
@media (max-width: 992px) {
    .sidebar {
        width: 70px;
        overflow: hidden;
    }
    
    .main-content {
        margin-left: 70px;
        width: calc(100% - 70px);
    }
    
    .sidebar-nav a span:not(.material-icons-round) {
        display: none;
    }
}

@media (max-width: 768px) {
    .status-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .status-boxes {
        margin-top: 15px;
        width: 100%;
        flex-wrap: wrap;
    }
}
