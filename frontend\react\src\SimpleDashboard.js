import React, { useState, useEffect, useCallback } from 'react';
import ReactDOM from 'react-dom/client';
import { fetchWithCache, clearCacheItem } from './utils/simpleCache';
import SimpleErrorBoundary from './SimpleErrorBoundary';
import './styles/simplePrint.css';

// Simple Dashboard Component
const SimpleDashboard = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Handle print action
  const handlePrint = () => {
    // Add print-specific elements to the DOM
    addPrintElements();

    // Print the page
    window.print();

    // Remove print-specific elements after printing
    setTimeout(removePrintElements, 1000);
  };

  // Add print-specific elements to the DOM
  const addPrintElements = () => {
    // Remove any existing print elements
    removePrintElements();

    // Create container for print elements
    const printContainer = document.createElement('div');
    printContainer.id = 'print-elements';
    printContainer.className = 'print-only';
    document.body.appendChild(printContainer);

    // Add header
    const header = document.createElement('div');
    header.className = 'print-header';

    const headerTitle = document.createElement('h1');
    headerTitle.textContent = 'Power Monitoring Dashboard';
    header.appendChild(headerTitle);

    const headerSubtitle = document.createElement('p');
    headerSubtitle.textContent = 'Power Monitoring System';
    header.appendChild(headerSubtitle);

    printContainer.appendChild(header);

    // Add timestamp
    const timestamp = document.createElement('div');
    timestamp.className = 'print-timestamp';

    const now = new Date();
    timestamp.textContent = `Generated on: ${now.toLocaleDateString()} at ${now.toLocaleTimeString()}`;

    printContainer.appendChild(timestamp);

    // Add footer
    const footer = document.createElement('div');
    footer.className = 'print-footer';
    footer.textContent = `© ${new Date().getFullYear()} PowerMonitor Pro - Confidential`;

    printContainer.appendChild(footer);
  };

  // Remove print-specific elements from the DOM
  const removePrintElements = () => {
    const printElements = document.getElementById('print-elements');
    if (printElements) {
      printElements.remove();
    }
  };

  // Fetch data from the backend with caching
  const fetchData = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);

      // If force refresh, clear the cache for this endpoint
      if (forceRefresh) {
        clearCacheItem('../backend/get_latest_data.php');
      }

      // Fetch data with caching (10 second TTL for live data)
      const jsonData = await fetchWithCache(
        '../backend/get_latest_data.php',
        { records: 20 },
        10000 // 10 seconds cache
      );

      setData(jsonData);
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to fetch data. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch data on component mount and set up interval
  useEffect(() => {
    // Fetch data immediately
    fetchData();

    // Set up interval for regular updates
    const interval = setInterval(() => fetchData(), 5000); // Update every 5 seconds

    // Clean up on unmount
    return () => clearInterval(interval);
  }, [fetchData]);

  return (
    <div style={{
      padding: '2rem',
      maxWidth: '1200px',
      margin: '0 auto',
      fontFamily: 'Inter, system-ui, sans-serif'
    }}>
      <header style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <h1 style={{ color: '#2563eb', margin: 0 }}>Power Monitoring Dashboard</h1>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '1rem'
        }}>
          <button
            onClick={handlePrint}
            className="no-print"
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: 'white',
              color: '#2563eb',
              border: '1px solid #e2e8f0',
              borderRadius: '0.25rem',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              fontSize: '0.875rem'
            }}
          >
            🖨️ Print Dashboard
          </button>
          <div style={{ color: '#64748b', fontSize: '0.875rem' }}>
            {lastUpdated ? `Last updated: ${lastUpdated.toLocaleTimeString()}` : 'Updating...'}
          </div>
        </div>
      </header>

      {loading && !data && (
        <div style={{
          padding: '2rem',
          textAlign: 'center',
          backgroundColor: '#f8fafc',
          borderRadius: '0.5rem',
          border: '1px solid #e2e8f0'
        }}>
          <div style={{ fontSize: '1.25rem', color: '#64748b' }}>Loading data...</div>
        </div>
      )}

      {error && (
        <div style={{
          padding: '2rem',
          textAlign: 'center',
          backgroundColor: '#fef2f2',
          borderRadius: '0.5rem',
          border: '1px solid #fee2e2',
          color: '#b91c1c'
        }}>
          <div style={{ fontSize: '1.25rem', marginBottom: '0.5rem' }}>Error</div>
          <div>{error}</div>
          <button
            onClick={() => fetchData(true)} // Force refresh on retry
            style={{
              marginTop: '1rem',
              padding: '0.5rem 1rem',
              backgroundColor: '#2563eb',
              color: 'white',
              border: 'none',
              borderRadius: '0.25rem',
              cursor: 'pointer'
            }}
          >
            Retry
          </button>
        </div>
      )}

      {data && (
        <div>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
            gap: '1rem',
            marginBottom: '2rem'
          }}>
            <SummaryCard title="Voltage (Avg)" value={calculateAverage([data[0].voltage_1, data[0].voltage_2, data[0].voltage_3])} unit="V" />
            <SummaryCard title="Current (Total)" value={calculateSum([data[0].current_1, data[0].current_2, data[0].current_3])} unit="A" />
            <SummaryCard title="Power Factor (Avg)" value={calculateAverage([data[0].pf_1, data[0].pf_2, data[0].pf_3])} unit="" />
            <SummaryCard title="Frequency" value={data[0].frequency} unit="Hz" />
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(500px, 1fr))',
            gap: '1.5rem'
          }}>
            <DataTable
              title="Voltage Readings"
              headers={['Phase', 'Value (V)']}
              data={[
                { label: 'Phase 1', value: data[0].voltage_1 },
                { label: 'Phase 2', value: data[0].voltage_2 },
                { label: 'Phase 3', value: data[0].voltage_3 }
              ]}
            />

            <DataTable
              title="Current Readings"
              headers={['Phase', 'Value (A)']}
              data={[
                { label: 'Phase 1', value: data[0].current_1 },
                { label: 'Phase 2', value: data[0].current_2 },
                { label: 'Phase 3', value: data[0].current_3 }
              ]}
            />

            <DataTable
              title="Power Factor"
              headers={['Phase', 'Value']}
              data={[
                { label: 'Phase 1', value: data[0].pf_1 },
                { label: 'Phase 2', value: data[0].pf_2 },
                { label: 'Phase 3', value: data[0].pf_3 }
              ]}
            />

            <DataTable
              title="Power Readings"
              headers={['Type', 'Value']}
              data={[
                { label: 'Total KW', value: data[0].total_kw },
                { label: 'Total KVA', value: data[0].total_kva },
                { label: 'Total KVAR', value: data[0].total_kvar }
              ]}
            />
          </div>

          <div style={{
            marginTop: '2rem',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <button
              onClick={() => fetchData(true)} // Force refresh
              className="no-print"
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#2563eb',
                color: 'white',
                border: 'none',
                borderRadius: '0.25rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <span>Refresh Data</span>
            </button>

            <div style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Timestamp: {data[0].timestamp}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper component for summary cards
const SummaryCard = ({ title, value, unit }) => {
  return (
    <div style={{
      padding: '1.5rem',
      backgroundColor: '#f8fafc',
      borderRadius: '0.5rem',
      border: '1px solid #e2e8f0'
    }}>
      <div style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.5rem' }}>
        {title}
      </div>
      <div style={{
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#0f172a',
        display: 'flex',
        alignItems: 'baseline',
        gap: '0.25rem'
      }}>
        {formatNumber(value)}
        {unit && <span style={{ fontSize: '0.875rem', color: '#64748b' }}>{unit}</span>}
      </div>
    </div>
  );
};

// Helper component for data tables
const DataTable = ({ title, headers, data }) => {
  return (
    <div style={{
      backgroundColor: '#f8fafc',
      borderRadius: '0.5rem',
      border: '1px solid #e2e8f0',
      overflow: 'hidden'
    }}>
      <div style={{
        padding: '1rem',
        borderBottom: '1px solid #e2e8f0',
        fontWeight: '600',
        color: '#0f172a'
      }}>
        {title}
      </div>
      <table style={{
        width: '100%',
        borderCollapse: 'collapse'
      }}>
        <thead>
          <tr>
            {headers.map((header, index) => (
              <th key={index} style={{
                padding: '0.75rem 1rem',
                textAlign: 'left',
                borderBottom: '1px solid #e2e8f0',
                color: '#64748b',
                fontWeight: '500',
                fontSize: '0.875rem'
              }}>
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr key={index}>
              <td style={{
                padding: '0.75rem 1rem',
                borderBottom: index < data.length - 1 ? '1px solid #e2e8f0' : 'none',
                color: '#0f172a'
              }}>
                {row.label}
              </td>
              <td style={{
                padding: '0.75rem 1rem',
                borderBottom: index < data.length - 1 ? '1px solid #e2e8f0' : 'none',
                color: '#0f172a',
                fontWeight: '500'
              }}>
                {formatNumber(row.value)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Helper function to format numbers
const formatNumber = (value) => {
  if (value === null || value === undefined) return 'N/A';
  return Number(value).toFixed(3);
};

// Helper function to calculate average
const calculateAverage = (values) => {
  if (!values || values.length === 0) return 0;
  const sum = values.reduce((acc, val) => acc + parseFloat(val || 0), 0);
  return sum / values.length;
};

// Helper function to calculate sum
const calculateSum = (values) => {
  if (!values || values.length === 0) return 0;
  return values.reduce((acc, val) => acc + parseFloat(val || 0), 0);
};

// Dashboard App with Error Boundary
const DashboardApp = () => {
  const handleRetry = () => {
    console.log('Retrying after error...');
    // Any cleanup or reset logic can go here
  };

  return (
    <SimpleErrorBoundary
      message="An error occurred in the dashboard. Please try again."
      showDetails={true}
      onRetry={handleRetry}
    >
      <SimpleDashboard />
    </SimpleErrorBoundary>
  );
};

// Render the app
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <DashboardApp />
  </React.StrictMode>
);
