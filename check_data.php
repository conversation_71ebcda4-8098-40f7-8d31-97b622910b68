<?php
// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Database Check</h1>";

// Check if database exists
$result = $conn->query("SHOW DATABASES LIKE '$dbname'");
if ($result->num_rows == 0) {
    echo "<p style='color:red'>Database $dbname does not exist!</p>";
    exit;
}

// Select database
$conn->select_db($dbname);

// Check if table exists
$result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
if ($result->num_rows == 0) {
    echo "<p style='color:red'>Table electrical_data does not exist!</p>";
    exit;
}

// Count records in the table
$result = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
$row = $result->fetch_assoc();
$count = $row['count'];

echo "<p>Number of records in the table: <strong>$count</strong></p>";

if ($count > 0) {
    // Get the latest record
    $result = $conn->query("SELECT * FROM electrical_data ORDER BY timestamp DESC LIMIT 1");
    $row = $result->fetch_assoc();
    
    echo "<h2>Latest Record</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    
    foreach ($row as $key => $value) {
        echo "<tr>";
        echo "<td>$key</td>";
        echo "<td>$value</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Get the raw JSON that would be returned by get_latest_data.php
    echo "<h2>JSON Response</h2>";
    echo "<pre>";
    
    // Convert timestamp to IST
    $timestamp = new DateTime($row["timestamp"]);
    $timestamp_ist = $timestamp->format('Y-m-d H:i:s');
    
    // Format the response
    $response = [
        "voltage_1" => floatval($row["voltage_1"]),
        "voltage_2" => floatval($row["voltage_2"]),
        "voltage_3" => floatval($row["voltage_3"]),
        "current_1" => floatval($row["current_1"]),
        "current_2" => floatval($row["current_2"]),
        "current_3" => floatval($row["current_3"]),
        "pf_1" => floatval($row["pf_1"]),
        "pf_2" => floatval($row["pf_2"]),
        "pf_3" => floatval($row["pf_3"]),
        "kva_1" => floatval($row["kva_1"]),
        "kva_2" => floatval($row["kva_2"]),
        "kva_3" => floatval($row["kva_3"]),
        "total_kva" => floatval($row["total_kva"]),
        "total_kw" => floatval($row["total_kw"]),
        "total_kvar" => floatval($row["total_kvar"]),
        "frequency" => floatval($row["frequency"]),
        "timestamp" => $timestamp_ist
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    echo "</pre>";
} else {
    echo "<p style='color:red'>No data found in the table.</p>";
    
    // Insert test data
    echo "<h2>Inserting Test Data</h2>";
    
    // Current timestamp
    $timestamp = date('Y-m-d H:i:s');
    
    // Test data
    $sql = "INSERT INTO electrical_data (
        voltage_1, voltage_2, voltage_3,
        current_1, current_2, current_3,
        pf_1, pf_2, pf_3,
        kva_1, kva_2, kva_3,
        total_kva, total_kw, total_kvar,
        frequency, timestamp
    ) VALUES (
        400.5, 401.2, 399.8,
        50.1, 49.8, 50.3,
        0.92, 0.93, 0.91,
        20.1, 19.8, 20.3,
        60.2, 55.4, 24.8,
        50.1, '$timestamp'
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color:green'>Test data inserted successfully.</p>";
        echo "<p>Refresh this page to see the inserted data.</p>";
    } else {
        echo "<p style='color:red'>Error inserting test data: " . $conn->error . "</p>";
    }
}

// Close the connection
$conn->close();
?>
