/**
 * HTML generators for AI analysis detailed results
 */

/**
 * Generate HTML for voltage analysis
 * @param {Object} analysis - Voltage analysis results
 * @returns {string} HTML content
 */
function generateVoltageAnalysisHTML(analysis) {
    if (!analysis) return '<p>No voltage analysis data available.</p>';
    
    const stats = analysis.statistics;
    
    let html = `
        <div class="analysis-stat-grid">
            <div class="analysis-stat">
                <div class="analysis-stat-label">Average Voltage (Phase 1)</div>
                <div class="analysis-stat-value">${stats.phase1.mean.toFixed(1)} V</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Average Voltage (Phase 2)</div>
                <div class="analysis-stat-value">${stats.phase2.mean.toFixed(1)} V</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Average Voltage (Phase 3)</div>
                <div class="analysis-stat-value">${stats.phase3.mean.toFixed(1)} V</div>
            </div>
        </div>
        
        <h5>Voltage Range</h5>
        <ul class="analysis-detail-list">
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Phase 1 Range</span>
                <span class="analysis-detail-value">${stats.phase1.min.toFixed(1)} - ${stats.phase1.max.toFixed(1)} V</span>
            </li>
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Phase 2 Range</span>
                <span class="analysis-detail-value">${stats.phase2.min.toFixed(1)} - ${stats.phase2.max.toFixed(1)} V</span>
            </li>
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Phase 3 Range</span>
                <span class="analysis-detail-value">${stats.phase3.min.toFixed(1)} - ${stats.phase3.max.toFixed(1)} V</span>
            </li>
        </ul>
    `;
    
    // Add issues section if any issues detected
    const issues = analysis.issues;
    if (issues.highVoltage.detected || issues.lowVoltage.detected || issues.imbalance.detected || issues.fluctuations.detected) {
        html += '<h5>Detected Issues</h5><ul class="analysis-detail-list">';
        
        if (issues.highVoltage.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">High Voltage</span>
                    <span class="analysis-detail-value">Detected (${issues.highVoltage.severity} severity)</span>
                </li>
            `;
        }
        
        if (issues.lowVoltage.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Low Voltage</span>
                    <span class="analysis-detail-value">Detected (${issues.lowVoltage.severity} severity)</span>
                </li>
            `;
        }
        
        if (issues.imbalance.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Phase Imbalance</span>
                    <span class="analysis-detail-value">Avg ${issues.imbalance.statistics.mean}% (${issues.imbalance.severity} severity)</span>
                </li>
            `;
        }
        
        if (issues.fluctuations.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Voltage Fluctuations</span>
                    <span class="analysis-detail-value">Detected (${issues.fluctuations.severity} severity)</span>
                </li>
            `;
        }
        
        html += '</ul>';
    }
    
    // Add recommendations if any
    if (analysis.recommendations && analysis.recommendations.length > 0) {
        html += '<h5>Recommendations</h5><ul>';
        analysis.recommendations.forEach(recommendation => {
            html += `<li>${recommendation}</li>`;
        });
        html += '</ul>';
    }
    
    return html;
}

/**
 * Generate HTML for current analysis
 * @param {Object} analysis - Current analysis results
 * @returns {string} HTML content
 */
function generateCurrentAnalysisHTML(analysis) {
    if (!analysis) return '<p>No current analysis data available.</p>';
    
    const stats = analysis.statistics;
    
    let html = `
        <div class="analysis-stat-grid">
            <div class="analysis-stat">
                <div class="analysis-stat-label">Average Current (Phase 1)</div>
                <div class="analysis-stat-value">${stats.phase1.mean.toFixed(2)} A</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Average Current (Phase 2)</div>
                <div class="analysis-stat-value">${stats.phase2.mean.toFixed(2)} A</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Average Current (Phase 3)</div>
                <div class="analysis-stat-value">${stats.phase3.mean.toFixed(2)} A</div>
            </div>
        </div>
        
        <h5>Current Range</h5>
        <ul class="analysis-detail-list">
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Phase 1 Range</span>
                <span class="analysis-detail-value">${stats.phase1.min.toFixed(2)} - ${stats.phase1.max.toFixed(2)} A</span>
            </li>
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Phase 2 Range</span>
                <span class="analysis-detail-value">${stats.phase2.min.toFixed(2)} - ${stats.phase2.max.toFixed(2)} A</span>
            </li>
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Phase 3 Range</span>
                <span class="analysis-detail-value">${stats.phase3.min.toFixed(2)} - ${stats.phase3.max.toFixed(2)} A</span>
            </li>
        </ul>
    `;
    
    // Add issues section if any issues detected
    const issues = analysis.issues;
    if (issues.imbalance.detected || issues.overload.detected || issues.harmonics.detected) {
        html += '<h5>Detected Issues</h5><ul class="analysis-detail-list">';
        
        if (issues.imbalance.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Current Imbalance</span>
                    <span class="analysis-detail-value">Avg ${issues.imbalance.statistics.mean}% (${issues.imbalance.severity} severity)</span>
                </li>
            `;
        }
        
        if (issues.overload.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Potential Overload</span>
                    <span class="analysis-detail-value">Detected (${issues.overload.severity} severity)</span>
                </li>
            `;
        }
        
        if (issues.harmonics.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Potential Harmonics</span>
                    <span class="analysis-detail-value">Detected (${issues.harmonics.severity} severity)</span>
                </li>
            `;
        }
        
        html += '</ul>';
    }
    
    // Add recommendations if any
    if (analysis.recommendations && analysis.recommendations.length > 0) {
        html += '<h5>Recommendations</h5><ul>';
        analysis.recommendations.forEach(recommendation => {
            html += `<li>${recommendation}</li>`;
        });
        html += '</ul>';
    }
    
    return html;
}

/**
 * Generate HTML for power factor analysis
 * @param {Object} analysis - Power factor analysis results
 * @returns {string} HTML content
 */
function generatePowerFactorAnalysisHTML(analysis) {
    if (!analysis) return '<p>No power factor analysis data available.</p>';
    
    const stats = analysis.statistics;
    
    let html = `
        <div class="analysis-stat-grid">
            <div class="analysis-stat">
                <div class="analysis-stat-label">Average Power Factor</div>
                <div class="analysis-stat-value">${analysis.averagePF}</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Phase 1 Average PF</div>
                <div class="analysis-stat-value">${stats.phase1.mean.toFixed(3)}</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Phase 2 Average PF</div>
                <div class="analysis-stat-value">${stats.phase2.mean.toFixed(3)}</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Phase 3 Average PF</div>
                <div class="analysis-stat-value">${stats.phase3.mean.toFixed(3)}</div>
            </div>
        </div>
    `;
    
    // Add potential savings information
    if (analysis.potentialSavings) {
        html += '<h5>Power Factor Correction</h5><ul class="analysis-detail-list">';
        
        if (analysis.potentialSavings.required) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Current PF</span>
                    <span class="analysis-detail-value">${analysis.potentialSavings.currentPF}</span>
                </li>
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Target PF</span>
                    <span class="analysis-detail-value">${analysis.potentialSavings.targetPF}</span>
                </li>
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Potential Savings</span>
                    <span class="analysis-detail-value">${analysis.potentialSavings.estimatedSavingsPercent}%</span>
                </li>
            `;
        } else {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Status</span>
                    <span class="analysis-detail-value">Power factor is already optimal</span>
                </li>
            `;
        }
        
        html += '</ul>';
    }
    
    // Add issues section if any issues detected
    const issues = analysis.issues;
    if (issues.lowPowerFactor.detected || issues.leadingPowerFactor.detected) {
        html += '<h5>Detected Issues</h5><ul class="analysis-detail-list">';
        
        if (issues.lowPowerFactor.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Low Power Factor</span>
                    <span class="analysis-detail-value">Detected (${issues.lowPowerFactor.severity} severity)</span>
                </li>
            `;
        }
        
        if (issues.leadingPowerFactor.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Leading Power Factor</span>
                    <span class="analysis-detail-value">Detected (${issues.leadingPowerFactor.severity} severity)</span>
                </li>
            `;
        }
        
        html += '</ul>';
    }
    
    // Add recommendations if any
    if (analysis.recommendations && analysis.recommendations.length > 0) {
        html += '<h5>Recommendations</h5><ul>';
        analysis.recommendations.forEach(recommendation => {
            html += `<li>${recommendation}</li>`;
        });
        html += '</ul>';
    }
    
    return html;
}

/**
 * Generate HTML for frequency analysis
 * @param {Object} analysis - Frequency analysis results
 * @returns {string} HTML content
 */
function generateFrequencyAnalysisHTML(analysis) {
    if (!analysis) return '<p>No frequency analysis data available.</p>';
    
    const stats = analysis.statistics;
    
    let html = `
        <div class="analysis-stat-grid">
            <div class="analysis-stat">
                <div class="analysis-stat-label">Average Frequency</div>
                <div class="analysis-stat-value">${stats.mean.toFixed(2)} Hz</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Frequency Range</div>
                <div class="analysis-stat-value">${stats.min.toFixed(2)} - ${stats.max.toFixed(2)} Hz</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Standard Deviation</div>
                <div class="analysis-stat-value">${stats.stdDev.toFixed(3)} Hz</div>
            </div>
        </div>
    `;
    
    // Add issues section if any issues detected
    const issues = analysis.issues;
    if (issues.deviation.detected || issues.stability.detected) {
        html += '<h5>Detected Issues</h5><ul class="analysis-detail-list">';
        
        if (issues.deviation.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Frequency Deviation</span>
                    <span class="analysis-detail-value">Max ${issues.deviation.statistics.maxDeviation} Hz (${issues.deviation.severity} severity)</span>
                </li>
            `;
        }
        
        if (issues.stability.detected) {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Frequency Stability</span>
                    <span class="analysis-detail-value">Issues detected (${issues.stability.severity} severity)</span>
                </li>
            `;
        }
        
        html += '</ul>';
    }
    
    // Add recommendations if any
    if (analysis.recommendations && analysis.recommendations.length > 0) {
        html += '<h5>Recommendations</h5><ul>';
        analysis.recommendations.forEach(recommendation => {
            html += `<li>${recommendation}</li>`;
        });
        html += '</ul>';
    }
    
    return html;
}

// Export HTML generator functions to global scope
window.generateVoltageAnalysisHTML = generateVoltageAnalysisHTML;
window.generateCurrentAnalysisHTML = generateCurrentAnalysisHTML;
window.generatePowerFactorAnalysisHTML = generatePowerFactorAnalysisHTML;
window.generateFrequencyAnalysisHTML = generateFrequencyAnalysisHTML;
