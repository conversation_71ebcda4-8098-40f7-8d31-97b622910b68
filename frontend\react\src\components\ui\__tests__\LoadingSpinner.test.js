import React from 'react';
import { render, screen } from '@testing-library/react';
import LoadingSpinner from '../LoadingSpinner';

describe('LoadingSpinner', () => {
  it('renders with default props', () => {
    render(<LoadingSpinner />);
    
    // Should show default message
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    // Should not have fullScreen class
    const container = screen.getByText('Loading...').parentElement;
    expect(container).not.toHaveStyle('position: fixed');
  });
  
  it('renders with custom message', () => {
    render(<LoadingSpinner message="Custom loading message" />);
    
    // Should show custom message
    expect(screen.getByText('Custom loading message')).toBeInTheDocument();
  });
  
  it('renders in fullScreen mode', () => {
    render(<LoadingSpinner fullScreen={true} />);
    
    // Should have fullScreen styles
    const container = screen.getByText('Loading...').parentElement;
    expect(container).toHaveStyle('position: fixed');
  });
  
  it('renders with different sizes', () => {
    const { rerender } = render(<LoadingSpinner size="small" />);
    
    // Small spinner should have smaller dimensions
    let spinner = screen.getByText('Loading...').previousSibling;
    expect(spinner).toHaveStyle('width: 1.5rem');
    expect(spinner).toHaveStyle('height: 1.5rem');
    
    // Rerender with large size
    rerender(<LoadingSpinner size="large" />);
    
    // Large spinner should have larger dimensions
    spinner = screen.getByText('Loading...').previousSibling;
    expect(spinner).toHaveStyle('width: 3rem');
    expect(spinner).toHaveStyle('height: 3rem');
  });
});
