<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Include database connection
require_once 'db_connect.php';

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        handleGetRequest();
    } elseif ($method === 'POST') {
        handlePostRequest();
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Handle GET requests for fetching alerts and events
 */
function handleGetRequest() {
    $type = $_GET['type'] ?? 'all';
    $start = $_GET['start'] ?? date('Y-m-d H:i:s', strtotime('-24 hours'));
    $end = $_GET['end'] ?? date('Y-m-d H:i:s');
    $page = intval($_GET['page'] ?? 1);
    $limit = 25;
    $offset = ($page - 1) * $limit;
    
    // Generate sample alerts and events data
    $alerts = generateSampleAlerts($type, $start, $end, $limit, $offset);
    $events = generateSampleEvents($type, $start, $end, $limit, $offset);
    $counts = getAlertCounts();
    $stats = getAlertStats();
    
    echo json_encode([
        'success' => true,
        'alerts' => $alerts,
        'events' => $events,
        'counts' => $counts,
        'stats' => $stats,
        'totalPages' => 5, // Sample pagination
        'currentPage' => $page,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Handle POST requests for alert actions
 */
function handlePostRequest() {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'acknowledge':
            $alertId = $input['alert_id'] ?? '';
            acknowledgeAlert($alertId);
            break;
            
        case 'dismiss':
            $alertId = $input['alert_id'] ?? '';
            dismissAlert($alertId);
            break;
            
        case 'acknowledge_all':
            acknowledgeAllAlerts();
            break;
            
        case 'clear_resolved':
            clearResolvedAlerts();
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Action completed successfully',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Generate sample alerts data
 */
function generateSampleAlerts($type, $start, $end, $limit, $offset) {
    $sampleAlerts = [
        [
            'id' => 'alert_001',
            'title' => 'High Voltage Detected',
            'message' => 'Phase 1 voltage exceeded 440V threshold (measured: 445V)',
            'severity' => 'critical',
            'source' => 'Voltage Monitor',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-2 hours')),
            'status' => 'active'
        ],
        [
            'id' => 'alert_002',
            'title' => 'Power Factor Low',
            'message' => 'System power factor dropped below 0.8 (current: 0.75)',
            'severity' => 'warning',
            'source' => 'Power Quality Monitor',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-4 hours')),
            'status' => 'active'
        ],
        [
            'id' => 'alert_003',
            'title' => 'Frequency Deviation',
            'message' => 'Grid frequency outside normal range (measured: 49.2Hz)',
            'severity' => 'warning',
            'source' => 'Frequency Monitor',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-6 hours')),
            'status' => 'acknowledged'
        ],
        [
            'id' => 'alert_004',
            'title' => 'Current Imbalance',
            'message' => 'Phase current imbalance detected (>10% difference)',
            'severity' => 'info',
            'source' => 'Current Monitor',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-8 hours')),
            'status' => 'resolved'
        ],
        [
            'id' => 'alert_005',
            'title' => 'Communication Error',
            'message' => 'Lost communication with sensor module #3',
            'severity' => 'critical',
            'source' => 'System Monitor',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-1 hour')),
            'status' => 'active'
        ]
    ];
    
    // Filter by type if specified
    if ($type !== 'all') {
        $sampleAlerts = array_filter($sampleAlerts, function($alert) use ($type) {
            return $alert['severity'] === $type || $alert['status'] === $type;
        });
    }
    
    return array_slice($sampleAlerts, $offset, $limit);
}

/**
 * Generate sample events data
 */
function generateSampleEvents($type, $start, $end, $limit, $offset) {
    $sampleEvents = [
        [
            'id' => 'event_001',
            'type' => 'System',
            'severity' => 'info',
            'source' => 'Data Logger',
            'message' => 'System startup completed successfully',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-12 hours')),
            'status' => 'completed'
        ],
        [
            'id' => 'event_002',
            'type' => 'Measurement',
            'severity' => 'info',
            'source' => 'Voltage Sensor',
            'message' => 'Voltage measurement calibration performed',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-10 hours')),
            'status' => 'completed'
        ],
        [
            'id' => 'event_003',
            'type' => 'Alert',
            'severity' => 'warning',
            'source' => 'Alert System',
            'message' => 'Power factor alert threshold updated to 0.8',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-8 hours')),
            'status' => 'active'
        ],
        [
            'id' => 'event_004',
            'type' => 'Maintenance',
            'severity' => 'info',
            'source' => 'Maintenance System',
            'message' => 'Scheduled maintenance reminder: Monthly calibration due',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-6 hours')),
            'status' => 'pending'
        ],
        [
            'id' => 'event_005',
            'type' => 'User',
            'severity' => 'info',
            'source' => 'User Interface',
            'message' => 'User admin logged in from 192.168.1.100',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-3 hours')),
            'status' => 'completed'
        ]
    ];
    
    return array_slice($sampleEvents, $offset, $limit);
}

/**
 * Get alert counts by severity
 */
function getAlertCounts() {
    return [
        'critical' => 2,
        'warning' => 3,
        'info' => 5,
        'total' => 10
    ];
}

/**
 * Get alert statistics
 */
function getAlertStats() {
    return [
        'total_critical' => 2,
        'total_warning' => 3,
        'total_info' => 5,
        'resolved' => 8,
        'acknowledged' => 3,
        'active' => 7
    ];
}

/**
 * Acknowledge a specific alert
 */
function acknowledgeAlert($alertId) {
    // In a real implementation, this would update the database
    // For now, we'll just simulate success
    if (empty($alertId)) {
        throw new Exception('Alert ID is required');
    }
    
    // Simulate database update
    return true;
}

/**
 * Dismiss a specific alert
 */
function dismissAlert($alertId) {
    // In a real implementation, this would update the database
    if (empty($alertId)) {
        throw new Exception('Alert ID is required');
    }
    
    // Simulate database update
    return true;
}

/**
 * Acknowledge all active alerts
 */
function acknowledgeAllAlerts() {
    // In a real implementation, this would update all active alerts in the database
    return true;
}

/**
 * Clear all resolved alerts
 */
function clearResolvedAlerts() {
    // In a real implementation, this would delete resolved alerts from the database
    return true;
}

/**
 * Create alerts table (for future implementation)
 */
function createAlertsTable() {
    global $conn;
    
    $sql = "CREATE TABLE IF NOT EXISTS alerts (
        id VARCHAR(50) PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        message TEXT,
        severity ENUM('critical', 'warning', 'info') NOT NULL,
        source VARCHAR(100),
        status ENUM('active', 'acknowledged', 'resolved', 'dismissed') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        acknowledged_at TIMESTAMP NULL,
        resolved_at TIMESTAMP NULL
    )";
    
    return $conn->query($sql);
}

/**
 * Create events table (for future implementation)
 */
function createEventsTable() {
    global $conn;
    
    $sql = "CREATE TABLE IF NOT EXISTS events (
        id VARCHAR(50) PRIMARY KEY,
        type VARCHAR(50) NOT NULL,
        severity ENUM('critical', 'warning', 'info') NOT NULL,
        source VARCHAR(100),
        message TEXT,
        status ENUM('active', 'completed', 'pending', 'failed') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data JSON
    )";
    
    return $conn->query($sql);
}
?>
