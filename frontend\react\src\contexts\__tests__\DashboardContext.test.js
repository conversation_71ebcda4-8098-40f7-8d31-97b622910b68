import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { DashboardProvider, useDashboard } from '../DashboardContext';

// Mock localStorage
const mockLocalStorage = (() => {
  let store = {};
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn(key => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Test component that uses the context
const TestComponent = () => {
  const { 
    activeWidgets, 
    addWidget, 
    removeWidget, 
    editMode, 
    toggleEditMode 
  } = useDashboard();
  
  return (
    <div>
      <div data-testid="active-widgets">{activeWidgets.join(',')}</div>
      <div data-testid="edit-mode">{editMode.toString()}</div>
      <button 
        data-testid="toggle-edit-mode" 
        onClick={toggleEditMode}
      >
        Toggle Edit Mode
      </button>
      <button 
        data-testid="add-widget" 
        onClick={() => addWidget('testWidget')}
      >
        Add Widget
      </button>
      <button 
        data-testid="remove-widget" 
        onClick={() => removeWidget('voltageWidget')}
      >
        Remove Widget
      </button>
    </div>
  );
};

describe('DashboardContext', () => {
  beforeEach(() => {
    mockLocalStorage.clear();
  });
  
  it('should provide default values', () => {
    render(
      <DashboardProvider>
        <TestComponent />
      </DashboardProvider>
    );
    
    // Default edit mode should be false
    expect(screen.getByTestId('edit-mode').textContent).toBe('false');
    
    // Default active widgets should include voltage, current, etc.
    const activeWidgetsText = screen.getByTestId('active-widgets').textContent;
    expect(activeWidgetsText).toContain('voltageWidget');
    expect(activeWidgetsText).toContain('currentWidget');
  });
  
  it('should toggle edit mode', () => {
    render(
      <DashboardProvider>
        <TestComponent />
      </DashboardProvider>
    );
    
    // Initial state
    expect(screen.getByTestId('edit-mode').textContent).toBe('false');
    
    // Toggle edit mode
    fireEvent.click(screen.getByTestId('toggle-edit-mode'));
    
    // Edit mode should be true
    expect(screen.getByTestId('edit-mode').textContent).toBe('true');
    
    // Toggle again
    fireEvent.click(screen.getByTestId('toggle-edit-mode'));
    
    // Edit mode should be false again
    expect(screen.getByTestId('edit-mode').textContent).toBe('false');
  });
  
  it('should add and remove widgets', () => {
    render(
      <DashboardProvider>
        <TestComponent />
      </DashboardProvider>
    );
    
    // Initial state should not include testWidget
    let activeWidgetsText = screen.getByTestId('active-widgets').textContent;
    expect(activeWidgetsText).not.toContain('testWidget');
    
    // Add testWidget
    fireEvent.click(screen.getByTestId('add-widget'));
    
    // Active widgets should now include testWidget
    activeWidgetsText = screen.getByTestId('active-widgets').textContent;
    expect(activeWidgetsText).toContain('testWidget');
    
    // Remove voltageWidget
    fireEvent.click(screen.getByTestId('remove-widget'));
    
    // Active widgets should not include voltageWidget
    activeWidgetsText = screen.getByTestId('active-widgets').textContent;
    expect(activeWidgetsText).not.toContain('voltageWidget');
  });
});
