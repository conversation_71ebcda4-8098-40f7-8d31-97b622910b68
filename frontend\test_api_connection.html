<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 300px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Dashboard API Connection Test</h1>
        <p>This tests the exact same API call that the dashboard makes.</p>
        
        <button class="button" onclick="testDashboardAPI()">Test Dashboard API Call</button>
        <button class="button" onclick="testDirectAPI()">Test Direct API</button>
        <button class="button" onclick="sendTestData()">Send Test Data First</button>
        
        <div id="results"></div>
        
        <h3>📋 What to Check:</h3>
        <ol>
            <li><strong>If API returns data:</strong> Dashboard should work - check browser console for JS errors</li>
            <li><strong>If API returns error:</strong> Database or PHP issue</li>
            <li><strong>If API doesn't respond:</strong> Path or server issue</li>
            <li><strong>If no data in database:</strong> Send test data first</li>
        </ol>
    </div>

    <script>
        async function testDashboardAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Testing dashboard API call...</div>';
            
            try {
                // This is the EXACT same call the dashboard makes
                const timestamp = new Date().getTime();
                const apiUrl = `../backend/get_latest_data.php?_=${timestamp}&records=20`;
                
                console.log('Testing dashboard API URL:', apiUrl);
                
                const response = await fetch(apiUrl);
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const rawData = await response.text();
                console.log('Raw response:', rawData);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
                }
                
                let data;
                try {
                    data = JSON.parse(rawData);
                } catch (parseError) {
                    throw new Error('Invalid JSON response: ' + parseError.message);
                }
                
                console.log('Parsed data:', data);
                
                if (data.error) {
                    throw new Error('API Error: ' + data.error);
                }
                
                const latestData = Array.isArray(data) ? data[0] : data;
                
                resultsDiv.innerHTML = `
                    <div class="success">✅ Dashboard API call successful!</div>
                    <h4>Latest Data Point:</h4>
                    <pre>${JSON.stringify(latestData, null, 2)}</pre>
                    <h4>Raw Response:</h4>
                    <pre>${rawData}</pre>
                    <div class="info">
                        <strong>Records received:</strong> ${Array.isArray(data) ? data.length : 1}<br>
                        <strong>Timestamp:</strong> ${latestData?.timestamp || 'N/A'}<br>
                        <strong>Voltage 1:</strong> ${latestData?.voltage_1 || 'N/A'}<br>
                        <strong>Current 1:</strong> ${latestData?.current_1 || 'N/A'}<br>
                        <strong>Frequency:</strong> ${latestData?.frequency || 'N/A'}
                    </div>
                `;
                
            } catch (error) {
                console.error('Dashboard API test failed:', error);
                resultsDiv.innerHTML = `
                    <div class="error">❌ Dashboard API call failed: ${error.message}</div>
                    <div class="info">This is the same error the dashboard would see!</div>
                `;
            }
        }
        
        async function testDirectAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Testing direct API call...</div>';
            
            try {
                const response = await fetch('/online%20data%20logger/backend/get_latest_data.php');
                const data = await response.text();
                
                resultsDiv.innerHTML = `
                    <div class="success">✅ Direct API call successful!</div>
                    <h4>Response:</h4>
                    <pre>${data}</pre>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Direct API call failed: ${error.message}</div>
                `;
            }
        }
        
        async function sendTestData() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Sending test data...</div>';
            
            const testData = {
                voltage_1: 230.5, voltage_2: 231.2, voltage_3: 229.8,
                current_1: 5.1, current_2: 5.2, current_3: 5.0,
                pf_1: 0.92, pf_2: 0.93, pf_3: 0.91,
                kva_1: 1150.5, kva_2: 1151.2, kva_3: 1149.8,
                total_kva: 3450.5, total_kw: 3277.8, total_kvar: 1076.5,
                frequency: 50.1
            };
            
            try {
                const response = await fetch('../backend/receive_data.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.text();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ Test data sent successfully!</div>
                        <div class="info">Now try testing the dashboard API call again.</div>
                        <pre>${result}</pre>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Failed to send test data: ${error.message}</div>
                `;
            }
        }
    </script>
</body>
</html>
