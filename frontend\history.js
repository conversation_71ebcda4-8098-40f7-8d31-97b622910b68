/**
 * Historical Data Page JavaScript
 * Light mode optimized for Online Data Logger System
 */

// Global variables
let historyChart = null;
let currentData = [];
let currentPage = 1;
let totalPages = 1;
let isLoading = false;

// Chart.js configuration
const chartConfig = {
    type: 'line',
    data: {
        datasets: []
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'top',
                labels: {
                    usePointStyle: true,
                    padding: 20,
                    color: '#1e293b'
                }
            },
            tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                titleColor: '#1e293b',
                bodyColor: '#64748b',
                borderColor: '#e2e8f0',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true
            }
        },
        scales: {
            x: {
                type: 'time',
                time: {
                    displayFormats: {
                        second: 'HH:mm:ss',
                        minute: 'HH:mm',
                        hour: 'HH:mm'
                    }
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.05)'
                },
                ticks: {
                    color: '#64748b'
                }
            },
            y: {
                grid: {
                    color: 'rgba(0, 0, 0, 0.05)'
                },
                ticks: {
                    color: '#64748b'
                }
            }
        },
        animation: {
            duration: 0
        }
    }
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Historical Data page...');
    
    initializeChart();
    setupEventListeners();
    setDefaultDates();
    loadData();
});

function initializeChart() {
    const ctx = document.getElementById('historyChart');
    if (ctx) {
        historyChart = new Chart(ctx, chartConfig);
    }
}

function setupEventListeners() {
    // Quick filters
    document.querySelectorAll('.quick-filter').forEach(filter => {
        filter.addEventListener('click', function() {
            document.querySelectorAll('.quick-filter').forEach(f => f.classList.remove('active'));
            this.classList.add('active');
            
            const range = this.dataset.range;
            if (range !== 'custom') {
                setQuickRange(range);
                loadData();
            }
        });
    });

    // Filter button
    const filterBtn = document.getElementById('filterBtn');
    if (filterBtn) {
        filterBtn.addEventListener('click', loadData);
    }

    // Export button
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportData);
    }

    // Parameter buttons
    document.querySelectorAll('.parameter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.parameter-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const parameter = this.dataset.parameter;
            updateChart(parameter);
        });
    });

    // Pagination
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                loadData();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                loadData();
            }
        });
    }
}

function setDefaultDates() {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    if (startDate) {
        startDate.value = formatDateTimeLocal(oneHourAgo);
    }
    
    if (endDate) {
        endDate.value = formatDateTimeLocal(now);
    }
}

function setQuickRange(range) {
    const now = new Date();
    let startTime;
    
    switch (range) {
        case '1h':
            startTime = new Date(now.getTime() - 60 * 60 * 1000);
            break;
        case '6h':
            startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
            break;
        case '24h':
            startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
        case '7d':
            startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case '30d':
            startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
        default:
            return;
    }
    
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    if (startDate) startDate.value = formatDateTimeLocal(startTime);
    if (endDate) endDate.value = formatDateTimeLocal(now);
}

function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

async function loadData() {
    if (isLoading) return;
    
    isLoading = true;
    showMessage('Loading data...', 'info');
    
    const startDate = document.getElementById('startDate')?.value;
    const endDate = document.getElementById('endDate')?.value;
    const limit = document.getElementById('limit')?.value || 25;
    
    try {
        const params = new URLSearchParams({
            start_date: startDate,
            end_date: endDate,
            limit: limit,
            page: currentPage
        });
        
        const response = await fetch(`../backend/get_historical_data.php?${params}`);
        const result = await response.json();
        
        if (result.error) {
            throw new Error(result.error);
        }
        
        currentData = result.data || [];
        totalPages = result.total_pages || 1;
        
        updateTable();
        updateChart('voltage'); // Default to voltage
        updatePagination();
        
        showMessage(`Loaded ${currentData.length} records`, 'success');
        
    } catch (error) {
        console.error('Error loading data:', error);
        showMessage('Error loading data: ' + error.message, 'error');
    } finally {
        isLoading = false;
    }
}

function updateTable() {
    const tbody = document.getElementById('dataRows');
    if (!tbody) return;
    
    if (currentData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="12" class="loading">No data found</td></tr>';
        return;
    }
    
    tbody.innerHTML = currentData.map(row => `
        <tr>
            <td>${new Date(row.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}</td>
            <td>${parseFloat(row.voltage_1).toFixed(2)}</td>
            <td>${parseFloat(row.voltage_2).toFixed(2)}</td>
            <td>${parseFloat(row.voltage_3).toFixed(2)}</td>
            <td>${parseFloat(row.current_1).toFixed(3)}</td>
            <td>${parseFloat(row.current_2).toFixed(3)}</td>
            <td>${parseFloat(row.current_3).toFixed(3)}</td>
            <td>${parseFloat(row.pf_1).toFixed(3)}</td>
            <td>${parseFloat(row.pf_2).toFixed(3)}</td>
            <td>${parseFloat(row.pf_3).toFixed(3)}</td>
            <td>${parseFloat(row.total_kw).toFixed(2)}</td>
            <td>${parseFloat(row.frequency).toFixed(2)}</td>
        </tr>
    `).join('');
}

function updateChart(parameter) {
    if (!historyChart || currentData.length === 0) return;
    
    const colors = ['#2563eb', '#dc2626', '#16a34a', '#ea580c', '#7c3aed', '#0891b2'];
    let datasets = [];
    
    switch (parameter) {
        case 'voltage':
            datasets = [
                { label: 'V1N', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' },
                { label: 'V2N', data: [], borderColor: colors[1], backgroundColor: colors[1] + '20' },
                { label: 'V3N', data: [], borderColor: colors[2], backgroundColor: colors[2] + '20' }
            ];
            currentData.forEach(row => {
                const time = new Date(row.timestamp);
                datasets[0].data.push({ x: time, y: parseFloat(row.voltage_1) });
                datasets[1].data.push({ x: time, y: parseFloat(row.voltage_2) });
                datasets[2].data.push({ x: time, y: parseFloat(row.voltage_3) });
            });
            break;
            
        case 'current':
            datasets = [
                { label: 'Ia', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' },
                { label: 'Ib', data: [], borderColor: colors[1], backgroundColor: colors[1] + '20' },
                { label: 'Ic', data: [], borderColor: colors[2], backgroundColor: colors[2] + '20' }
            ];
            currentData.forEach(row => {
                const time = new Date(row.timestamp);
                datasets[0].data.push({ x: time, y: parseFloat(row.current_1) });
                datasets[1].data.push({ x: time, y: parseFloat(row.current_2) });
                datasets[2].data.push({ x: time, y: parseFloat(row.current_3) });
            });
            break;
            
        case 'power':
            datasets = [
                { label: 'Total kW', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' },
                { label: 'Total kVA', data: [], borderColor: colors[1], backgroundColor: colors[1] + '20' },
                { label: 'Total kVAR', data: [], borderColor: colors[2], backgroundColor: colors[2] + '20' }
            ];
            currentData.forEach(row => {
                const time = new Date(row.timestamp);
                datasets[0].data.push({ x: time, y: parseFloat(row.total_kw) });
                datasets[1].data.push({ x: time, y: parseFloat(row.total_kva) });
                datasets[2].data.push({ x: time, y: parseFloat(row.total_kvar) });
            });
            break;
            
        case 'pf':
            datasets = [
                { label: 'PF1', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' },
                { label: 'PF2', data: [], borderColor: colors[1], backgroundColor: colors[1] + '20' },
                { label: 'PF3', data: [], borderColor: colors[2], backgroundColor: colors[2] + '20' }
            ];
            currentData.forEach(row => {
                const time = new Date(row.timestamp);
                datasets[0].data.push({ x: time, y: parseFloat(row.pf_1) });
                datasets[1].data.push({ x: time, y: parseFloat(row.pf_2) });
                datasets[2].data.push({ x: time, y: parseFloat(row.pf_3) });
            });
            break;
            
        case 'frequency':
            datasets = [
                { label: 'Frequency', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' }
            ];
            currentData.forEach(row => {
                const time = new Date(row.timestamp);
                datasets[0].data.push({ x: time, y: parseFloat(row.frequency) });
            });
            break;
    }
    
    // Apply common dataset properties
    datasets.forEach(dataset => {
        dataset.borderWidth = 1;
        dataset.fill = false;
        dataset.tension = 0.1;
        dataset.pointRadius = 0;
        dataset.pointHoverRadius = 4;
    });
    
    historyChart.data.datasets = datasets;
    historyChart.update();
}

function updatePagination() {
    const pageInfo = document.getElementById('pageInfo');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    if (pageInfo) {
        pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
    }
    
    if (prevBtn) {
        prevBtn.disabled = currentPage <= 1;
    }
    
    if (nextBtn) {
        nextBtn.disabled = currentPage >= totalPages;
    }
}

async function exportData() {
    try {
        showMessage('Preparing export...', 'info');
        
        const startDate = document.getElementById('startDate')?.value;
        const endDate = document.getElementById('endDate')?.value;
        
        const params = new URLSearchParams({
            start_date: startDate,
            end_date: endDate,
            export: 'csv'
        });
        
        const response = await fetch(`../backend/export_data.php?${params}`);
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `electrical_data_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            showMessage('Data exported successfully', 'success');
        } else {
            throw new Error('Export failed');
        }
        
    } catch (error) {
        console.error('Export error:', error);
        showMessage('Export failed: ' + error.message, 'error');
    }
}

function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    if (!container) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.innerHTML = `
        <span class="material-icons-round">${getMessageIcon(type)}</span>
        ${message}
    `;
    
    container.innerHTML = '';
    container.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

function getMessageIcon(type) {
    switch (type) {
        case 'success': return 'check_circle';
        case 'error': return 'error';
        case 'warning': return 'warning';
        default: return 'info';
    }
}
