<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Dashboard - Online Data Logger</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .card-title { font-size: 14px; color: #666; margin-bottom: 10px; }
        .card-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .data-table { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: 600; }
        .button { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Simple Dashboard - Online Data Logger</h1>
            <div id="status" class="status info">Loading...</div>
            <div>
                <button class="button" onclick="refreshData()">Refresh Data</button>
                <button class="button" onclick="sendTestData()">Send Test Data</button>
                <a href="advanced_dashboard.php" class="button">Advanced Dashboard</a>
            </div>
        </div>
        
        <div class="cards">
            <div class="card">
                <div class="card-title">Average Voltage</div>
                <div class="card-value" id="avgVoltage">-- V</div>
            </div>
            <div class="card">
                <div class="card-title">Total Current</div>
                <div class="card-value" id="totalCurrent">-- A</div>
            </div>
            <div class="card">
                <div class="card-title">Total Power</div>
                <div class="card-value" id="totalPower">-- kW</div>
            </div>
            <div class="card">
                <div class="card-title">Frequency</div>
                <div class="card-value" id="frequency">-- Hz</div>
            </div>
        </div>
        
        <div class="data-table">
            <h3>Latest Data Records</h3>
            <table>
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Voltage 1</th>
                        <th>Current 1</th>
                        <th>Power Factor 1</th>
                        <th>Frequency</th>
                        <th>Total KW</th>
                    </tr>
                </thead>
                <tbody id="dataTableBody">
                    <tr><td colspan="6">Loading...</td></tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let refreshInterval;
        
        // Start data fetching when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Simple dashboard loaded');
            refreshData();
            
            // Auto-refresh every 5 seconds
            refreshInterval = setInterval(refreshData, 5000);
        });
        
        async function refreshData() {
            try {
                console.log('Fetching data...');
                updateStatus('Fetching data...', 'info');
                
                const response = await fetch('../backend/get_latest_data.php?records=10');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('Data received:', data);
                
                if (Array.isArray(data) && data.length > 0) {
                    updateCards(data[0]);
                    updateTable(data);
                    updateStatus(`Data updated successfully - ${data.length} records`, 'success');
                } else {
                    updateStatus('No data found in database', 'error');
                    clearCards();
                    clearTable();
                }
                
            } catch (error) {
                console.error('Error fetching data:', error);
                updateStatus(`Error: ${error.message}`, 'error');
                clearCards();
                clearTable();
            }
        }
        
        function updateCards(latestData) {
            // Calculate average voltage
            const avgVoltage = ((parseFloat(latestData.voltage_1) + parseFloat(latestData.voltage_2) + parseFloat(latestData.voltage_3)) / 3).toFixed(2);
            document.getElementById('avgVoltage').textContent = `${avgVoltage} V`;
            
            // Calculate total current
            const totalCurrent = (parseFloat(latestData.current_1) + parseFloat(latestData.current_2) + parseFloat(latestData.current_3)).toFixed(2);
            document.getElementById('totalCurrent').textContent = `${totalCurrent} A`;
            
            // Total power
            document.getElementById('totalPower').textContent = `${parseFloat(latestData.total_kw).toFixed(2)} kW`;
            
            // Frequency
            document.getElementById('frequency').textContent = `${parseFloat(latestData.frequency).toFixed(2)} Hz`;
        }
        
        function updateTable(data) {
            const tbody = document.getElementById('dataTableBody');
            
            const rows = data.slice(0, 10).map(record => `
                <tr>
                    <td>${record.timestamp}</td>
                    <td>${parseFloat(record.voltage_1).toFixed(2)} V</td>
                    <td>${parseFloat(record.current_1).toFixed(3)} A</td>
                    <td>${parseFloat(record.pf_1).toFixed(3)}</td>
                    <td>${parseFloat(record.frequency).toFixed(2)} Hz</td>
                    <td>${parseFloat(record.total_kw).toFixed(2)} kW</td>
                </tr>
            `).join('');
            
            tbody.innerHTML = rows;
        }
        
        function clearCards() {
            document.getElementById('avgVoltage').textContent = '-- V';
            document.getElementById('totalCurrent').textContent = '-- A';
            document.getElementById('totalPower').textContent = '-- kW';
            document.getElementById('frequency').textContent = '-- Hz';
        }
        
        function clearTable() {
            document.getElementById('dataTableBody').innerHTML = '<tr><td colspan="6">No data available</td></tr>';
        }
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        async function sendTestData() {
            try {
                updateStatus('Sending test data...', 'info');
                
                const testData = {
                    voltage_1: 230.5, voltage_2: 231.2, voltage_3: 229.8,
                    current_1: 5.1, current_2: 5.2, current_3: 5.0,
                    pf_1: 0.92, pf_2: 0.93, pf_3: 0.91,
                    kva_1: 1150.5, kva_2: 1151.2, kva_3: 1149.8,
                    total_kva: 3450.5, total_kw: 3277.8, total_kvar: 1076.5,
                    frequency: 50.1
                };
                
                const response = await fetch('../backend/receive_data.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    updateStatus('Test data sent successfully', 'success');
                    setTimeout(refreshData, 1000);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                updateStatus(`Failed to send test data: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
