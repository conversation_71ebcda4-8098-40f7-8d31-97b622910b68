<?php
// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if database exists
$result = $conn->query("SHOW DATABASES LIKE '$dbname'");
if ($result->num_rows == 0) {
    // Create database
    $sql = "CREATE DATABASE IF NOT EXISTS $dbname";
    if ($conn->query($sql) === TRUE) {
        echo "Database created successfully<br>";
    } else {
        echo "Error creating database: " . $conn->error . "<br>";
        exit;
    }
}

// Select database
$conn->select_db($dbname);

// Check if table exists
$result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
if ($result->num_rows == 0) {
    // Create table
    $sql = "CREATE TABLE IF NOT EXISTS electrical_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        voltage_1 FLOAT,
        voltage_2 FLOAT,
        voltage_3 FLOAT,
        current_1 FLOAT,
        current_2 FLOAT,
        current_3 FLOAT,
        pf_1 FLOAT,
        pf_2 FLOAT,
        pf_3 FLOAT,
        kva_1 FLOAT,
        kva_2 FLOAT,
        kva_3 FLOAT,
        total_kva FLOAT,
        total_kw FLOAT,
        total_kvar FLOAT,
        frequency FLOAT,
        timestamp DATETIME
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Table created successfully<br>";
    } else {
        echo "Error creating table: " . $conn->error . "<br>";
        exit;
    }
}

// Number of data points to generate
$count = isset($_GET['count']) ? intval($_GET['count']) : 10;
if ($count <= 0 || $count > 100) {
    $count = 10;
}

echo "<h2>Generating $count Test Data Points</h2>";

// Generate and insert test data
for ($i = 0; $i < $count; $i++) {
    $timestamp = date('Y-m-d H:i:s', time() - ($count - $i - 1) * 60); // 1 minute apart
    
    // Generate random values with some variation
    $baseVoltage = 400 + rand(-10, 10);
    $baseCurrent = 50 + rand(-5, 5);
    $basePF = 0.9 + (rand(-5, 5) / 100);
    $baseKVA = 20 + rand(-2, 2);
    $baseFrequency = 50 + (rand(-2, 2) / 10);
    
    // Calculate total values
    $totalKVA = $baseKVA * 3;
    $totalKW = $totalKVA * $basePF;
    $totalKVAR = sqrt(pow($totalKVA, 2) - pow($totalKW, 2));
    
    // Insert data
    $sql = "INSERT INTO electrical_data (
        voltage_1, voltage_2, voltage_3,
        current_1, current_2, current_3,
        pf_1, pf_2, pf_3,
        kva_1, kva_2, kva_3,
        total_kva, total_kw, total_kvar,
        frequency, timestamp
    ) VALUES (
        $baseVoltage, " . ($baseVoltage + rand(-5, 5)) . ", " . ($baseVoltage + rand(-5, 5)) . ",
        $baseCurrent, " . ($baseCurrent + rand(-2, 2)) . ", " . ($baseCurrent + rand(-2, 2)) . ",
        $basePF, " . ($basePF + (rand(-2, 2) / 100)) . ", " . ($basePF + (rand(-2, 2) / 100)) . ",
        $baseKVA, " . ($baseKVA + rand(-1, 1)) . ", " . ($baseKVA + rand(-1, 1)) . ",
        $totalKVA, $totalKW, $totalKVAR,
        $baseFrequency, '$timestamp'
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Data point $i inserted with timestamp: $timestamp<br>";
    } else {
        echo "Error inserting data point $i: " . $conn->error . "<br>";
    }
}

// Get the latest record
$result = $conn->query("SELECT * FROM electrical_data ORDER BY id DESC LIMIT 1");
$row = $result->fetch_assoc();

echo "<h3>Latest Data:</h3>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Field</th><th>Value</th></tr>";

foreach ($row as $key => $value) {
    echo "<tr>";
    echo "<td>$key</td>";
    echo "<td>$value</td>";
    echo "</tr>";
}

echo "</table>";

// Count records in the database
$result = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
$row = $result->fetch_assoc();
$totalCount = $row['count'];

echo "<p>Total records in database: $totalCount</p>";

// Close the connection
$conn->close();

echo "<p><a href='frontend/'>Go to Dashboard</a> | <a href='api_tester.php'>Test API</a></p>";
?>
