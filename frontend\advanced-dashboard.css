/* Advanced Dashboard Styling - Professional Theme */
:root {
    /* Professional color palette - Enhanced */
    --primary-color: #0056b3;
    --primary-dark: #003d82;
    --primary-light: #3a7abd;
    --secondary-color: #00a67e;
    --accent-color: #e63946;
    --warning-color: #f9a826;
    --success-color: #00875a;
    --info-color: #0077b6;

    /* Text and background colors */
    --text-color: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --background-color: #f1f5f9;
    --card-color: #ffffff;
    --border-color: #e2e8f0;
    --hover-color: #f8fafc;

    /* Shadows with improved depth perception */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Typography */
    --font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Layout */
    --border-radius-sm: 4px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --transition-speed: 0.2s;
    --content-width: 1440px;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), #008f6c);
    --gradient-accent: linear-gradient(135deg, var(--accent-color), #d13040);
    --gradient-success: linear-gradient(135deg, var(--success-color), #006c47);
    --gradient-warning: linear-gradient(135deg, var(--warning-color), #f08c00);

    /* Dark theme colors (initially not applied) */
    --dark-background: #0f172a;
    --dark-card: #1e293b;
    --dark-border: #334155;
    --dark-text: #f1f5f9;
    --dark-text-secondary: #cbd5e1;
    --dark-text-muted: #94a3b8;
    --dark-hover: #273549;

    /* Chart colors */
    --chart-line-width: 1.5px;
    --chart-point-radius: 0;
    --chart-grid-color: rgba(203, 213, 225, 0.4);
}

/* Dark theme */
body.dark-theme {
    --background-color: var(--dark-background);
    --card-color: var(--dark-card);
    --border-color: var(--dark-border);
    --text-color: var(--dark-text);
    --text-secondary: var(--dark-text-secondary);
    --text-muted: var(--dark-text-muted);
    --hover-color: var(--dark-hover);
    --chart-grid-color: rgba(51, 65, 85, 0.5);

    /* Adjust shadows for dark mode */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Reset styles */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-family);
}

html {
    font-size: 16px;
    height: 100%;
    scroll-behavior: smooth;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    font-size: 0.875rem;
    font-weight: var(--font-weight-normal);
    letter-spacing: 0.01em;
    transition: all var(--transition-speed) ease;
    overflow-x: hidden;
    min-height: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    max-width: var(--content-width);
    margin: 0 auto;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background-color: var(--card-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 100;
    transition: all var(--transition-speed) ease;
    box-shadow: var(--shadow-md);
}

.sidebar-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid var(--border-color);
}

.company-logo {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Legacy logo styling for backward compatibility */
.sidebar-header span {
    color: var(--primary-color);
    font-size: 1.75rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: rgba(0, 86, 179, 0.1);
}

.sidebar-header h2 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    letter-spacing: -0.01em;
}

.sidebar-nav {
    flex: 1;
    padding: 1.25rem 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 0.25rem;
    padding: 0 0.75rem;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.25rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
    gap: 0.875rem;
    font-weight: var(--font-weight-medium);
    position: relative;
    overflow: hidden;
}

.sidebar-nav a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0.25rem;
    background: var(--gradient-primary);
    border-radius: 0.125rem;
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
}

.sidebar-nav a:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
}

.sidebar-nav li.active a {
    background-color: rgba(0, 86, 179, 0.08);
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

.sidebar-nav li.active a::before {
    opacity: 1;
}

body.dark-theme .sidebar-nav li.active a {
    background-color: rgba(0, 86, 179, 0.15);
}

.sidebar-nav span.material-icons-round {
    font-size: 1.25rem;
}

.sidebar-footer {
    padding: 1.25rem;
    border-top: 1px solid var(--border-color);
}

.theme-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    width: 100%;
    transition: all var(--transition-speed) ease;
    margin-bottom: 1rem;
    font-weight: var(--font-weight-medium);
}

.theme-toggle:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-light);
    color: var(--primary-color);
}

.system-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.8125rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    background-color: var(--hover-color);
    margin-bottom: 1rem;
}

.logout-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background-color: rgba(230, 57, 70, 0.1);
    border: 1px solid rgba(230, 57, 70, 0.2);
    color: #e63946;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    width: 100%;
    transition: all var(--transition-speed) ease;
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    justify-content: center;
}

.logout-button:hover {
    background-color: rgba(230, 57, 70, 0.15);
    border-color: rgba(230, 57, 70, 0.3);
}

body.dark-theme .logout-button {
    background-color: rgba(230, 57, 70, 0.15);
    border-color: rgba(230, 57, 70, 0.2);
}

.status-indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background-color: var(--success-color);
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: -0.125rem;
    left: -0.125rem;
    right: -0.125rem;
    bottom: -0.125rem;
    border-radius: 50%;
    background-color: var(--success-color);
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.offline {
    background-color: var(--accent-color);
}

.status-indicator.warning {
    background-color: var(--warning-color);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    70% {
        transform: scale(1.5);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 1.5rem;
    transition: all var(--transition-speed) ease;
    max-width: calc(100% - 280px);
}

/* Top Bar */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1.25rem 1.5rem;
    background-color: var(--card-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.top-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0.25rem;
    background: var(--gradient-primary);
}

.page-title {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 2rem;
}

.title-section {
    display: flex;
    flex-direction: column;
}

.page-title h1 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: 0.25rem;
    letter-spacing: -0.01em;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.status-section {
    display: flex;
    gap: 1.5rem;
    margin-left: auto;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--card-color);
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.status-icon {
    font-size: 1.25rem;
    color: var(--text-secondary);
}

.status-icon.online {
    color: var(--success-color);
}

.status-details {
    display: flex;
    flex-direction: column;
}

.status-label {
    font-size: 0.6875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-value {
    font-size: 0.8125rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

/* Legacy styling for backward compatibility */
.last-updated {
    font-size: 0.8125rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.last-updated::before {
    content: '';
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--success-color);
    border-radius: 50%;
    margin-right: 0.25rem;
}

.top-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.control-buttons {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.time-navigation {
    display: flex;
    align-items: center;
    background-color: rgba(0, 86, 179, 0.05);
    border-radius: var(--border-radius-lg);
    padding: 0.25rem;
    margin-right: 0.5rem;
    border: 1px solid rgba(0, 86, 179, 0.1);
    box-shadow: var(--shadow-sm);
}

#timeDisplay {
    padding: 0.5rem 0.875rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin: 0 0.25rem;
    font-size: 0.875rem;
    min-width: 7.5rem;
    text-align: center;
}

.icon-button {
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
}

.icon-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    opacity: 0;
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.icon-button:hover::before {
    opacity: 0.1;
    transform: scale(1);
}

.icon-button:hover {
    color: var(--primary-color);
}

.control-button {
    padding: 0.625rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-color);
    color: var(--text-color);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    font-weight: var(--font-weight-medium);
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.control-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
    z-index: -1;
}

.control-button:hover {
    border-color: var(--primary-light);
    color: var(--primary-color);
}

.control-button.primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 86, 179, 0.3);
}

.control-button.primary::before {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    opacity: 0;
}

.control-button.primary:hover {
    box-shadow: 0 4px 10px rgba(0, 86, 179, 0.4);
    transform: translateY(-2px);
    color: white;
}

.control-button.primary:hover::before {
    opacity: 1;
}

.control-button.secondary {
    background: var(--gradient-secondary);
    color: white;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 166, 126, 0.3);
}

.control-button.secondary::before {
    background: linear-gradient(135deg, var(--secondary-color), #00875a);
    opacity: 0;
}

.control-button.secondary:hover {
    box-shadow: 0 4px 10px rgba(0, 166, 126, 0.4);
    transform: translateY(-2px);
    color: white;
}

.control-button.secondary:hover::before {
    opacity: 1;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.summary-card {
    background-color: var(--card-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    transition: all var(--transition-speed) ease;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.summary-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0.25rem;
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
}

.summary-card.voltage::after {
    background: var(--gradient-primary);
}

.summary-card.current::after {
    background: var(--gradient-accent);
}

.summary-card.power::after {
    background: var(--gradient-secondary);
}

.summary-card.frequency::after {
    background: var(--gradient-warning);
}

.summary-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.summary-card:hover::after {
    opacity: 1;
}

.card-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.25rem;
    position: relative;
    z-index: 1;
}

.card-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    opacity: 0.15;
    z-index: -1;
}

.summary-card.voltage .card-icon {
    color: var(--primary-color);
}

.summary-card.voltage .card-icon::before {
    background: var(--primary-color);
}

.summary-card.current .card-icon {
    color: var(--accent-color);
}

.summary-card.current .card-icon::before {
    background: var(--accent-color);
}

.summary-card.power .card-icon {
    color: var(--secondary-color);
}

.summary-card.power .card-icon::before {
    background: var(--secondary-color);
}

.summary-card.frequency .card-icon {
    color: var(--warning-color);
}

.summary-card.frequency .card-icon::before {
    background: var(--warning-color);
}

.card-icon span {
    font-size: 1.75rem;
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 0.9375rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.03em;
}

.card-value {
    font-size: 1.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: 0.5rem;
    letter-spacing: -0.01em;
}

.trend-indicator {
    display: flex;
    align-items: center;
    font-size: 0.8125rem;
    font-weight: var(--font-weight-medium);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    width: fit-content;
}

.trend-indicator.up {
    color: var(--success-color);
    background-color: rgba(0, 135, 90, 0.1);
}

.trend-indicator.down {
    color: var(--accent-color);
    background-color: rgba(230, 57, 70, 0.1);
}

.trend-indicator.stable {
    color: var(--warning-color);
    background-color: rgba(249, 168, 38, 0.1);
}

.trend-indicator span.material-icons-round {
    font-size: 1rem;
    margin-right: 0.25rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(640px, 1fr));
    gap: 1.5rem;
}

/* Graph Widget */
.graph-widget {
    background-color: var(--card-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-speed) ease;
    height: 420px;
    border: 1px solid var(--border-color);
    position: relative;
}

.graph-widget:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    background-color: rgba(241, 245, 249, 0.3);
}

body.dark-theme .widget-header {
    background-color: rgba(30, 41, 59, 0.3);
}

.widget-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.widget-title span {
    color: var(--primary-color);
    font-size: 1.25rem;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 86, 179, 0.1);
}

.widget-title h3 {
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    letter-spacing: -0.01em;
}

.widget-controls {
    display: flex;
    gap: 0.375rem;
    background-color: var(--card-color);
    padding: 0.25rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.widget-control {
    width: 2rem;
    height: 2rem;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
}

.widget-control::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: currentColor;
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
    z-index: -1;
}

.widget-control:hover {
    color: var(--primary-color);
}

.widget-control:hover::before {
    opacity: 0.1;
}

.widget-control[data-paused="true"] {
    color: var(--accent-color);
}

.widget-control[data-paused="true"]::before {
    opacity: 0.1;
}

.widget-control[data-auto-scroll="true"] {
    color: var(--primary-color);
}

.widget-control[data-auto-scroll="true"]::before {
    opacity: 0.1;
}

.widget-content {
    height: calc(100% - 4.25rem);
    position: relative;
    padding: 0.5rem;
}

canvas {
    width: 100% !important;
    height: 100% !important;
    border-radius: var(--border-radius);
}

/* Instant Values */
.instant-values {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    z-index: 10;
    font-size: 0.8125rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(8px);
    max-width: 12rem;
    transition: all var(--transition-speed) ease;
}

.instant-values:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

body.dark-theme .instant-values {
    background-color: rgba(30, 41, 59, 0.95);
}

.instant-values div {
    display: flex;
    justify-content: space-between;
    gap: 0.75rem;
    padding-bottom: 0.375rem;
    border-bottom: 1px dashed var(--border-color);
}

.instant-values div:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.instant-values span {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
}

/* Status Indicator */
#statusIndicator {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    padding: 0.75rem 1.25rem;
    background-color: var(--card-color);
    color: var(--text-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    max-width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: none;
    border: 1px solid var(--border-color);
    transform: translateY(0);
}

#statusIndicator.error {
    background-color: rgba(230, 57, 70, 0.1);
    color: var(--accent-color);
    border-color: rgba(230, 57, 70, 0.3);
}

#statusIndicator.warning {
    background-color: rgba(249, 168, 38, 0.1);
    color: var(--warning-color);
    border-color: rgba(249, 168, 38, 0.3);
}

#statusIndicator.success {
    background-color: rgba(0, 135, 90, 0.1);
    color: var(--success-color);
    border-color: rgba(0, 135, 90, 0.3);
}

#statusIndicator::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0.25rem;
}

#statusIndicator.error::before {
    background: var(--gradient-accent);
}

#statusIndicator.warning::before {
    background: var(--gradient-warning);
}

#statusIndicator.success::before {
    background: var(--gradient-success);
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 380px;
}

.notification {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 1rem 1.25rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    animation: slideIn 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
    position: relative;
    overflow: hidden;
    max-width: 100%;
    border: 1px solid var(--border-color);
}

.notification::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0.25rem;
}

.notification.info::before {
    background: var(--gradient-primary);
}

.notification.success::before {
    background: var(--gradient-success);
}

.notification.warning::before {
    background: var(--gradient-warning);
}

.notification.error::before {
    background: var(--gradient-accent);
}

.notification-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    position: relative;
}

.notification-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    opacity: 0.15;
    z-index: -1;
}

.notification.info .notification-icon {
    color: var(--primary-color);
}

.notification.info .notification-icon::before {
    background-color: var(--primary-color);
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.success .notification-icon::before {
    background-color: var(--success-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.warning .notification-icon::before {
    background-color: var(--warning-color);
}

.notification.error .notification-icon {
    color: var(--accent-color);
}

.notification.error .notification-icon::before {
    background-color: var(--accent-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: var(--font-weight-semibold);
    font-size: 0.9375rem;
    margin-bottom: 0.375rem;
    color: var(--text-color);
}

.notification-message {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.125rem;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 50%;
    transition: all var(--transition-speed) ease;
}

.notification-close:hover {
    background-color: var(--hover-color);
    color: var(--accent-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 23, 42, 0.75);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--card-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 640px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalIn 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
    border: 1px solid var(--border-color);
}

.modal-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background-color: var(--card-color);
    z-index: 1;
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    letter-spacing: -0.01em;
}

.close-modal {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.5rem;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    transition: all var(--transition-speed) ease;
}

.close-modal:hover {
    background-color: var(--hover-color);
    color: var(--accent-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    position: sticky;
    bottom: 0;
    background-color: var(--card-color);
    z-index: 1;
}

.modal-button {
    padding: 0.625rem 1.25rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: var(--font-weight-medium);
    font-size: 0.875rem;
    transition: all var(--transition-speed) ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-button.primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
}

.modal-button.primary:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.modal-button.secondary {
    background-color: var(--card-color);
    color: var(--text-color);
}

.modal-button.secondary:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-light);
}

@keyframes modalIn {
    from {
        transform: translateY(30px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* Settings */
.settings-section {
    margin-bottom: 2rem;
}

.settings-section h3 {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    letter-spacing: -0.01em;
}

.setting-item {
    margin-bottom: 1.25rem;
}

.setting-item label {
    display: block;
    font-size: 0.9375rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.setting-item input[type="number"],
.setting-item input[type="text"],
.setting-item input[type="datetime-local"],
.setting-item select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9375rem;
    color: var(--text-color);
    background-color: var(--card-color);
    transition: all var(--transition-speed) ease;
    box-shadow: var(--shadow-sm);
}

.setting-item input[type="range"] {
    width: 100%;
    height: 0.5rem;
    -webkit-appearance: none;
    background: var(--hover-color);
    border-radius: 0.25rem;
    outline: none;
    margin: 0.75rem 0;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    border: 2px solid white;
}

.setting-item input:focus,
.setting-item select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.15);
    outline: none;
}

.setting-item.checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.setting-item.checkbox input {
    width: 1.125rem;
    height: 1.125rem;
    accent-color: var(--primary-color);
}

.setting-item.checkbox label {
    margin-bottom: 0;
    cursor: pointer;
}

/* Export Options */
.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.export-option {
    position: relative;
}

.export-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.export-option label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    box-shadow: var(--shadow-sm);
    height: 100%;
}

.export-option label:hover {
    border-color: var(--primary-light);
    background-color: var(--hover-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.export-option input[type="radio"]:checked + label {
    border-color: var(--primary-color);
    background-color: rgba(0, 86, 179, 0.05);
    box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.15);
}

.export-option label span.material-icons-round {
    font-size: 1.5rem;
    color: var(--primary-color);
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 86, 179, 0.1);
}

.export-option label h4 {
    font-size: 0.9375rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.export-option label p {
    font-size: 0.8125rem;
    color: var(--text-secondary);
}

.date-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Fullscreen Widget */
.fullscreen-widget {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--background-color);
    z-index: 1001;
    display: none;
    flex-direction: column;
}

.fullscreen-widget.active {
    display: flex;
}

.fullscreen-header {
    padding: 1.25rem 1.5rem;
    background-color: var(--card-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-md);
}

.fullscreen-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.fullscreen-title span {
    color: var(--primary-color);
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 86, 179, 0.1);
}

.fullscreen-title h3 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    letter-spacing: -0.01em;
}

.fullscreen-controls {
    display: flex;
    gap: 0.5rem;
}

.fullscreen-control {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.fullscreen-control:hover {
    background-color: var(--hover-color);
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.fullscreen-content {
    flex: 1;
    padding: 1.5rem;
    position: relative;
    display: flex;
    flex-direction: column;
}

.fullscreen-content canvas {
    flex: 1;
    width: 100%;
    height: auto !important;
    max-height: calc(100vh - 10rem);
}

/* Responsive Design */
@media (max-width: 1400px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .graph-widget {
        height: 380px;
    }
}

@media (max-width: 1200px) {
    :root {
        --content-width: 100%;
    }
}

@media (max-width: 992px) {
    .sidebar {
        width: 5rem;
        overflow: hidden;
    }

    .sidebar-header h2,
    .sidebar-nav a span:not(.material-icons-round),
    .theme-toggle span:not(.material-icons-round),
    .system-status span:not(.status-indicator) {
        display: none;
    }

    .sidebar-header {
        justify-content: center;
        padding: 1.25rem 0;
    }

    .sidebar-nav a {
        justify-content: center;
        padding: 0.875rem 0;
    }

    .sidebar-nav a::before {
        width: 0.25rem;
        height: 1.5rem;
        top: calc(50% - 0.75rem);
    }

    .theme-toggle {
        justify-content: center;
        padding: 0.75rem;
    }

    .system-status {
        justify-content: center;
    }

    .main-content {
        margin-left: 5rem;
        max-width: calc(100% - 5rem);
    }
}

@media (max-width: 768px) {
    .top-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .top-controls {
        width: 100%;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .time-navigation {
        width: 100%;
        justify-content: space-between;
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .control-button {
        flex: 1;
        justify-content: center;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .graph-widget {
        height: 340px;
    }

    .notification-container {
        max-width: calc(100% - 2rem);
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 1rem;
    }

    .modal-content {
        width: 95%;
        max-height: 85vh;
    }

    .date-range {
        grid-template-columns: 1fr;
    }

    .export-options {
        grid-template-columns: 1fr;
    }

    .graph-widget {
        height: 300px;
    }

    .widget-controls {
        flex-wrap: wrap;
    }
}
