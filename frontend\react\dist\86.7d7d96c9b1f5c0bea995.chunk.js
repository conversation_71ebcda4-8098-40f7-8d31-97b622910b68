"use strict";(self.webpackChunkpower_monitor_pro_dashboard=self.webpackChunkpower_monitor_pro_dashboard||[]).push([[86],{705:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  "default": () => (/* binding */ modals_ThemeBuilderModal)\n});\n\n// EXTERNAL MODULE: ./node_modules/react/index.js\nvar react = __webpack_require__(540);\nvar react_default = /*#__PURE__*/__webpack_require__.n(react);\n// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 1 modules\nvar styled_components_browser_esm = __webpack_require__(523);\n// EXTERNAL MODULE: ./src/components/modals/Modal.js\nvar Modal = __webpack_require__(515);\n// EXTERNAL MODULE: ./src/contexts/ThemeContext.js\nvar ThemeContext = __webpack_require__(223);\n;// ./src/utils/uiUtils.js\nfunction _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }\nfunction _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }\n/**\n * Utility functions for UI and theming\n */\n\n/**\n * Generate a custom theme based on primary color\n * @param {string} primaryColor - Primary color in hex format\n * @param {boolean} isDark - Whether to generate a dark theme\n * @returns {Object} - Theme object with color variables\n */\nvar generateTheme = function generateTheme() {\n  var primaryColor = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \'#2563eb\';\n  var isDark = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  // Convert hex to RGB for calculations\n  var hexToRgb = function hexToRgb(hex) {\n    var result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n    return result ? {\n      r: parseInt(result[1], 16),\n      g: parseInt(result[2], 16),\n      b: parseInt(result[3], 16)\n    } : null;\n  };\n\n  // Adjust color brightness\n  var adjustBrightness = function adjustBrightness(hex, factor) {\n    var rgb = hexToRgb(hex);\n    if (!rgb) return hex;\n    var newRgb = {\n      r: Math.max(0, Math.min(255, Math.round(rgb.r * factor))),\n      g: Math.max(0, Math.min(255, Math.round(rgb.g * factor))),\n      b: Math.max(0, Math.min(255, Math.round(rgb.b * factor)))\n    };\n    return "#".concat(newRgb.r.toString(16).padStart(2, \'0\')).concat(newRgb.g.toString(16).padStart(2, \'0\')).concat(newRgb.b.toString(16).padStart(2, \'0\'));\n  };\n\n  // Generate complementary colors\n  var rgbPrimary = hexToRgb(primaryColor);\n  var hslPrimary = rgbToHsl(rgbPrimary.r, rgbPrimary.g, rgbPrimary.b);\n\n  // Generate secondary color (120° shift)\n  var hslSecondary = _objectSpread(_objectSpread({}, hslPrimary), {}, {\n    h: (hslPrimary.h + 120) % 360\n  });\n  var rgbSecondary = hslToRgb(hslSecondary.h, hslSecondary.s, hslSecondary.l);\n  var secondaryColor = rgbToHex(rgbSecondary.r, rgbSecondary.g, rgbSecondary.b);\n\n  // Generate accent color (180° shift)\n  var hslAccent = _objectSpread(_objectSpread({}, hslPrimary), {}, {\n    h: (hslPrimary.h + 180) % 360\n  });\n  var rgbAccent = hslToRgb(hslAccent.h, hslAccent.s, hslAccent.l);\n  var accentColor = rgbToHex(rgbAccent.r, rgbAccent.g, rgbAccent.b);\n\n  // Generate warning color (60° shift)\n  var hslWarning = _objectSpread(_objectSpread({}, hslPrimary), {}, {\n    h: (hslPrimary.h + 60) % 360\n  });\n  var rgbWarning = hslToRgb(hslWarning.h, hslWarning.s, hslWarning.l);\n  var warningColor = rgbToHex(rgbWarning.r, rgbWarning.g, rgbWarning.b);\n\n  // Generate shades\n  var primaryDark = adjustBrightness(primaryColor, 0.8);\n  var primaryLight = adjustBrightness(primaryColor, 1.2);\n\n  // Base theme\n  var baseTheme = {\n    primaryColor: primaryColor,\n    primaryDark: primaryDark,\n    primaryLight: primaryLight,\n    secondaryColor: secondaryColor,\n    accentColor: accentColor,\n    warningColor: warningColor,\n    successColor: secondaryColor,\n    // Use secondary as success\n    infoColor: primaryLight\n  };\n\n  // Light or dark theme specific colors\n  if (isDark) {\n    return _objectSpread(_objectSpread({}, baseTheme), {}, {\n      backgroundColor: \'#0f172a\',\n      cardColor: \'#1e293b\',\n      borderColor: \'#334155\',\n      textColor: \'#f8fafc\',\n      textSecondary: \'#cbd5e1\',\n      textMuted: \'#94a3b8\',\n      hoverColor: \'#273549\'\n    });\n  } else {\n    return _objectSpread(_objectSpread({}, baseTheme), {}, {\n      backgroundColor: \'#f8fafc\',\n      cardColor: \'#ffffff\',\n      borderColor: \'#e2e8f0\',\n      textColor: \'#0f172a\',\n      textSecondary: \'#475569\',\n      textMuted: \'#94a3b8\',\n      hoverColor: \'#f1f5f9\'\n    });\n  }\n};\n\n/**\n * Apply theme to document by setting CSS variables\n * @param {Object} theme - Theme object with color variables\n */\nvar applyTheme = function applyTheme(theme) {\n  var root = document.documentElement;\n\n  // Apply each theme property as a CSS variable\n  Object.entries(theme).forEach(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      key = _ref2[0],\n      value = _ref2[1];\n    // Convert camelCase to kebab-case for CSS variables\n    var cssKey = key.replace(/([A-Z])/g, \'-$1\').toLowerCase();\n    root.style.setProperty("--".concat(cssKey), value);\n  });\n};\n\n/**\n * Convert RGB to HSL\n * @param {number} r - Red (0-255)\n * @param {number} g - Green (0-255)\n * @param {number} b - Blue (0-255)\n * @returns {Object} - HSL values\n */\nfunction rgbToHsl(r, g, b) {\n  r /= 255;\n  g /= 255;\n  b /= 255;\n  var max = Math.max(r, g, b);\n  var min = Math.min(r, g, b);\n  var h,\n    s,\n    l = (max + min) / 2;\n  if (max === min) {\n    h = s = 0; // achromatic\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h * 360,\n    s: s,\n    l: l\n  };\n}\n\n/**\n * Convert HSL to RGB\n * @param {number} h - Hue (0-360)\n * @param {number} s - Saturation (0-1)\n * @param {number} l - Lightness (0-1)\n * @returns {Object} - RGB values\n */\nfunction hslToRgb(h, s, l) {\n  h /= 360;\n  var r, g, b;\n  if (s === 0) {\n    r = g = b = l; // achromatic\n  } else {\n    var hue2rgb = function hue2rgb(p, q, t) {\n      if (t < 0) t += 1;\n      if (t > 1) t -= 1;\n      if (t < 1 / 6) return p + (q - p) * 6 * t;\n      if (t < 1 / 2) return q;\n      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n      return p;\n    };\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: Math.round(r * 255),\n    g: Math.round(g * 255),\n    b: Math.round(b * 255)\n  };\n}\n\n/**\n * Convert RGB to hex\n * @param {number} r - Red (0-255)\n * @param {number} g - Green (0-255)\n * @param {number} b - Blue (0-255)\n * @returns {string} - Hex color\n */\nfunction rgbToHex(r, g, b) {\n  return "#".concat(r.toString(16).padStart(2, \'0\')).concat(g.toString(16).padStart(2, \'0\')).concat(b.toString(16).padStart(2, \'0\'));\n}\n\n/**\n * Check if a color is light or dark\n * @param {string} color - Color in hex format\n * @returns {boolean} - True if color is light\n */\nvar isLightColor = function isLightColor(color) {\n  var rgb = hexToRgb(color);\n  if (!rgb) return true;\n\n  // Calculate relative luminance\n  var luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;\n  return luminance > 0.5;\n};\n;// ./src/components/modals/ThemeBuilderModal.js\nvar _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12, _templateObject13, _templateObject14, _templateObject15, _templateObject16, _templateObject17, _templateObject18, _templateObject19;\nfunction _taggedTemplateLiteral(e, t) { return t || (t = e.slice(0)), Object.freeze(Object.defineProperties(e, { raw: { value: Object.freeze(t) } })); }\nfunction ThemeBuilderModal_slicedToArray(r, e) { return ThemeBuilderModal_arrayWithHoles(r) || ThemeBuilderModal_iterableToArrayLimit(r, e) || ThemeBuilderModal_unsupportedIterableToArray(r, e) || ThemeBuilderModal_nonIterableRest(); }\nfunction ThemeBuilderModal_nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction ThemeBuilderModal_unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return ThemeBuilderModal_arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? ThemeBuilderModal_arrayLikeToArray(r, a) : void 0; } }\nfunction ThemeBuilderModal_arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ThemeBuilderModal_iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction ThemeBuilderModal_arrayWithHoles(r) { if (Array.isArray(r)) return r; }\n\n\n\n\n\nvar ThemeBuilderModal = function ThemeBuilderModal(_ref) {\n  var isOpen = _ref.isOpen,\n    onClose = _ref.onClose,\n    addNotification = _ref.addNotification;\n  var _useTheme = (0,ThemeContext/* useTheme */.D)(),\n    darkMode = _useTheme.darkMode;\n  var _useState = (0,react.useState)(\'#2563eb\'),\n    _useState2 = ThemeBuilderModal_slicedToArray(_useState, 2),\n    primaryColor = _useState2[0],\n    setPrimaryColor = _useState2[1];\n  var _useState3 = (0,react.useState)(null),\n    _useState4 = ThemeBuilderModal_slicedToArray(_useState3, 2),\n    customTheme = _useState4[0],\n    setCustomTheme = _useState4[1];\n  var _useState5 = (0,react.useState)(darkMode),\n    _useState6 = ThemeBuilderModal_slicedToArray(_useState5, 2),\n    previewDarkMode = _useState6[0],\n    setPreviewDarkMode = _useState6[1];\n  var _useState7 = (0,react.useState)([{\n      name: \'Default Blue\',\n      color: \'#2563eb\'\n    }, {\n      name: \'Emerald\',\n      color: \'#10b981\'\n    }, {\n      name: \'Ruby\',\n      color: \'#e11d48\'\n    }, {\n      name: \'Amber\',\n      color: \'#f59e0b\'\n    }, {\n      name: \'Violet\',\n      color: \'#8b5cf6\'\n    }, {\n      name: \'Slate\',\n      color: \'#475569\'\n    }]),\n    _useState8 = ThemeBuilderModal_slicedToArray(_useState7, 2),\n    presetThemes = _useState8[0],\n    setPresetThemes = _useState8[1];\n\n  // Generate theme preview when primary color changes\n  (0,react.useEffect)(function () {\n    var theme = generateTheme(primaryColor, previewDarkMode);\n    setCustomTheme(theme);\n  }, [primaryColor, previewDarkMode]);\n\n  // Handle color change\n  var handleColorChange = function handleColorChange(e) {\n    setPrimaryColor(e.target.value);\n  };\n\n  // Handle preset theme selection\n  var handlePresetSelect = function handlePresetSelect(color) {\n    setPrimaryColor(color);\n  };\n\n  // Handle dark mode toggle for preview\n  var handleDarkModeToggle = function handleDarkModeToggle() {\n    setPreviewDarkMode(function (prev) {\n      return !prev;\n    });\n  };\n\n  // Apply theme\n  var handleApplyTheme = function handleApplyTheme() {\n    // Generate and apply theme\n    var theme = generateTheme(primaryColor, darkMode);\n    applyTheme(theme);\n\n    // Save theme to localStorage\n    localStorage.setItem(\'customTheme\', JSON.stringify({\n      primaryColor: primaryColor,\n      timestamp: Date.now()\n    }));\n    addNotification(\'Custom theme applied successfully\', \'success\');\n  };\n\n  // Reset theme to default\n  var handleResetTheme = function handleResetTheme() {\n    // Remove custom theme from localStorage\n    localStorage.removeItem(\'customTheme\');\n\n    // Apply default theme\n    var defaultTheme = generateTheme(\'#2563eb\', darkMode);\n    applyTheme(defaultTheme);\n    setPrimaryColor(\'#2563eb\');\n    addNotification(\'Theme reset to default\', \'info\');\n  };\n  return /*#__PURE__*/react_default().createElement(Modal/* default */.A, {\n    isOpen: isOpen,\n    onClose: onClose,\n    title: "Theme Builder"\n  }, /*#__PURE__*/react_default().createElement(ThemeBuilderContent, null, /*#__PURE__*/react_default().createElement(ThemeSection, null, /*#__PURE__*/react_default().createElement("h3", null, "Choose Primary Color"), /*#__PURE__*/react_default().createElement(ColorPickerContainer, null, /*#__PURE__*/react_default().createElement(ColorPicker, {\n    type: "color",\n    value: primaryColor,\n    onChange: handleColorChange\n  }), /*#__PURE__*/react_default().createElement(ColorCode, null, primaryColor))), /*#__PURE__*/react_default().createElement(ThemeSection, null, /*#__PURE__*/react_default().createElement("h3", null, "Preset Themes"), /*#__PURE__*/react_default().createElement(PresetContainer, null, presetThemes.map(function (theme) {\n    return /*#__PURE__*/react_default().createElement(PresetColor, {\n      key: theme.name,\n      style: {\n        backgroundColor: theme.color\n      },\n      onClick: function onClick() {\n        return handlePresetSelect(theme.color);\n      },\n      className: primaryColor === theme.color ? \'active\' : \'\',\n      title: theme.name\n    });\n  }))), /*#__PURE__*/react_default().createElement(ThemeSection, null, /*#__PURE__*/react_default().createElement("h3", null, "Preview"), /*#__PURE__*/react_default().createElement(PreviewToggle, null, /*#__PURE__*/react_default().createElement("span", null, "Light Mode"), /*#__PURE__*/react_default().createElement(ToggleSwitch, {\n    type: "checkbox",\n    checked: previewDarkMode,\n    onChange: handleDarkModeToggle\n  }), /*#__PURE__*/react_default().createElement("span", null, "Dark Mode")), /*#__PURE__*/react_default().createElement(ThemePreview, {\n    style: {\n      backgroundColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.backgroundColor,\n      color: customTheme === null || customTheme === void 0 ? void 0 : customTheme.textColor,\n      borderColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.borderColor\n    }\n  }, /*#__PURE__*/react_default().createElement(PreviewHeader, {\n    style: {\n      borderColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.borderColor\n    }\n  }, /*#__PURE__*/react_default().createElement("h4", null, "Theme Preview")), /*#__PURE__*/react_default().createElement(PreviewContent, null, /*#__PURE__*/react_default().createElement(PreviewCard, {\n    style: {\n      backgroundColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.cardColor,\n      color: customTheme === null || customTheme === void 0 ? void 0 : customTheme.textColor,\n      borderColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.borderColor\n    }\n  }, /*#__PURE__*/react_default().createElement(PreviewCardTitle, {\n    style: {\n      color: customTheme === null || customTheme === void 0 ? void 0 : customTheme.primaryColor\n    }\n  }, "Card Title"), /*#__PURE__*/react_default().createElement(PreviewCardContent, {\n    style: {\n      color: customTheme === null || customTheme === void 0 ? void 0 : customTheme.textSecondary\n    }\n  }, "This is a preview of your custom theme."), /*#__PURE__*/react_default().createElement(PreviewButton, {\n    style: {\n      backgroundColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.primaryColor,\n      color: \'white\'\n    }\n  }, "Button")), /*#__PURE__*/react_default().createElement(PreviewElements, null, /*#__PURE__*/react_default().createElement(PreviewElement, null, /*#__PURE__*/react_default().createElement("span", null, "Primary:"), /*#__PURE__*/react_default().createElement(ColorSwatch, {\n    style: {\n      backgroundColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.primaryColor\n    }\n  })), /*#__PURE__*/react_default().createElement(PreviewElement, null, /*#__PURE__*/react_default().createElement("span", null, "Secondary:"), /*#__PURE__*/react_default().createElement(ColorSwatch, {\n    style: {\n      backgroundColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.secondaryColor\n    }\n  })), /*#__PURE__*/react_default().createElement(PreviewElement, null, /*#__PURE__*/react_default().createElement("span", null, "Accent:"), /*#__PURE__*/react_default().createElement(ColorSwatch, {\n    style: {\n      backgroundColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.accentColor\n    }\n  })), /*#__PURE__*/react_default().createElement(PreviewElement, null, /*#__PURE__*/react_default().createElement("span", null, "Background:"), /*#__PURE__*/react_default().createElement(ColorSwatch, {\n    style: {\n      backgroundColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.backgroundColor\n    }\n  })), /*#__PURE__*/react_default().createElement(PreviewElement, null, /*#__PURE__*/react_default().createElement("span", null, "Card:"), /*#__PURE__*/react_default().createElement(ColorSwatch, {\n    style: {\n      backgroundColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.cardColor\n    }\n  })), /*#__PURE__*/react_default().createElement(PreviewElement, null, /*#__PURE__*/react_default().createElement("span", null, "Text:"), /*#__PURE__*/react_default().createElement(ColorSwatch, {\n    style: {\n      backgroundColor: customTheme === null || customTheme === void 0 ? void 0 : customTheme.textColor\n    }\n  }))))))), /*#__PURE__*/react_default().createElement(ModalFooter, null, /*#__PURE__*/react_default().createElement(ModalButton, {\n    className: "secondary",\n    onClick: handleResetTheme\n  }, "Reset to Default"), /*#__PURE__*/react_default().createElement(ModalButton, {\n    onClick: handleApplyTheme\n  }, "Apply Theme")));\n};\nvar ThemeBuilderContent = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = _taggedTemplateLiteral(["\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2rem;\\n"])));\nvar ThemeSection = styled_components_browser_esm/* default */.Ay.div(_templateObject2 || (_templateObject2 = _taggedTemplateLiteral(["\\n  h3 {\\n    font-size: 1.125rem;\\n    font-weight: var(--font-weight-semibold);\\n    color: var(--text-color);\\n    margin-bottom: 1.25rem;\\n    padding-bottom: 0.75rem;\\n    border-bottom: 1px solid var(--border-color);\\n    letter-spacing: -0.01em;\\n  }\\n"])));\nvar ColorPickerContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = _taggedTemplateLiteral(["\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n"])));\nvar ColorPicker = styled_components_browser_esm/* default */.Ay.input(_templateObject4 || (_templateObject4 = _taggedTemplateLiteral(["\\n  width: 4rem;\\n  height: 4rem;\\n  border: none;\\n  border-radius: var(--border-radius);\\n  cursor: pointer;\\n  overflow: hidden;\\n  \\n  &::-webkit-color-swatch-wrapper {\\n    padding: 0;\\n  }\\n  \\n  &::-webkit-color-swatch {\\n    border: none;\\n    border-radius: var(--border-radius);\\n  }\\n"])));\nvar ColorCode = styled_components_browser_esm/* default */.Ay.div(_templateObject5 || (_templateObject5 = _taggedTemplateLiteral(["\\n  font-family: monospace;\\n  font-size: 1rem;\\n  padding: 0.5rem 1rem;\\n  background-color: var(--hover-color);\\n  border-radius: var(--border-radius);\\n  color: var(--text-color);\\n  border: 1px solid var(--border-color);\\n"])));\nvar PresetContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject6 || (_templateObject6 = _taggedTemplateLiteral(["\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n"])));\nvar PresetColor = styled_components_browser_esm/* default */.Ay.div(_templateObject7 || (_templateObject7 = _taggedTemplateLiteral(["\\n  width: 3rem;\\n  height: 3rem;\\n  border-radius: var(--border-radius);\\n  cursor: pointer;\\n  transition: all var(--animation-medium) ease;\\n  border: 2px solid transparent;\\n  \\n  &:hover {\\n    transform: scale(1.1);\\n  }\\n  \\n  &.active {\\n    border-color: var(--text-color);\\n    box-shadow: 0 0 0 2px white, 0 0 0 4px var(--border-color);\\n  }\\n"])));\nvar PreviewToggle = styled_components_browser_esm/* default */.Ay.div(_templateObject8 || (_templateObject8 = _taggedTemplateLiteral(["\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  margin-bottom: 1.25rem;\\n  \\n  span {\\n    font-size: 0.875rem;\\n    color: var(--text-secondary);\\n  }\\n"])));\nvar ToggleSwitch = styled_components_browser_esm/* default */.Ay.input(_templateObject9 || (_templateObject9 = _taggedTemplateLiteral(["\\n  width: 3rem;\\n  height: 1.5rem;\\n  appearance: none;\\n  background-color: var(--border-color);\\n  border-radius: 9999px;\\n  position: relative;\\n  cursor: pointer;\\n  transition: all var(--animation-medium) ease;\\n  \\n  &:checked {\\n    background-color: var(--primary-color);\\n  }\\n  \\n  &::before {\\n    content: \'\';\\n    position: absolute;\\n    top: 0.125rem;\\n    left: 0.125rem;\\n    width: 1.25rem;\\n    height: 1.25rem;\\n    background-color: white;\\n    border-radius: 50%;\\n    transition: all var(--animation-medium) ease;\\n  }\\n  \\n  &:checked::before {\\n    left: 1.625rem;\\n  }\\n"])));\nvar ThemePreview = styled_components_browser_esm/* default */.Ay.div(_templateObject0 || (_templateObject0 = _taggedTemplateLiteral(["\\n  border: 1px solid;\\n  border-radius: var(--border-radius-lg);\\n  overflow: hidden;\\n"])));\nvar PreviewHeader = styled_components_browser_esm/* default */.Ay.div(_templateObject1 || (_templateObject1 = _taggedTemplateLiteral(["\\n  padding: 1rem;\\n  border-bottom: 1px solid;\\n  \\n  h4 {\\n    font-size: 1rem;\\n    font-weight: var(--font-weight-semibold);\\n  }\\n"])));\nvar PreviewContent = styled_components_browser_esm/* default */.Ay.div(_templateObject10 || (_templateObject10 = _taggedTemplateLiteral(["\\n  padding: 1.5rem;\\n  display: flex;\\n  gap: 1.5rem;\\n  \\n  @media (max-width: 768px) {\\n    flex-direction: column;\\n  }\\n"])));\nvar PreviewCard = styled_components_browser_esm/* default */.Ay.div(_templateObject11 || (_templateObject11 = _taggedTemplateLiteral(["\\n  flex: 1;\\n  padding: 1.25rem;\\n  border-radius: var(--border-radius);\\n  border: 1px solid;\\n"])));\nvar PreviewCardTitle = styled_components_browser_esm/* default */.Ay.h5(_templateObject12 || (_templateObject12 = _taggedTemplateLiteral(["\\n  font-size: 1.125rem;\\n  font-weight: var(--font-weight-semibold);\\n  margin-bottom: 0.75rem;\\n"])));\nvar PreviewCardContent = styled_components_browser_esm/* default */.Ay.p(_templateObject13 || (_templateObject13 = _taggedTemplateLiteral(["\\n  font-size: 0.875rem;\\n  margin-bottom: 1.25rem;\\n"])));\nvar PreviewButton = styled_components_browser_esm/* default */.Ay.button(_templateObject14 || (_templateObject14 = _taggedTemplateLiteral(["\\n  padding: 0.5rem 1rem;\\n  border: none;\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  font-weight: var(--font-weight-medium);\\n  cursor: pointer;\\n"])));\nvar PreviewElements = styled_components_browser_esm/* default */.Ay.div(_templateObject15 || (_templateObject15 = _taggedTemplateLiteral(["\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n"])));\nvar PreviewElement = styled_components_browser_esm/* default */.Ay.div(_templateObject16 || (_templateObject16 = _taggedTemplateLiteral(["\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  \\n  span {\\n    font-size: 0.875rem;\\n  }\\n"])));\nvar ColorSwatch = styled_components_browser_esm/* default */.Ay.div(_templateObject17 || (_templateObject17 = _taggedTemplateLiteral(["\\n  width: 2rem;\\n  height: 1.25rem;\\n  border-radius: var(--border-radius-sm);\\n  border: 1px solid var(--border-color);\\n"])));\nvar ModalFooter = styled_components_browser_esm/* default */.Ay.div(_templateObject18 || (_templateObject18 = _taggedTemplateLiteral(["\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 1rem;\\n  margin-top: 2rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid var(--border-color);\\n"])));\nvar ModalButton = styled_components_browser_esm/* default */.Ay.button(_templateObject19 || (_templateObject19 = _taggedTemplateLiteral(["\\n  padding: 0.75rem 1.5rem;\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  border-radius: var(--border-radius);\\n  font-weight: var(--font-weight-medium);\\n  cursor: pointer;\\n  transition: all var(--animation-medium) ease;\\n  \\n  &:hover {\\n    background-color: var(--primary-dark);\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-md);\\n  }\\n  \\n  &.secondary {\\n    background-color: transparent;\\n    color: var(--text-color);\\n    border: 1px solid var(--border-color);\\n    \\n    &:hover {\\n      background-color: var(--hover-color);\\n      border-color: var(--text-secondary);\\n    }\\n  }\\n"])));\n/* harmony default export */ const modals_ThemeBuilderModal = (ThemeBuilderModal);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///705\n')}}]);