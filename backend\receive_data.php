<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

try {
    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get JSON input
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON data');
        }
        
        // Validate required fields
        $required_fields = [
            'voltage_1', 'voltage_2', 'voltage_3',
            'current_1', 'current_2', 'current_3',
            'pf_1', 'pf_2', 'pf_3',
            'kva_1', 'kva_2', 'kva_3',
            'total_kva', 'total_kw', 'total_kvar',
            'frequency'
        ];
        
        foreach ($required_fields as $field) {
            if (!isset($data[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }
        
        // Set timezone to India and get current timestamp
        date_default_timezone_set('Asia/Kolkata');
        $timestamp = date('Y-m-d H:i:s');
        
        // Prepare SQL statement
        $stmt = $conn->prepare("INSERT INTO electrical_data (
            voltage_1, voltage_2, voltage_3,
            current_1, current_2, current_3,
            pf_1, pf_2, pf_3,
            kva_1, kva_2, kva_3,
            total_kva, total_kw, total_kvar,
            frequency, timestamp
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        // Bind parameters
        $stmt->bind_param("dddddddddddddddds",
            $data['voltage_1'], $data['voltage_2'], $data['voltage_3'],
            $data['current_1'], $data['current_2'], $data['current_3'],
            $data['pf_1'], $data['pf_2'], $data['pf_3'],
            $data['kva_1'], $data['kva_2'], $data['kva_3'],
            $data['total_kva'], $data['total_kw'], $data['total_kvar'],
            $data['frequency'], $timestamp
        );
        
        // Execute statement
        if ($stmt->execute()) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Data received and stored successfully',
                'timestamp' => $timestamp,
                'id' => $conn->insert_id
            ]);
        } else {
            throw new Exception("Execute failed: " . $stmt->error);
        }
        
        $stmt->close();
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Return endpoint information for testing
        echo json_encode([
            'status' => 'success',
            'message' => 'ESP32 Data Receiver Endpoint',
            'endpoint' => $_SERVER['REQUEST_URI'],
            'method' => 'POST',
            'content_type' => 'application/json',
            'required_fields' => [
                'voltage_1', 'voltage_2', 'voltage_3',
                'current_1', 'current_2', 'current_3',
                'pf_1', 'pf_2', 'pf_3',
                'kva_1', 'kva_2', 'kva_3',
                'total_kva', 'total_kw', 'total_kvar',
                'frequency'
            ]
        ]);
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
