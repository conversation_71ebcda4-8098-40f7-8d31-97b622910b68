import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Line } from 'react-chartjs-2';

const FullscreenWidget = ({ 
  isOpen, 
  title, 
  icon, 
  chartId, 
  sourceChartId, 
  onClose 
}) => {
  const chartRef = useRef(null);
  
  useEffect(() => {
    if (isOpen && sourceChartId) {
      // Clone the source chart's data and options
      const sourceChart = document.getElementById(sourceChartId)?.chart;
      
      if (sourceChart && chartRef.current) {
        const chart = chartRef.current;
        
        // Copy data and options from source chart
        chart.data = JSON.parse(JSON.stringify(sourceChart.data));
        chart.options = JSON.parse(JSON.stringify(sourceChart.options));
        
        // Update the chart
        chart.update();
      }
    }
  }, [isOpen, sourceChartId]);
  
  if (!isOpen) return null;
  
  return (
    <FullscreenWidgetContainer>
      <FullscreenHeader>
        <FullscreenTitle>
          <span className="material-icons-round">{icon}</span>
          <h3>{title}</h3>
        </FullscreenTitle>
        <FullscreenControls>
          <FullscreenControl onClick={onClose}>
            <span className="material-icons-round">close</span>
          </FullscreenControl>
        </FullscreenControls>
      </FullscreenHeader>
      <FullscreenContent>
        <Line 
          id={chartId ? `fullscreen${chartId}` : 'fullscreenChart'}
          ref={chartRef}
          data={{ datasets: [] }} // Initial empty data, will be populated from source chart
          options={{}} // Initial empty options, will be populated from source chart
        />
      </FullscreenContent>
    </FullscreenWidgetContainer>
  );
};

const FullscreenWidgetContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--background-color);
  z-index: 1001;
  display: flex;
  flex-direction: column;
`;

const FullscreenHeader = styled.div`
  padding: 1.25rem 1.5rem;
  background-color: var(--card-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-md);
`;

const FullscreenTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  
  span {
    color: var(--primary-color);
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 86, 179, 0.1);
  }
  
  h3 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    letter-spacing: -0.01em;
  }
`;

const FullscreenControls = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const FullscreenControl = styled.button`
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  
  &:hover {
    background-color: var(--hover-color);
    color: var(--accent-color);
    border-color: var(--accent-color);
  }
`;

const FullscreenContent = styled.div`
  flex: 1;
  padding: 1.5rem;
  position: relative;
  display: flex;
  flex-direction: column;
  
  canvas {
    flex: 1;
    width: 100%;
    height: auto !important;
    max-height: calc(100vh - 10rem);
  }
`;

export default FullscreenWidget;
