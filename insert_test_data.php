<?php
// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";  // Change this to your database username
$password = "";      // Change this to your database password
$dbname = "esp32_data";  // Change this to your database name

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Test Data Insertion</h1>";

// Check if database exists
$result = $conn->query("SHOW DATABASES LIKE '$dbname'");
if ($result->num_rows == 0) {
    echo "<p>Creating database $dbname...</p>";
    $conn->query("CREATE DATABASE IF NOT EXISTS $dbname");
    $conn->select_db($dbname);
} else {
    $conn->select_db($dbname);
}

// Check if table exists
$result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
if ($result->num_rows == 0) {
    echo "<p>Creating table electrical_data...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS electrical_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        voltage_1 FLOAT,
        voltage_2 FLOAT,
        voltage_3 FLOAT,
        current_1 FLOAT,
        current_2 FLOAT,
        current_3 FLOAT,
        pf_1 FLOAT,
        pf_2 FLOAT,
        pf_3 FLOAT,
        kva_1 FLOAT,
        kva_2 FLOAT,
        kva_3 FLOAT,
        total_kva FLOAT,
        total_kw FLOAT,
        total_kvar FLOAT,
        frequency FLOAT,
        timestamp DATETIME
    )";
    $conn->query($sql);
}

// Insert 10 test data points with timestamps 1 minute apart
echo "<p>Inserting test data...</p>";

$baseTime = time();
$count = 0;
$insertedCount = 0;

for ($i = 0; $i < 10; $i++) {
    $timestamp = date('Y-m-d H:i:s', $baseTime - ($i * 60)); // 1 minute apart
    $angle = $i * 0.1; // For creating sine wave variations
    
    // Create simulated data with some randomness and sine wave patterns
    $voltage_1 = 400 + sin($angle) * 10 + (rand(-5, 5) / 10);
    $voltage_2 = 400 + sin($angle + 2.09) * 10 + (rand(-5, 5) / 10); // 120° phase shift
    $voltage_3 = 400 + sin($angle + 4.19) * 10 + (rand(-5, 5) / 10); // 240° phase shift
    
    $current_1 = 50 + sin($angle) * 5 + (rand(-10, 10) / 10);
    $current_2 = 50 + sin($angle + 2.09) * 5 + (rand(-10, 10) / 10);
    $current_3 = 50 + sin($angle + 4.19) * 5 + (rand(-10, 10) / 10);
    
    $pf_1 = 0.9 + sin($angle) * 0.05 + (rand(-5, 5) / 100);
    $pf_2 = 0.9 + sin($angle + 2.09) * 0.05 + (rand(-5, 5) / 100);
    $pf_3 = 0.9 + sin($angle + 4.19) * 0.05 + (rand(-5, 5) / 100);
    
    $kva_1 = 20 + sin($angle) * 2 + (rand(-5, 5) / 10);
    $kva_2 = 20 + sin($angle + 2.09) * 2 + (rand(-5, 5) / 10);
    $kva_3 = 20 + sin($angle + 4.19) * 2 + (rand(-5, 5) / 10);
    
    // Calculate total values
    $total_kva = $kva_1 + $kva_2 + $kva_3;
    $total_kw = $total_kva * (($pf_1 + $pf_2 + $pf_3) / 3);
    $total_kvar = sqrt(pow($total_kva, 2) - pow($total_kw, 2));
    
    // Add frequency with small variations
    $frequency = 50 + sin($angle) * 0.1 + (rand(-5, 5) / 100);
    
    // Insert data into database
    $stmt = $conn->prepare("INSERT INTO electrical_data (
        voltage_1, voltage_2, voltage_3,
        current_1, current_2, current_3,
        pf_1, pf_2, pf_3,
        kva_1, kva_2, kva_3,
        total_kva, total_kw, total_kvar,
        frequency, timestamp
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->bind_param("dddddddddddddddds", 
        $voltage_1, $voltage_2, $voltage_3,
        $current_1, $current_2, $current_3,
        $pf_1, $pf_2, $pf_3,
        $kva_1, $kva_2, $kva_3,
        $total_kva, $total_kw, $total_kvar,
        $frequency, $timestamp
    );
    
    if ($stmt->execute()) {
        $insertedCount++;
    }
    
    $stmt->close();
}

echo "<p>Inserted $insertedCount test data points.</p>";

// Get the latest record
$result = $conn->query("SELECT * FROM electrical_data ORDER BY timestamp DESC LIMIT 1");
if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    
    echo "<h2>Latest Record</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    
    foreach ($row as $key => $value) {
        echo "<tr>";
        echo "<td>$key</td>";
        echo "<td>$value</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Close the connection
$conn->close();

echo "<p>Done! <a href='frontend/'>Go to Dashboard</a></p>";
?>
