<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

try {
    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Clear all data from the electrical_data table
    $sql = "DELETE FROM electrical_data";
    
    if ($conn->query($sql) === TRUE) {
        // Reset auto-increment counter
        $conn->query("ALTER TABLE electrical_data AUTO_INCREMENT = 1");
        
        echo json_encode([
            'success' => true,
            'message' => 'All data cleared successfully',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        throw new Exception("Error clearing data: " . $conn->error);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
