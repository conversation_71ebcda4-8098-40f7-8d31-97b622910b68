import React, { useState } from 'react';
import styled from 'styled-components';
import Modal from './Modal';
import { useDashboard } from '../../contexts/DashboardContext';
import { availableWidgets } from '../../utils/layoutUtils';

const CustomizeModal = ({ isOpen, onClose, addNotification }) => {
  const { 
    activeWidgets, 
    addWidget, 
    removeWidget, 
    toggleEditMode, 
    editMode,
    resetDashboard
  } = useDashboard();
  
  const [selectedCategory, setSelectedCategory] = useState('all');
  
  // Filter widgets by category
  const filteredWidgets = selectedCategory === 'all' 
    ? availableWidgets 
    : availableWidgets.filter(widget => widget.category === selectedCategory);
  
  // Get unique categories
  const categories = ['all', ...new Set(availableWidgets.map(widget => widget.category))];
  
  // Handle widget toggle
  const handleWidgetToggle = (widgetId) => {
    if (activeWidgets.includes(widgetId)) {
      removeWidget(widgetId);
      addNotification(`Removed ${widgetId} from dashboard`, 'info');
    } else {
      addWidget(widgetId);
      addNotification(`Added ${widgetId} to dashboard`, 'success');
    }
  };
  
  // Handle edit mode toggle
  const handleEditModeToggle = () => {
    toggleEditMode();
    addNotification(`Dashboard edit mode ${editMode ? 'disabled' : 'enabled'}`, 'info');
  };
  
  // Handle dashboard reset
  const handleResetDashboard = () => {
    if (window.confirm('Are you sure you want to reset the dashboard to default layout?')) {
      resetDashboard();
      addNotification('Dashboard reset to default layout', 'success');
    }
  };
  
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      title="Customize Dashboard"
    >
      <CustomizeOptions>
        <OptionSection>
          <h3>Dashboard Layout</h3>
          <OptionButtons>
            <OptionButton 
              onClick={handleEditModeToggle}
              className={editMode ? 'active' : ''}
            >
              <span className="material-icons-round">
                {editMode ? 'lock' : 'dashboard_customize'}
              </span>
              <span>{editMode ? 'Lock Layout' : 'Edit Layout'}</span>
            </OptionButton>
            
            <OptionButton 
              onClick={handleResetDashboard}
              className="warning"
            >
              <span className="material-icons-round">restart_alt</span>
              <span>Reset Layout</span>
            </OptionButton>
          </OptionButtons>
          
          {editMode && (
            <EditModeInstructions>
              <p>
                <span className="material-icons-round">info</span>
                Edit mode is active. You can now drag and resize widgets on the dashboard.
              </p>
            </EditModeInstructions>
          )}
        </OptionSection>
        
        <OptionSection>
          <h3>Available Widgets</h3>
          <CategoryFilter>
            {categories.map(category => (
              <CategoryButton 
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={selectedCategory === category ? 'active' : ''}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </CategoryButton>
            ))}
          </CategoryFilter>
          
          <WidgetGrid>
            {filteredWidgets.map(widget => (
              <WidgetCard 
                key={widget.id}
                className={activeWidgets.includes(widget.id) ? 'active' : ''}
                onClick={() => handleWidgetToggle(widget.id)}
              >
                <WidgetIcon>
                  <span className="material-icons-round">{widget.icon}</span>
                </WidgetIcon>
                <WidgetInfo>
                  <h4>{widget.title}</h4>
                  <p>{widget.description}</p>
                </WidgetInfo>
                <WidgetToggle>
                  <span className="material-icons-round">
                    {activeWidgets.includes(widget.id) ? 'check_circle' : 'add_circle'}
                  </span>
                </WidgetToggle>
              </WidgetCard>
            ))}
          </WidgetGrid>
        </OptionSection>
      </CustomizeOptions>
      
      <ModalFooter>
        <ModalButton 
          onClick={onClose}
        >
          Close
        </ModalButton>
      </ModalFooter>
    </Modal>
  );
};

const CustomizeOptions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const OptionSection = styled.div`
  h3 {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    letter-spacing: -0.01em;
  }
`;

const OptionButtons = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  
  @media (max-width: 576px) {
    flex-direction: column;
  }
`;

const OptionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--card-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:hover {
    background-color: var(--hover-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  &.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  &.warning {
    color: var(--warning-color);
    border-color: var(--warning-color);
    background-color: rgba(245, 158, 11, 0.1);
    
    &:hover {
      background-color: var(--warning-color);
      color: white;
    }
  }
  
  span.material-icons-round {
    font-size: 1.25rem;
  }
`;

const EditModeInstructions = styled.div`
  background-color: rgba(37, 99, 235, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.2);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-bottom: 1rem;
  
  p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-size: 0.875rem;
    
    span.material-icons-round {
      font-size: 1.25rem;
    }
  }
`;

const CategoryFilter = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.25rem;
`;

const CategoryButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: var(--card-color);
  border: 1px solid var(--border-color);
  border-radius: 9999px;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
    border-color: var(--primary-light);
  }
  
  &.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
`;

const WidgetGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  
  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const WidgetCard = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--card-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:hover {
    background-color: var(--hover-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  &.active {
    border-color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
  }
`;

const WidgetIcon = styled.div`
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: rgba(37, 99, 235, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  
  span {
    font-size: 1.25rem;
  }
  
  ${WidgetCard}.active & {
    background-color: var(--primary-color);
    color: white;
  }
`;

const WidgetInfo = styled.div`
  flex: 1;
  
  h4 {
    font-size: 0.9375rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: 0.25rem;
  }
  
  p {
    font-size: 0.8125rem;
    color: var(--text-secondary);
    line-height: 1.4;
  }
`;

const WidgetToggle = styled.div`
  color: var(--text-secondary);
  
  span {
    font-size: 1.5rem;
  }
  
  ${WidgetCard}.active & {
    color: var(--primary-color);
  }
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
`;

const ModalButton = styled.button`
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
`;

export default CustomizeModal;
