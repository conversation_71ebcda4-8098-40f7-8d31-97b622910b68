/**
 * Simple caching utility for API responses
 */

// Cache storage
const cache = new Map();

/**
 * Get data from cache
 * @param {string} key - Cache key
 * @returns {Object|null} - Cached data or null if not found or expired
 */
export const getFromCache = (key) => {
  if (!key) return null;
  
  // Check if we have this key in cache
  if (cache.has(key)) {
    const cachedItem = cache.get(key);
    
    // Check if cache is expired
    if (cachedItem.expiry > Date.now()) {
      console.log(`Cache hit for ${key}`);
      return cachedItem.data;
    } else {
      // Cache expired, remove it
      console.log(`Cache expired for ${key}`);
      cache.delete(key);
    }
  }
  
  return null;
};

/**
 * Save data to cache
 * @param {string} key - Cache key
 * @param {Object} data - Data to cache
 * @param {number} ttl - Time to live in milliseconds
 */
export const saveToCache = (key, data, ttl = 60000) => {
  if (!key || !data) return;
  
  // Save to cache with expiry time
  cache.set(key, {
    data,
    expiry: Date.now() + ttl
  });
  
  console.log(`Saved to cache: ${key}`);
};

/**
 * Clear a specific item from cache
 * @param {string} key - Cache key
 */
export const clearCacheItem = (key) => {
  if (!key) return;
  
  if (cache.has(key)) {
    cache.delete(key);
    console.log(`Cleared from cache: ${key}`);
  }
};

/**
 * Clear all cache
 */
export const clearAllCache = () => {
  cache.clear();
  console.log('Cleared all cache');
};

/**
 * Generate a cache key from URL and params
 * @param {string} url - API URL
 * @param {Object} params - Query parameters
 * @returns {string} - Cache key
 */
export const generateCacheKey = (url, params = {}) => {
  const paramString = Object.entries(params)
    .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
    .map(([key, value]) => `${key}=${value}`)
    .join('&');
  
  return `${url}?${paramString}`;
};

/**
 * Fetch data with caching
 * @param {string} url - API URL
 * @param {Object} params - Query parameters
 * @param {number} cacheTTL - Cache TTL in milliseconds
 * @returns {Promise<Object>} - Fetched data
 */
export const fetchWithCache = async (url, params = {}, cacheTTL = 10000) => {
  // Generate cache key
  const cacheKey = generateCacheKey(url, params);
  
  // Try to get from cache first
  const cachedData = getFromCache(cacheKey);
  if (cachedData) {
    return cachedData;
  }
  
  // Not in cache, fetch from API
  try {
    // Build URL with parameters
    const queryParams = new URLSearchParams(params).toString();
    const fetchUrl = queryParams ? `${url}?${queryParams}` : url;
    
    // Fetch data
    const response = await fetch(fetchUrl);
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    // Parse response
    const data = await response.json();
    
    // Save to cache
    saveToCache(cacheKey, data, cacheTTL);
    
    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
};
