import React from 'react';
import ReactDOM from 'react-dom/client';

// Simple test component
const TestApp = () => {
  return (
    <div style={{ 
      padding: '2rem', 
      maxWidth: '800px', 
      margin: '0 auto',
      fontFamily: 'Inter, sans-serif'
    }}>
      <h1 style={{ color: '#2563eb' }}>Dashboard Test</h1>
      <p>If you can see this message, <PERSON>act is working correctly!</p>
      
      <div style={{ 
        marginTop: '2rem',
        padding: '1rem',
        backgroundColor: '#f1f5f9',
        borderRadius: '0.5rem',
        border: '1px solid #e2e8f0'
      }}>
        <h2>System Status</h2>
        <ul>
          <li>React loaded: ✅</li>
          <li>DOM rendering: ✅</li>
          <li>Styling applied: ✅</li>
        </ul>
      </div>
      
      <button 
        onClick={() => alert('Button clicked!')}
        style={{
          marginTop: '1rem',
          padding: '0.5rem 1rem',
          backgroundColor: '#2563eb',
          color: 'white',
          border: 'none',
          borderRadius: '0.25rem',
          cursor: 'pointer'
        }}
      >
        Test Interactivity
      </button>
    </div>
  );
};

// Render the test component
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <TestApp />
  </React.StrictMode>
);
