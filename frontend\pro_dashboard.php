<?php
// No login required
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Pro Dashboard - Online Data Logger System</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">

    <!-- Chart.js and plugins -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.1/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0/dist/chartjs-adapter-luxon.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .header h1 {
            color: #1e293b;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            color: #64748b;
            font-size: 1rem;
        }

        .controls {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .controls h3 {
            margin-bottom: 16px;
            color: #1e293b;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .time-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-group label {
            font-weight: 500;
            margin-bottom: 4px;
            color: #374151;
            font-size: 14px;
        }

        .input-group input, .input-group button {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: #2563eb;
        }

        .btn {
            background: #2563eb;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }

        .btn:hover {
            background: #1d4ed8;
        }

        .quick-filters {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }

        .quick-filter {
            padding: 6px 12px;
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .quick-filter:hover, .quick-filter.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .parameter-selection {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }

        .parameter-btn {
            padding: 8px 16px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .parameter-btn:hover, .parameter-btn.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chart-canvas {
            position: relative;
            height: 400px;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .status-bar {
            background: white;
            border-radius: 12px;
            padding: 16px 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
            margin-top: 24px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #64748b;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .time-inputs {
                grid-template-columns: 1fr;
            }

            .parameter-selection, .quick-filters {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>⚡ Pro Dashboard</h1>
            <p>Simple charts for all electrical parameters with historical data viewing</p>
        </div>

        <!-- Controls -->
        <div class="controls">
            <h3>Time Range & Parameters</h3>

            <!-- Quick Filters -->
            <div class="quick-filters">
                <div class="quick-filter active" data-range="1h">Last 1 Hour</div>
                <div class="quick-filter" data-range="6h">Last 6 Hours</div>
                <div class="quick-filter" data-range="24h">Last 24 Hours</div>
                <div class="quick-filter" data-range="7d">Last 7 Days</div>
                <div class="quick-filter" data-range="custom">Custom Range</div>
                <div class="quick-filter" data-range="live">Live Mode</div>
            </div>

            <!-- Time Inputs -->
            <div class="time-inputs">
                <div class="input-group">
                    <label for="startDate">Start Date & Time</label>
                    <input type="datetime-local" id="startDate">
                </div>
                <div class="input-group">
                    <label for="endDate">End Date & Time</label>
                    <input type="datetime-local" id="endDate">
                </div>
                <div class="input-group">
                    <label>&nbsp;</label>
                    <button class="btn" onclick="loadData()">Load Data</button>
                </div>
            </div>

            <!-- Parameter Selection -->
            <div class="parameter-selection">
                <button class="parameter-btn active" data-parameter="voltage">
                    <span class="material-icons-round">electrical_services</span>
                    Voltage
                </button>
                <button class="parameter-btn" data-parameter="current">
                    <span class="material-icons-round">flash_on</span>
                    Current
                </button>
                <button class="parameter-btn" data-parameter="power">
                    <span class="material-icons-round">power</span>
                    Power
                </button>
                <button class="parameter-btn" data-parameter="pf">
                    <span class="material-icons-round">analytics</span>
                    Power Factor
                </button>
                <button class="parameter-btn" data-parameter="frequency">
                    <span class="material-icons-round">waves</span>
                    Frequency
                </button>
                <button class="parameter-btn" data-parameter="all">
                    <span class="material-icons-round">view_module</span>
                    All Parameters
                </button>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="chart-container">
            <div class="chart-title">
                <span class="material-icons-round">show_chart</span>
                <span id="chartTitle">Voltage Parameters</span>
            </div>
            <div class="chart-canvas">
                <canvas id="mainChart"></canvas>
            </div>
        </div>

        <!-- Table Container -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>V1N (V)</th>
                        <th>V2N (V)</th>
                        <th>V3N (V)</th>
                        <th>Ia (A)</th>
                        <th>Ib (A)</th>
                        <th>Ic (A)</th>
                        <th>PF1</th>
                        <th>PF2</th>
                        <th>PF3</th>
                        <th>Total kW</th>
                        <th>Frequency (Hz)</th>
                    </tr>
                </thead>
                <tbody id="dataRows">
                    <tr>
                        <td colspan="12" style="text-align: center; padding: 40px; color: #64748b;">
                            Loading data...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator"></div>
                <span id="connectionStatus">Connected</span>
            </div>
            <div class="status-item">
                <span class="material-icons-round">update</span>
                <span id="lastUpdate">Last updated: --</span>
            </div>
            <div class="status-item">
                <span class="material-icons-round">data_usage</span>
                <span id="dataPoints">Data points: 0</span>
            </div>
            <div class="status-item">
                <span class="material-icons-round">schedule</span>
                <span id="currentMode">Live Mode</span>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let mainChart = null;
        let currentData = [];
        let isLiveMode = true;
        let updateInterval;
        let currentParameter = 'voltage';

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing Pro Dashboard...');

            initializeChart();
            setupEventListeners();
            setDefaultDates();
            loadData();
        });

        // Initialize the main chart
        function initializeChart() {
            const ctx = document.getElementById('mainChart');
            if (!ctx) return;

            mainChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                color: '#1e293b'
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#1e293b',
                            bodyColor: '#64748b',
                            borderColor: '#e2e8f0',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                displayFormats: {
                                    second: 'HH:mm:ss',
                                    minute: 'HH:mm',
                                    hour: 'HH:mm'
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                color: '#64748b'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                color: '#64748b'
                            }
                        }
                    },
                    animation: {
                        duration: 0
                    }
                }
            });
        }

        // Setup event listeners
        function setupEventListeners() {
            // Quick filters
            document.querySelectorAll('.quick-filter').forEach(filter => {
                filter.addEventListener('click', function() {
                    document.querySelectorAll('.quick-filter').forEach(f => f.classList.remove('active'));
                    this.classList.add('active');

                    const range = this.dataset.range;
                    if (range === 'live') {
                        setLiveMode();
                    } else if (range !== 'custom') {
                        setQuickRange(range);
                        loadData();
                    }
                });
            });

            // Parameter buttons
            document.querySelectorAll('.parameter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.parameter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    currentParameter = this.dataset.parameter;
                    updateChart(currentParameter);
                });
            });
        }

        // Set default dates
        function setDefaultDates() {
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

            document.getElementById('startDate').value = formatDateTimeLocal(oneHourAgo);
            document.getElementById('endDate').value = formatDateTimeLocal(now);
        }

        // Format date for datetime-local input
        function formatDateTimeLocal(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        // Set quick time ranges
        function setQuickRange(range) {
            const now = new Date();
            let startTime;

            switch (range) {
                case '1h':
                    startTime = new Date(now.getTime() - 60 * 60 * 1000);
                    break;
                case '6h':
                    startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                    break;
                case '24h':
                    startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case '7d':
                    startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    return;
            }

            document.getElementById('startDate').value = formatDateTimeLocal(startTime);
            document.getElementById('endDate').value = formatDateTimeLocal(now);

            isLiveMode = false;
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
            document.getElementById('currentMode').textContent = 'Historical Mode';
        }

        // Set live mode
        function setLiveMode() {
            isLiveMode = true;
            document.getElementById('currentMode').textContent = 'Live Mode';

            // Start live updates
            if (updateInterval) clearInterval(updateInterval);
            updateInterval = setInterval(fetchLiveData, 3000);

            // Load initial live data
            fetchLiveData();
        }

        // Load data (historical or live)
        async function loadData() {
            try {
                updateStatus('Loading data...');

                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                let url;
                if (isLiveMode) {
                    url = '../backend/get_latest_data.php?records=50';
                } else {
                    const params = new URLSearchParams({
                        start_date: startDate,
                        end_date: endDate,
                        limit: 1000
                    });
                    url = `../backend/get_historical_data.php?${params}`;
                }

                const response = await fetch(url);
                const result = await response.json();

                if (result.error) {
                    throw new Error(result.error);
                }

                currentData = isLiveMode ? result : (result.data || []);

                if (currentData.length > 0) {
                    updateTable();
                    updateChart(currentParameter);
                    updateStatus(`Loaded ${currentData.length} records`);
                    updateDataPointsCount(currentData.length);

                    const latestData = currentData[currentData.length - 1];
                    document.getElementById('lastUpdate').textContent =
                        `Last updated: ${new Date(latestData.timestamp).toLocaleTimeString()}`;
                } else {
                    updateStatus('No data found');
                }

            } catch (error) {
                console.error('Error loading data:', error);
                updateStatus('Error loading data: ' + error.message);
            }
        }

        // Fetch live data
        async function fetchLiveData() {
            if (!isLiveMode) return;

            try {
                const response = await fetch('../backend/get_latest_data.php?records=1');
                const data = await response.json();

                if (Array.isArray(data) && data.length > 0) {
                    const latestData = data[0];

                    // Add to current data
                    currentData.push(latestData);

                    // Keep only last 100 points for performance
                    if (currentData.length > 100) {
                        currentData.shift();
                    }

                    updateTable();
                    updateChart(currentParameter);

                    document.getElementById('lastUpdate').textContent =
                        `Last updated: ${new Date(latestData.timestamp).toLocaleTimeString()}`;
                    updateDataPointsCount(currentData.length);
                }

                updateStatus('Connected');

            } catch (error) {
                console.error('Error fetching live data:', error);
                updateStatus('Connection Error');
            }
        }

        // Update the chart based on selected parameter
        function updateChart(parameter) {
            if (!mainChart || currentData.length === 0) return;

            const colors = ['#2563eb', '#dc2626', '#16a34a', '#ea580c', '#7c3aed', '#0891b2'];
            let datasets = [];
            let title = '';

            switch (parameter) {
                case 'voltage':
                    title = 'Voltage Parameters';
                    datasets = [
                        { label: 'V1N', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' },
                        { label: 'V2N', data: [], borderColor: colors[1], backgroundColor: colors[1] + '20' },
                        { label: 'V3N', data: [], borderColor: colors[2], backgroundColor: colors[2] + '20' }
                    ];
                    currentData.forEach(row => {
                        const time = new Date(row.timestamp);
                        datasets[0].data.push({ x: time, y: parseFloat(row.voltage_1) });
                        datasets[1].data.push({ x: time, y: parseFloat(row.voltage_2) });
                        datasets[2].data.push({ x: time, y: parseFloat(row.voltage_3) });
                    });
                    break;

                case 'current':
                    title = 'Current Parameters';
                    datasets = [
                        { label: 'Ia', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' },
                        { label: 'Ib', data: [], borderColor: colors[1], backgroundColor: colors[1] + '20' },
                        { label: 'Ic', data: [], borderColor: colors[2], backgroundColor: colors[2] + '20' }
                    ];
                    currentData.forEach(row => {
                        const time = new Date(row.timestamp);
                        datasets[0].data.push({ x: time, y: parseFloat(row.current_1) });
                        datasets[1].data.push({ x: time, y: parseFloat(row.current_2) });
                        datasets[2].data.push({ x: time, y: parseFloat(row.current_3) });
                    });
                    break;

                case 'power':
                    title = 'Power Parameters';
                    datasets = [
                        { label: 'Total kW', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' },
                        { label: 'Total kVA', data: [], borderColor: colors[1], backgroundColor: colors[1] + '20' },
                        { label: 'Total kVAR', data: [], borderColor: colors[2], backgroundColor: colors[2] + '20' }
                    ];
                    currentData.forEach(row => {
                        const time = new Date(row.timestamp);
                        datasets[0].data.push({ x: time, y: parseFloat(row.total_kw) });
                        datasets[1].data.push({ x: time, y: parseFloat(row.total_kva) });
                        datasets[2].data.push({ x: time, y: parseFloat(row.total_kvar) });
                    });
                    break;

                case 'pf':
                    title = 'Power Factor Parameters';
                    datasets = [
                        { label: 'PF1', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' },
                        { label: 'PF2', data: [], borderColor: colors[1], backgroundColor: colors[1] + '20' },
                        { label: 'PF3', data: [], borderColor: colors[2], backgroundColor: colors[2] + '20' }
                    ];
                    currentData.forEach(row => {
                        const time = new Date(row.timestamp);
                        datasets[0].data.push({ x: time, y: parseFloat(row.pf_1) });
                        datasets[1].data.push({ x: time, y: parseFloat(row.pf_2) });
                        datasets[2].data.push({ x: time, y: parseFloat(row.pf_3) });
                    });
                    break;

                case 'frequency':
                    title = 'Frequency Parameter';
                    datasets = [
                        { label: 'Frequency', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' }
                    ];
                    currentData.forEach(row => {
                        const time = new Date(row.timestamp);
                        datasets[0].data.push({ x: time, y: parseFloat(row.frequency) });
                    });
                    break;

                case 'all':
                    title = 'All Parameters (Normalized)';
                    datasets = [
                        { label: 'V1N (V/10)', data: [], borderColor: colors[0], backgroundColor: colors[0] + '20' },
                        { label: 'Ia (A*10)', data: [], borderColor: colors[1], backgroundColor: colors[1] + '20' },
                        { label: 'PF1 (*100)', data: [], borderColor: colors[2], backgroundColor: colors[2] + '20' },
                        { label: 'Frequency', data: [], borderColor: colors[3], backgroundColor: colors[3] + '20' },
                        { label: 'Total kW/100', data: [], borderColor: colors[4], backgroundColor: colors[4] + '20' }
                    ];
                    currentData.forEach(row => {
                        const time = new Date(row.timestamp);
                        datasets[0].data.push({ x: time, y: parseFloat(row.voltage_1) / 10 });
                        datasets[1].data.push({ x: time, y: parseFloat(row.current_1) * 10 });
                        datasets[2].data.push({ x: time, y: parseFloat(row.pf_1) * 100 });
                        datasets[3].data.push({ x: time, y: parseFloat(row.frequency) });
                        datasets[4].data.push({ x: time, y: parseFloat(row.total_kw) / 100 });
                    });
                    break;
            }

            // Apply thin line style
            datasets.forEach(dataset => {
                dataset.borderWidth = 1; // Very thin lines
                dataset.fill = false;
                dataset.tension = 0.1;
                dataset.pointRadius = 0;
                dataset.pointHoverRadius = 3;
            });

            document.getElementById('chartTitle').textContent = title;
            mainChart.data.datasets = datasets;
            mainChart.update();
        }

        // Update table with current data
        function updateTable() {
            const tbody = document.getElementById('dataRows');
            if (!tbody || currentData.length === 0) {
                if (tbody) {
                    tbody.innerHTML = '<tr><td colspan="12" style="text-align: center; padding: 40px; color: #64748b;">No data available</td></tr>';
                }
                return;
            }

            // Show only last 10 records in table for performance
            const displayData = currentData.slice(-10);

            tbody.innerHTML = displayData.map(row => `
                <tr>
                    <td>${new Date(row.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}</td>
                    <td>${parseFloat(row.voltage_1).toFixed(3)}</td>
                    <td>${parseFloat(row.voltage_2).toFixed(3)}</td>
                    <td>${parseFloat(row.voltage_3).toFixed(3)}</td>
                    <td>${parseFloat(row.current_1).toFixed(3)}</td>
                    <td>${parseFloat(row.current_2).toFixed(3)}</td>
                    <td>${parseFloat(row.current_3).toFixed(3)}</td>
                    <td>${parseFloat(row.pf_1).toFixed(3)}</td>
                    <td>${parseFloat(row.pf_2).toFixed(3)}</td>
                    <td>${parseFloat(row.pf_3).toFixed(3)}</td>
                    <td>${parseFloat(row.total_kw).toFixed(3)}</td>
                    <td>${parseFloat(row.frequency).toFixed(3)}</td>
                </tr>
            `).join('');
        }

        // Update status
        function updateStatus(message) {
            document.getElementById('connectionStatus').textContent = message;
        }

        // Update data points count
        function updateDataPointsCount(count) {
            document.getElementById('dataPoints').textContent = `Data points: ${count}`;
        }

        // Initialize with live mode
        console.log('Pro Dashboard initialized successfully');
    </script>
</body>
</html>

        // Start live data updates
        function startLiveUpdates() {
            if (updateInterval) clearInterval(updateInterval);

            updateInterval = setInterval(async () => {
                if (isLiveMode && !isPaused) {
                    await fetchLatestData();
                }
            }, 2000); // Update every 2 seconds
        }

        // Fetch latest data from API
        async function fetchLatestData() {
            try {
                const response = await fetch('../backend/get_latest_data.php?records=1');
                if (!response.ok) throw new Error('Failed to fetch data');

                const data = await response.json();
                if (Array.isArray(data) && data.length > 0) {
                    const latestData = data[0];
                    updateAllCharts(latestData);
                    updateStatus('Connected', new Date(latestData.timestamp));
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                updateStatus('Connection Error', null);
            }
        }

        // Load initial historical data
        async function loadInitialData() {
            try {
                const response = await fetch('../backend/get_latest_data.php?records=50');
                if (!response.ok) throw new Error('Failed to fetch initial data');

                const data = await response.json();
                if (Array.isArray(data) && data.length > 0) {
                    // Process data in reverse order (oldest first)
                    data.reverse().forEach(dataPoint => {
                        updateAllCharts(dataPoint);
                    });

                    updateStatus('Connected', new Date(data[data.length - 1].timestamp));
                    updateDataPointsCount(data.length);
                }
            } catch (error) {
                console.error('Error loading initial data:', error);
                updateStatus('Connection Error', null);
            }
        }

        // Update all charts with new data
        function updateAllCharts(dataPoint) {
            const timestamp = new Date(dataPoint.timestamp);

            Object.keys(chartConfigs).forEach(chartType => {
                const chart = charts[chartType];
                const config = chartConfigs[chartType];

                if (!chart) return;

                // Add timestamp to labels
                chart.data.labels.push(timestamp);

                // Add data to each dataset
                config.dataKeys.forEach((key, index) => {
                    const value = parseFloat(dataPoint[key]) || 0;
                    chart.data.datasets[index].data.push(value);
                });

                // Keep only last 100 points for performance
                if (chart.data.labels.length > 100) {
                    chart.data.labels.shift();
                    chart.data.datasets.forEach(dataset => {
                        dataset.data.shift();
                    });
                }

                chart.update('none');
            });

            allData.push(dataPoint);
            if (allData.length > 100) {
                allData.shift();
            }
        }

        // Load historical data
        async function loadHistoricalData() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;

            if (!startTime || !endTime) {
                alert('Please select both start and end times');
                return;
            }

            try {
                updateStatus('Loading historical data...', null);
                setLiveMode(false);

                // Clear existing data
                clearAllCharts();

                const startDate = new Date(startTime).toISOString().slice(0, 19).replace('T', ' ');
                const endDate = new Date(endTime).toISOString().slice(0, 19).replace('T', ' ');

                const response = await fetch(`../backend/get_historical_data.php?start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}&limit=1000`);

                if (!response.ok) throw new Error('Failed to fetch historical data');

                const result = await response.json();

                if (result.error) {
                    throw new Error(result.error);
                }

                const data = result.data || [];

                if (data.length > 0) {
                    // Process historical data
                    data.forEach(dataPoint => {
                        updateAllCharts(dataPoint);
                    });

                    updateStatus('Historical data loaded', new Date(data[data.length - 1].timestamp));
                    updateDataPointsCount(data.length);
                    document.getElementById('currentMode').textContent = 'Historical Mode';
                } else {
                    updateStatus('No data found for selected period', null);
                }

            } catch (error) {
                console.error('Error loading historical data:', error);
                updateStatus('Error loading historical data: ' + error.message, null);
            }
        }

        // Clear all charts
        function clearAllCharts() {
            Object.values(charts).forEach(chart => {
                if (chart) {
                    chart.data.labels = [];
                    chart.data.datasets.forEach(dataset => {
                        dataset.data = [];
                    });
                    chart.update('none');
                }
            });
            allData = [];
        }

        // Set quick time ranges
        function setQuickTime(hours) {
            const now = new Date();
            const startTime = new Date(now.getTime() - hours * 60 * 60 * 1000);

            document.getElementById('startTime').value = formatDateTimeLocal(startTime);
            document.getElementById('endTime').value = formatDateTimeLocal(now);

            // Update active button
            document.querySelectorAll('.quick-time-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Auto-load data
            loadHistoricalData();
        }

        // Set live mode
        function setLiveMode(enable = true) {
            isLiveMode = enable;

            if (enable) {
                document.getElementById('currentMode').textContent = 'Live Mode';
                document.querySelectorAll('.quick-time-btn').forEach(btn => btn.classList.remove('active'));
                document.querySelector('.quick-time-btn:last-child').classList.add('active');

                clearAllCharts();
                loadInitialData();
                startLiveUpdates();
            } else {
                document.getElementById('currentMode').textContent = 'Historical Mode';
                if (updateInterval) {
                    clearInterval(updateInterval);
                    updateInterval = null;
                }
            }
        }

        // Pause charts
        function pauseCharts() {
            isPaused = true;
            updateStatus('Paused', null);
        }

        // Resume charts
        function resumeCharts() {
            isPaused = false;
            updateStatus('Resumed', null);
        }

        // Reset zoom for a specific chart
        function resetZoom(chartId) {
            const chartType = chartId.replace('Chart', '').replace('pf', 'pf').replace('kva', 'kva').replace('totalPower', 'totalPower').replace('frequency', 'frequency');
            const chart = charts[chartType];

            if (chart && chart.resetZoom) {
                chart.resetZoom();
            }
        }

        // Toggle fullscreen for a chart
        function toggleFullscreen(chartId) {
            const chartElement = document.getElementById(chartId).closest('.chart-card');

            if (!document.fullscreenElement) {
                chartElement.requestFullscreen().catch(err => {
                    console.error('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }

        // Export data to CSV
        function exportData() {
            if (allData.length === 0) {
                alert('No data available to export');
                return;
            }

            const headers = [
                'Timestamp', 'Voltage_1', 'Voltage_2', 'Voltage_3',
                'Current_1', 'Current_2', 'Current_3',
                'PF_1', 'PF_2', 'PF_3',
                'KVA_1', 'KVA_2', 'KVA_3',
                'Total_KW', 'Total_KVA', 'Total_KVAR', 'Frequency'
            ];

            const csvContent = [
                headers.join(','),
                ...allData.map(row => [
                    row.timestamp,
                    row.voltage_1, row.voltage_2, row.voltage_3,
                    row.current_1, row.current_2, row.current_3,
                    row.pf_1, row.pf_2, row.pf_3,
                    row.kva_1, row.kva_2, row.kva_3,
                    row.total_kw, row.total_kva, row.total_kvar,
                    row.frequency
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `electrical_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Update status display
        function updateStatus(message, timestamp) {
            document.getElementById('connectionStatus').textContent = message;

            if (timestamp) {
                document.getElementById('lastUpdate').textContent =
                    `Last updated: ${timestamp.toLocaleTimeString()}`;
            }
        }

        // Update data points count
        function updateDataPointsCount(count) {
            document.getElementById('dataPoints').textContent = `Data points: ${count}`;
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            Object.values(charts).forEach(chart => {
                if (chart) {
                    chart.resize();
                }
            });
        });

        // Handle page visibility change
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // Page is hidden, pause updates to save resources
                if (updateInterval) {
                    clearInterval(updateInterval);
                }
            } else {
                // Page is visible, resume updates
                if (isLiveMode && !isPaused) {
                    startLiveUpdates();
                }
            }
        });

        // Initialize with live mode
        console.log('Pro Dashboard initialized successfully');
    </script>
</body>
</html>