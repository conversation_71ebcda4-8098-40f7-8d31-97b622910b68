<?php
// Database setup script
echo "Starting database setup...\n";

// Database configuration
$servername = "localhost";
$username = "root";  // Change this to your database username
$password = "";      // Change this to your database password

// Create connection without database selection
$conn = new mysqli($servername, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error . "\n");
}

echo "Connected to MySQL server successfully.\n";

// Create database if it doesn't exist
$sql = "CREATE DATABASE IF NOT EXISTS esp32_data";
if ($conn->query($sql) === TRUE) {
    echo "Database 'esp32_data' created or already exists.\n";
} else {
    echo "Error creating database: " . $conn->error . "\n";
    $conn->close();
    exit;
}

// Select the database
$conn->select_db("esp32_data");

// Create table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS electrical_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    -- Phase Voltages
    voltage_1 FLOAT,    -- Phase 1 Voltage
    voltage_2 FLOAT,    -- Phase 2 Voltage
    voltage_3 FLOAT,    -- Phase 3 Voltage
    
    -- Phase Currents
    current_1 FLOAT,    -- Phase 1 Current
    current_2 FLOAT,    -- Phase 2 Current
    current_3 FLOAT,    -- Phase 3 Current
    
    -- Power Factors
    pf_1 FLOAT,        -- Phase 1 Power Factor
    pf_2 FLOAT,        -- Phase 2 Power Factor
    pf_3 FLOAT,        -- Phase 3 Power Factor
    
    -- Apparent Power (kVA)
    kva_1 FLOAT,       -- Phase 1 kVA
    kva_2 FLOAT,       -- Phase 2 kVA
    kva_3 FLOAT,       -- Phase 3 kVA
    
    -- Total Power
    total_kva FLOAT,   -- Total kVA
    total_kw FLOAT,    -- Total kW
    total_kvar FLOAT,  -- Total kVAR
    
    frequency FLOAT,   -- System Frequency
    timestamp DATETIME
)";

if ($conn->query($sql) === TRUE) {
    echo "Table 'electrical_data' created or already exists.\n";
} else {
    echo "Error creating table: " . $conn->error . "\n";
}

// Insert sample data if the table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    echo "Inserting sample data...\n";
    
    // Current timestamp
    $timestamp = date('Y-m-d H:i:s');
    
    // Sample data
    $sql = "INSERT INTO electrical_data (
        voltage_1, voltage_2, voltage_3,
        current_1, current_2, current_3,
        pf_1, pf_2, pf_3,
        kva_1, kva_2, kva_3,
        total_kva, total_kw, total_kvar,
        frequency, timestamp
    ) VALUES 
    (400.5, 401.2, 399.8, 50.1, 49.8, 50.3, 0.92, 0.93, 0.91, 20.1, 19.8, 20.3, 60.2, 55.4, 24.8, 50.1, '$timestamp'),
    (401.0, 400.8, 400.2, 50.3, 50.1, 49.9, 0.93, 0.92, 0.92, 20.2, 20.1, 19.9, 60.3, 55.5, 24.9, 50.0, '$timestamp')";
    
    if ($conn->query($sql) === TRUE) {
        echo "Sample data inserted successfully.\n";
    } else {
        echo "Error inserting sample data: " . $conn->error . "\n";
    }
}

echo "Database setup completed.\n";
$conn->close();
?>
