/**
 * Utility functions for data caching
 */

// Cache duration in milliseconds
const DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Cache storage class
 */
class CacheStorage {
  constructor() {
    this.cache = {};
    this.metaCache = {};
    
    // Load cache from localStorage if available
    this.loadFromStorage();
    
    // Set up periodic cleanup
    setInterval(() => this.cleanupExpiredCache(), 60 * 1000); // Clean up every minute
  }
  
  /**
   * Load cache from localStorage
   */
  loadFromStorage() {
    try {
      const savedCache = localStorage.getItem('dataCache');
      const savedMetaCache = localStorage.getItem('metaCache');
      
      if (savedCache && savedMetaCache) {
        this.cache = JSON.parse(savedCache);
        this.metaCache = JSON.parse(savedMetaCache);
        
        // Clean up expired items on load
        this.cleanupExpiredCache();
      }
    } catch (error) {
      console.error('Error loading cache from localStorage:', error);
      // Reset cache if there's an error
      this.cache = {};
      this.metaCache = {};
    }
  }
  
  /**
   * Save cache to localStorage
   */
  saveToStorage() {
    try {
      localStorage.setItem('dataCache', JSON.stringify(this.cache));
      localStorage.setItem('metaCache', JSON.stringify(this.metaCache));
    } catch (error) {
      console.error('Error saving cache to localStorage:', error);
      
      // If storage is full, clear older items
      if (error.name === 'QuotaExceededError') {
        this.clearOldestItems(10); // Clear 10 oldest items
        this.saveToStorage(); // Try again
      }
    }
  }
  
  /**
   * Clear oldest items from cache
   * @param {number} count - Number of items to clear
   */
  clearOldestItems(count) {
    const entries = Object.entries(this.metaCache);
    
    // Sort by timestamp (oldest first)
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    // Remove oldest items
    const toRemove = entries.slice(0, count).map(entry => entry[0]);
    
    toRemove.forEach(key => {
      delete this.cache[key];
      delete this.metaCache[key];
    });
  }
  
  /**
   * Clean up expired cache items
   */
  cleanupExpiredCache() {
    const now = Date.now();
    let hasExpired = false;
    
    Object.keys(this.metaCache).forEach(key => {
      if (this.metaCache[key].expiresAt < now) {
        delete this.cache[key];
        delete this.metaCache[key];
        hasExpired = true;
      }
    });
    
    // Save to storage if items were removed
    if (hasExpired) {
      this.saveToStorage();
    }
  }
  
  /**
   * Set a cache item
   * @param {string} key - Cache key
   * @param {any} data - Data to cache
   * @param {number} duration - Cache duration in milliseconds
   */
  set(key, data, duration = DEFAULT_CACHE_DURATION) {
    const now = Date.now();
    
    this.cache[key] = data;
    this.metaCache[key] = {
      timestamp: now,
      expiresAt: now + duration
    };
    
    this.saveToStorage();
  }
  
  /**
   * Get a cache item
   * @param {string} key - Cache key
   * @returns {any|null} - Cached data or null if not found or expired
   */
  get(key) {
    const now = Date.now();
    
    // Check if item exists and is not expired
    if (this.metaCache[key] && this.metaCache[key].expiresAt > now) {
      // Update timestamp to indicate recent use
      this.metaCache[key].timestamp = now;
      return this.cache[key];
    }
    
    // Remove expired item
    if (this.metaCache[key]) {
      delete this.cache[key];
      delete this.metaCache[key];
      this.saveToStorage();
    }
    
    return null;
  }
  
  /**
   * Check if a cache item exists and is not expired
   * @param {string} key - Cache key
   * @returns {boolean} - True if item exists and is not expired
   */
  has(key) {
    const now = Date.now();
    return this.metaCache[key] && this.metaCache[key].expiresAt > now;
  }
  
  /**
   * Remove a cache item
   * @param {string} key - Cache key
   */
  remove(key) {
    delete this.cache[key];
    delete this.metaCache[key];
    this.saveToStorage();
  }
  
  /**
   * Clear all cache items
   */
  clear() {
    this.cache = {};
    this.metaCache = {};
    this.saveToStorage();
  }
}

// Create a singleton instance
const cacheStorage = new CacheStorage();

/**
 * Cache API response
 * @param {string} url - API URL
 * @param {Object} params - Request parameters
 * @param {Function} fetchFunction - Function to fetch data if not in cache
 * @param {number} duration - Cache duration in milliseconds
 * @returns {Promise<any>} - Cached or fetched data
 */
export const cacheApiResponse = async (url, params = {}, fetchFunction, duration = DEFAULT_CACHE_DURATION) => {
  // Create a cache key from URL and params
  const cacheKey = `${url}:${JSON.stringify(params)}`;
  
  // Check if data is in cache
  const cachedData = cacheStorage.get(cacheKey);
  if (cachedData) {
    console.log(`Using cached data for ${cacheKey}`);
    return cachedData;
  }
  
  // Fetch fresh data
  try {
    const data = await fetchFunction();
    
    // Cache the result
    cacheStorage.set(cacheKey, data, duration);
    
    return data;
  } catch (error) {
    throw error;
  }
};

/**
 * Clear cache for a specific API endpoint
 * @param {string} url - API URL pattern to match
 */
export const clearApiCache = (url) => {
  Object.keys(cacheStorage.cache).forEach(key => {
    if (key.startsWith(`${url}:`)) {
      cacheStorage.remove(key);
    }
  });
};

/**
 * Get cache statistics
 * @returns {Object} - Cache statistics
 */
export const getCacheStats = () => {
  const keys = Object.keys(cacheStorage.cache);
  
  return {
    itemCount: keys.length,
    size: new Blob([JSON.stringify(cacheStorage.cache)]).size,
    oldestItem: keys.length > 0 ? 
      Math.min(...Object.values(cacheStorage.metaCache).map(meta => meta.timestamp)) : 
      null,
    newestItem: keys.length > 0 ? 
      Math.max(...Object.values(cacheStorage.metaCache).map(meta => meta.timestamp)) : 
      null
  };
};

export default cacheStorage;
