/**
 * Utility functions for data export
 */

/**
 * Convert data to CSV format
 * @param {Array} data - Array of data objects
 * @param {Array} headers - Array of header objects with key and label properties
 * @param {string} filename - Filename for the CSV file
 * @returns {string} - CSV string
 */
export const convertToCSV = (data, headers) => {
  if (!data || !data.length) return '';
  
  // Create header row
  const headerRow = headers.map(header => `"${header.label}"`).join(',');
  
  // Create data rows
  const dataRows = data.map(item => {
    return headers.map(header => {
      const value = item[header.key];
      
      // Handle different value types
      if (value === null || value === undefined) {
        return '""';
      } else if (typeof value === 'string') {
        // Escape quotes in strings
        return `"${value.replace(/"/g, '""')}"`;
      } else if (value instanceof Date) {
        return `"${value.toISOString()}"`;
      } else {
        return `"${value}"`;
      }
    }).join(',');
  }).join('\n');
  
  // Combine header and data rows
  return `${headerRow}\n${dataRows}`;
};

/**
 * Download data as CSV file
 * @param {Array} data - Array of data objects
 * @param {Array} headers - Array of header objects with key and label properties
 * @param {string} filename - Filename for the CSV file
 */
export const downloadCSV = (data, headers, filename = 'export.csv') => {
  // Convert data to CSV
  const csv = convertToCSV(data, headers);
  
  // Create a Blob with the CSV data
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  
  // Create a download link
  const link = document.createElement('a');
  
  // Set link properties
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.style.display = 'none';
  
  // Add link to document
  document.body.appendChild(link);
  
  // Click the link to download the file
  link.click();
  
  // Clean up
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};

/**
 * Format timestamp for CSV export
 * @param {string} timestamp - Timestamp string
 * @returns {string} - Formatted timestamp
 */
export const formatTimestampForExport = (timestamp) => {
  if (!timestamp) return '';
  
  try {
    const date = new Date(timestamp);
    return date.toISOString();
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return timestamp;
  }
};

/**
 * Format number for CSV export
 * @param {number} value - Number value
 * @param {number} decimals - Number of decimal places
 * @returns {string} - Formatted number
 */
export const formatNumberForExport = (value, decimals = 3) => {
  if (value === null || value === undefined) return '';
  
  try {
    return Number(value).toFixed(decimals);
  } catch (error) {
    console.error('Error formatting number:', error);
    return value;
  }
};

/**
 * Generate CSV filename with timestamp
 * @param {string} prefix - Filename prefix
 * @param {Date} startDate - Start date for the data
 * @param {Date} endDate - End date for the data
 * @returns {string} - Filename
 */
export const generateCSVFilename = (prefix = 'power_data', startDate, endDate) => {
  const formatDate = (date) => {
    if (!date) return '';
    
    try {
      return date instanceof Date 
        ? date.toISOString().split('T')[0] 
        : new Date(date).toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date for filename:', error);
      return '';
    }
  };
  
  const startStr = startDate ? `_from_${formatDate(startDate)}` : '';
  const endStr = endDate ? `_to_${formatDate(endDate)}` : '';
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
  
  return `${prefix}${startStr}${endStr}_${timestamp}.csv`;
};

/**
 * Fetch all pages of data for export
 * @param {Function} fetchFunction - Function to fetch a page of data
 * @param {number} pageSize - Number of items per page
 * @param {Function} onProgress - Callback for progress updates
 * @returns {Promise<Array>} - All data
 */
export const fetchAllPagesForExport = async (fetchFunction, pageSize = 1000, onProgress = null) => {
  let allData = [];
  let page = 1;
  let hasMore = true;
  
  while (hasMore) {
    try {
      // Fetch a page of data
      const result = await fetchFunction(page, pageSize);
      
      // Check if we got data
      if (result && Array.isArray(result.data) && result.data.length > 0) {
        // Add data to the collection
        allData = [...allData, ...result.data];
        
        // Update progress
        if (onProgress) {
          onProgress({
            page,
            totalItems: allData.length,
            hasMore: result.data.length === pageSize
          });
        }
        
        // Check if we have more data
        hasMore = result.data.length === pageSize;
        page++;
      } else {
        // No more data
        hasMore = false;
      }
    } catch (error) {
      console.error('Error fetching data for export:', error);
      throw error;
    }
  }
  
  return allData;
};
