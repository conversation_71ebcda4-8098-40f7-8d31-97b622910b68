import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from './Modal';

const SettingsModal = ({ isOpen, onClose, settings, saveSettings, resetSettings }) => {
  const [formValues, setFormValues] = useState({
    refreshRate: 2,
    timeWindow: 1,
    decimalPlaces: 3,
    chartTheme: 'professional',
    lineThickness: 0.5,
    showGridLines: true,
    voltageAlertHigh: 450,
    voltageAlertLow: 350,
    frequencyAlertHigh: 55,
    frequencyAlertLow: 45
  });
  
  // Update form values when settings change
  useEffect(() => {
    if (settings) {
      setFormValues(settings);
    }
  }, [settings]);
  
  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormValues(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? parseFloat(value) : value
    }));
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    saveSettings(formValues);
    onClose();
  };
  
  // Handle reset
  const handleReset = () => {
    resetSettings();
    onClose();
  };
  
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      title="Dashboard Settings"
    >
      <SettingsForm onSubmit={handleSubmit}>
        <SettingsSection>
          <h3>Display Settings</h3>
          <SettingItem>
            <label htmlFor="refreshRate">Data Refresh Rate (seconds)</label>
            <input 
              type="number" 
              id="refreshRate" 
              name="refreshRate"
              min="1" 
              max="60" 
              value={formValues.refreshRate}
              onChange={handleChange}
            />
          </SettingItem>
          <SettingItem>
            <label htmlFor="timeWindow">Default Time Window (minutes)</label>
            <input 
              type="number" 
              id="timeWindow" 
              name="timeWindow"
              min="1" 
              max="60" 
              value={formValues.timeWindow}
              onChange={handleChange}
            />
          </SettingItem>
          <SettingItem>
            <label htmlFor="decimalPlaces">Decimal Places</label>
            <input 
              type="number" 
              id="decimalPlaces" 
              name="decimalPlaces"
              min="0" 
              max="5" 
              value={formValues.decimalPlaces}
              onChange={handleChange}
            />
          </SettingItem>
        </SettingsSection>
        
        <SettingsSection>
          <h3>Chart Settings</h3>
          <SettingItem>
            <label htmlFor="chartTheme">Chart Theme</label>
            <select 
              id="chartTheme" 
              name="chartTheme"
              value={formValues.chartTheme}
              onChange={handleChange}
            >
              <option value="professional">Professional</option>
              <option value="material">Material</option>
              <option value="pastel">Pastel</option>
            </select>
          </SettingItem>
          <SettingItem>
            <label htmlFor="lineThickness">Line Thickness</label>
            <input 
              type="range" 
              id="lineThickness" 
              name="lineThickness"
              min="0.5" 
              max="3" 
              step="0.5" 
              value={formValues.lineThickness}
              onChange={handleChange}
            />
          </SettingItem>
          <SettingItem className="checkbox">
            <input 
              type="checkbox" 
              id="showGridLines" 
              name="showGridLines"
              checked={formValues.showGridLines}
              onChange={handleChange}
            />
            <label htmlFor="showGridLines">Show Grid Lines</label>
          </SettingItem>
        </SettingsSection>
        
        <SettingsSection>
          <h3>Alert Settings</h3>
          <SettingItem>
            <label htmlFor="voltageAlertHigh">Voltage High Alert (V)</label>
            <input 
              type="number" 
              id="voltageAlertHigh" 
              name="voltageAlertHigh"
              value={formValues.voltageAlertHigh}
              onChange={handleChange}
            />
          </SettingItem>
          <SettingItem>
            <label htmlFor="voltageAlertLow">Voltage Low Alert (V)</label>
            <input 
              type="number" 
              id="voltageAlertLow" 
              name="voltageAlertLow"
              value={formValues.voltageAlertLow}
              onChange={handleChange}
            />
          </SettingItem>
          <SettingItem>
            <label htmlFor="frequencyAlertHigh">Frequency High Alert (Hz)</label>
            <input 
              type="number" 
              id="frequencyAlertHigh" 
              name="frequencyAlertHigh"
              value={formValues.frequencyAlertHigh}
              onChange={handleChange}
            />
          </SettingItem>
          <SettingItem>
            <label htmlFor="frequencyAlertLow">Frequency Low Alert (Hz)</label>
            <input 
              type="number" 
              id="frequencyAlertLow" 
              name="frequencyAlertLow"
              value={formValues.frequencyAlertLow}
              onChange={handleChange}
            />
          </SettingItem>
        </SettingsSection>
        
        <ModalFooter>
          <ModalButton 
            type="button" 
            className="secondary"
            onClick={handleReset}
          >
            Reset to Defaults
          </ModalButton>
          <ModalButton 
            type="submit" 
            className="primary"
          >
            Save Settings
          </ModalButton>
        </ModalFooter>
      </SettingsForm>
    </Modal>
  );
};

const SettingsForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const SettingsSection = styled.div`
  margin-bottom: 2rem;
  
  h3 {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    letter-spacing: -0.01em;
  }
`;

const SettingItem = styled.div`
  margin-bottom: 1.25rem;
  
  label {
    display: block;
    font-size: 0.9375rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: 0.5rem;
  }
  
  input[type="number"],
  input[type="text"],
  select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9375rem;
    color: var(--text-color);
    background-color: var(--card-color);
    transition: all var(--transition-speed) ease;
    box-shadow: var(--shadow-sm);
    
    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.15);
      outline: none;
    }
  }
  
  input[type="range"] {
    width: 100%;
    height: 0.5rem;
    -webkit-appearance: none;
    background: var(--hover-color);
    border-radius: 0.25rem;
    outline: none;
    margin: 0.75rem 0;
    
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 1.25rem;
      height: 1.25rem;
      border-radius: 50%;
      background: var(--primary-color);
      cursor: pointer;
      box-shadow: var(--shadow-sm);
      border: 2px solid white;
    }
  }
  
  &.checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    
    input {
      width: 1.125rem;
      height: 1.125rem;
      accent-color: var(--primary-color);
    }
    
    label {
      margin-bottom: 0;
      cursor: pointer;
    }
  }
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1rem;
`;

const ModalButton = styled.button`
  padding: 0.625rem 1.25rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
  transition: all var(--transition-speed) ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  
  &.primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    
    &:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }
  }
  
  &.secondary {
    background-color: var(--card-color);
    color: var(--text-color);
    
    &:hover {
      background-color: var(--hover-color);
      border-color: var(--primary-light);
    }
  }
`;

export default SettingsModal;
