<?php
require_once 'vite-helper.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Debug - Online Data Logger</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    
    <!-- Debug Vite Assets -->
    <?php 
    echo "<!-- Vite Client Script -->\n";
    echo viteClientScript() . "\n";
    echo "<!-- Vite CSS -->\n";
    echo viteCssTag('history') . "\n";
    echo "<!-- Vite JS -->\n";
    echo viteScriptTag('history') . "\n";
    ?>
    
    <style>
        .debug-info {
            background: #1e293b;
            color: #f8fafc;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 14px;
        }
        .debug-section {
            margin-bottom: 15px;
            padding: 10px;
            background: #0f172a;
            border-radius: 4px;
        }
        .debug-title {
            color: #06b6d4;
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <h1>CSS Debug Information</h1>
        
        <div class="debug-section">
            <div class="debug-title">Development Mode Check:</div>
            <div><?php echo isDev() ? 'TRUE - Vite dev server is running' : 'FALSE - Using production assets'; ?></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">Manifest File Check:</div>
            <div>
                <?php 
                $manifestPath = __DIR__ . '/assets/manifest.json';
                if (file_exists($manifestPath)) {
                    echo "✓ Manifest file exists at: $manifestPath";
                } else {
                    echo "✗ Manifest file NOT found at: $manifestPath";
                }
                ?>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">Manifest Contents:</div>
            <pre><?php 
            $manifest = getManifest();
            echo json_encode($manifest, JSON_PRETTY_PRINT);
            ?></pre>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">Generated CSS Tags:</div>
            <div><?php echo htmlspecialchars(viteCssTag('history')); ?></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">Generated JS Tags:</div>
            <div><?php echo htmlspecialchars(viteScriptTag('history')); ?></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">Vite Client Script:</div>
            <div><?php echo htmlspecialchars(viteClientScript()); ?></div>
        </div>
    </div>
    
    <!-- Test if CSS is working -->
    <div class="dashboard-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3>Test Sidebar</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="#">
                            <span class="material-icons-round">dashboard</span>
                            <span>Test Link</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <main class="main-content">
            <header class="status-header">
                <div class="status-title">
                    <div class="subtitle">CSS Test Page</div>
                </div>
                <div class="status-boxes">
                    <div class="status-box">
                        <div class="status-icon">
                            <span class="material-icons-round">check_circle</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">CSS Status</div>
                            <div class="status-value">Testing</div>
                        </div>
                    </div>
                </div>
            </header>
            
            <div class="history-container">
                <h2>CSS Test Container</h2>
                <p>If you can see this styled properly with a dark futuristic theme, the CSS is working!</p>
                
                <button class="button button-primary">
                    <span class="material-icons-round">play_arrow</span>
                    Test Button
                </button>
            </div>
        </main>
    </div>
</body>
</html>
