<?php
// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

try {
    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Get parameters
    $start_date = $_GET['start_date'] ?? '';
    $end_date = $_GET['end_date'] ?? '';
    $export_format = $_GET['export'] ?? 'csv';
    
    // Validate dates
    if (empty($start_date) || empty($end_date)) {
        throw new Exception("Start date and end date are required");
    }
    
    // Build WHERE clause
    $where_conditions = [];
    $params = [];
    $types = '';
    
    if (!empty($start_date)) {
        $where_conditions[] = "timestamp >= ?";
        $params[] = $start_date;
        $types .= 's';
    }
    
    if (!empty($end_date)) {
        $where_conditions[] = "timestamp <= ?";
        $params[] = $end_date;
        $types .= 's';
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get data
    $sql = "SELECT * FROM electrical_data $where_clause ORDER BY timestamp ASC";
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($export_format === 'csv') {
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="electrical_data_' . date('Y-m-d_H-i-s') . '.csv"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
        
        // Create file pointer connected to the output stream
        $output = fopen('php://output', 'w');
        
        // Add CSV headers
        fputcsv($output, [
            'Timestamp',
            'Voltage 1 (V)',
            'Voltage 2 (V)',
            'Voltage 3 (V)',
            'Current 1 (A)',
            'Current 2 (A)',
            'Current 3 (A)',
            'Power Factor 1',
            'Power Factor 2',
            'Power Factor 3',
            'KVA 1',
            'KVA 2',
            'KVA 3',
            'Total KVA',
            'Total KW',
            'Total KVAR',
            'Frequency (Hz)'
        ]);
        
        // Add data rows
        while ($row = $result->fetch_assoc()) {
            // Convert timestamp to IST
            $timestamp = new DateTime($row["timestamp"]);
            $timestamp_ist = $timestamp->format('Y-m-d H:i:s');
            
            fputcsv($output, [
                $timestamp_ist,
                number_format($row["voltage_1"], 2),
                number_format($row["voltage_2"], 2),
                number_format($row["voltage_3"], 2),
                number_format($row["current_1"], 3),
                number_format($row["current_2"], 3),
                number_format($row["current_3"], 3),
                number_format($row["pf_1"], 3),
                number_format($row["pf_2"], 3),
                number_format($row["pf_3"], 3),
                number_format($row["kva_1"], 2),
                number_format($row["kva_2"], 2),
                number_format($row["kva_3"], 2),
                number_format($row["total_kva"], 2),
                number_format($row["total_kw"], 2),
                number_format($row["total_kvar"], 2),
                number_format($row["frequency"], 2)
            ]);
        }
        
        fclose($output);
        
    } else {
        // JSON export
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="electrical_data_' . date('Y-m-d_H-i-s') . '.json"');
        
        $data = [];
        while ($row = $result->fetch_assoc()) {
            // Convert timestamp to IST and format data
            $timestamp = new DateTime($row["timestamp"]);
            $timestamp_ist = $timestamp->format('Y-m-d H:i:s');
            
            $data[] = [
                "timestamp" => $timestamp_ist,
                "voltage_1" => floatval($row["voltage_1"]),
                "voltage_2" => floatval($row["voltage_2"]),
                "voltage_3" => floatval($row["voltage_3"]),
                "current_1" => floatval($row["current_1"]),
                "current_2" => floatval($row["current_2"]),
                "current_3" => floatval($row["current_3"]),
                "pf_1" => floatval($row["pf_1"]),
                "pf_2" => floatval($row["pf_2"]),
                "pf_3" => floatval($row["pf_3"]),
                "kva_1" => floatval($row["kva_1"]),
                "kva_2" => floatval($row["kva_2"]),
                "kva_3" => floatval($row["kva_3"]),
                "total_kva" => floatval($row["total_kva"]),
                "total_kw" => floatval($row["total_kw"]),
                "total_kvar" => floatval($row["total_kvar"]),
                "frequency" => floatval($row["frequency"])
            ];
        }
        
        echo json_encode([
            'export_info' => [
                'format' => 'json',
                'generated_at' => date('Y-m-d H:i:s'),
                'total_records' => count($data),
                'date_range' => [
                    'start' => $start_date,
                    'end' => $end_date
                ]
            ],
            'data' => $data
        ], JSON_PRETTY_PRINT);
    }
    
    $stmt->close();
    
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
