"use strict";(self.webpackChunkpower_monitor_pro_dashboard=self.webpackChunkpower_monitor_pro_dashboard||[]).push([[952],{952:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(540);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(523);\n/* harmony import */ var react_chartjs_2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(731);\nvar _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;\nfunction _taggedTemplateLiteral(e, t) { return t || (t = e.slice(0)), Object.freeze(Object.defineProperties(e, { raw: { value: Object.freeze(t) } })); }\n\n\n\nvar FullscreenWidget = function FullscreenWidget(_ref) {\n  var isOpen = _ref.isOpen,\n    title = _ref.title,\n    icon = _ref.icon,\n    chartId = _ref.chartId,\n    sourceChartId = _ref.sourceChartId,\n    onClose = _ref.onClose;\n  var chartRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (isOpen && sourceChartId) {\n      var _document$getElementB;\n      // Clone the source chart\'s data and options\n      var sourceChart = (_document$getElementB = document.getElementById(sourceChartId)) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.chart;\n      if (sourceChart && chartRef.current) {\n        var chart = chartRef.current;\n\n        // Copy data and options from source chart\n        chart.data = JSON.parse(JSON.stringify(sourceChart.data));\n        chart.options = JSON.parse(JSON.stringify(sourceChart.options));\n\n        // Update the chart\n        chart.update();\n      }\n    }\n  }, [isOpen, sourceChartId]);\n  if (!isOpen) return null;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(FullscreenWidgetContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(FullscreenHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(FullscreenTitle, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {\n    className: "material-icons-round"\n  }, icon), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h3", null, title)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(FullscreenControls, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(FullscreenControl, {\n    onClick: onClose\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {\n    className: "material-icons-round"\n  }, "close")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(FullscreenContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_chartjs_2__WEBPACK_IMPORTED_MODULE_1__/* .Line */ .N1, {\n    id: chartId ? "fullscreen".concat(chartId) : \'fullscreenChart\',\n    ref: chartRef,\n    data: {\n      datasets: []\n    } // Initial empty data, will be populated from source chart\n    ,\n    options: {} // Initial empty options, will be populated from source chart\n  })));\n};\nvar FullscreenWidgetContainer = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = _taggedTemplateLiteral(["\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: var(--background-color);\\n  z-index: 1001;\\n  display: flex;\\n  flex-direction: column;\\n"])));\nvar FullscreenHeader = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = _taggedTemplateLiteral(["\\n  padding: 1.25rem 1.5rem;\\n  background-color: var(--card-color);\\n  border-bottom: 1px solid var(--border-color);\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  box-shadow: var(--shadow-md);\\n"])));\nvar FullscreenTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = _taggedTemplateLiteral(["\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  \\n  span {\\n    color: var(--primary-color);\\n    font-size: 1.5rem;\\n    width: 2.5rem;\\n    height: 2.5rem;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    border-radius: 50%;\\n    background-color: rgba(0, 86, 179, 0.1);\\n  }\\n  \\n  h3 {\\n    font-size: 1.25rem;\\n    font-weight: var(--font-weight-semibold);\\n    color: var(--text-color);\\n    letter-spacing: -0.01em;\\n  }\\n"])));\nvar FullscreenControls = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = _taggedTemplateLiteral(["\\n  display: flex;\\n  gap: 0.5rem;\\n"])));\nvar FullscreenControl = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.button(_templateObject5 || (_templateObject5 = _taggedTemplateLiteral(["\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: none;\\n  border: 1px solid var(--border-color);\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n  \\n  &:hover {\\n    background-color: var(--hover-color);\\n    color: var(--accent-color);\\n    border-color: var(--accent-color);\\n  }\\n"])));\nvar FullscreenContent = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = _taggedTemplateLiteral(["\\n  flex: 1;\\n  padding: 1.5rem;\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  \\n  canvas {\\n    flex: 1;\\n    width: 100%;\\n    height: auto !important;\\n    max-height: calc(100vh - 10rem);\\n  }\\n"])));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FullscreenWidget);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///952\n')}}]);