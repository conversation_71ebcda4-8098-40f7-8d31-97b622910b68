/**
 * Alerts & Events Page JavaScript
 * Light mode optimized for Online Data Logger System
 */

// Global variables
let currentAlerts = [];
let filteredAlerts = [];
let currentFilter = 'all';

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Alerts & Events page...');
    
    setupEventListeners();
    loadAlerts();
    updateSummary();
});

function setupEventListeners() {
    // Severity filters
    document.querySelectorAll('.severity-filter').forEach(filter => {
        filter.addEventListener('click', function() {
            document.querySelectorAll('.severity-filter').forEach(f => f.classList.remove('active'));
            this.classList.add('active');
            
            currentFilter = this.dataset.severity || 'all';
            filterAlerts();
        });
    });

    // Auto-refresh alerts every 30 seconds
    setInterval(loadAlerts, 30000);
}

async function loadAlerts() {
    try {
        const response = await fetch('../backend/alerts_events.php');
        const result = await response.json();
        
        if (result.error) {
            throw new Error(result.error);
        }
        
        currentAlerts = result.alerts || [];
        filterAlerts();
        updateSummary();
        
    } catch (error) {
        console.error('Error loading alerts:', error);
        showMessage('Error loading alerts: ' + error.message, 'error');
        loadSampleAlerts(); // Fallback to sample data
    }
}

function loadSampleAlerts() {
    // Sample alerts for demonstration
    currentAlerts = [
        {
            id: 1,
            severity: 'critical',
            title: 'Voltage Out of Range',
            description: 'Phase 1 voltage has exceeded safe operating limits (450V)',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            status: 'active',
            parameter: 'voltage_1',
            value: 452.3,
            threshold: 450
        },
        {
            id: 2,
            severity: 'warning',
            title: 'High Current Draw',
            description: 'Phase 2 current is approaching maximum capacity',
            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
            status: 'acknowledged',
            parameter: 'current_2',
            value: 18.7,
            threshold: 20
        },
        {
            id: 3,
            severity: 'info',
            title: 'Power Factor Low',
            description: 'Power factor has dropped below optimal range',
            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            status: 'resolved',
            parameter: 'pf_1',
            value: 0.75,
            threshold: 0.8
        },
        {
            id: 4,
            severity: 'warning',
            title: 'Frequency Deviation',
            description: 'System frequency is fluctuating outside normal range',
            timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
            status: 'active',
            parameter: 'frequency',
            value: 50.8,
            threshold: 50.5
        }
    ];
    
    filterAlerts();
    updateSummary();
}

function filterAlerts() {
    if (currentFilter === 'all') {
        filteredAlerts = [...currentAlerts];
    } else {
        filteredAlerts = currentAlerts.filter(alert => alert.severity === currentFilter);
    }
    
    displayAlerts();
}

function displayAlerts() {
    const alertList = document.querySelector('.alert-list');
    if (!alertList) return;
    
    if (filteredAlerts.length === 0) {
        alertList.innerHTML = `
            <div class="empty-state">
                <span class="material-icons-round">notifications_off</span>
                <h3>No Alerts</h3>
                <p>No alerts found for the selected filter.</p>
            </div>
        `;
        return;
    }
    
    const alertItems = filteredAlerts.map(alert => `
        <div class="alert-item" data-alert-id="${alert.id}">
            <div class="alert-severity ${alert.severity}">
                <span class="material-icons-round">${getSeverityIcon(alert.severity)}</span>
            </div>
            
            <div class="alert-content">
                <div class="alert-title">${alert.title}</div>
                <div class="alert-description">${alert.description}</div>
            </div>
            
            <div class="alert-meta">
                <div class="alert-time">${formatTime(alert.timestamp)}</div>
                <div class="alert-status ${alert.status}">${formatStatus(alert.status)}</div>
                <div class="alert-actions">
                    ${getActionButtons(alert)}
                </div>
            </div>
        </div>
    `).join('');
    
    alertList.innerHTML = alertItems;
    
    // Add event listeners to action buttons
    setupActionButtons();
}

function setupActionButtons() {
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            
            const alertId = parseInt(this.closest('.alert-item').dataset.alertId);
            const action = this.dataset.action;
            
            handleAlertAction(alertId, action);
        });
    });
}

function handleAlertAction(alertId, action) {
    const alert = currentAlerts.find(a => a.id === alertId);
    if (!alert) return;
    
    switch (action) {
        case 'acknowledge':
            alert.status = 'acknowledged';
            showMessage(`Alert "${alert.title}" acknowledged`, 'success');
            break;
            
        case 'resolve':
            alert.status = 'resolved';
            showMessage(`Alert "${alert.title}" resolved`, 'success');
            break;
            
        case 'details':
            showAlertDetails(alert);
            return;
    }
    
    // Update display
    filterAlerts();
    updateSummary();
    
    // In a real application, you would send this to the backend
    // updateAlertStatus(alertId, alert.status);
}

function showAlertDetails(alert) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Alert Details</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="detail-row">
                    <strong>Title:</strong> ${alert.title}
                </div>
                <div class="detail-row">
                    <strong>Severity:</strong> <span class="severity-badge ${alert.severity}">${alert.severity.toUpperCase()}</span>
                </div>
                <div class="detail-row">
                    <strong>Description:</strong> ${alert.description}
                </div>
                <div class="detail-row">
                    <strong>Parameter:</strong> ${alert.parameter}
                </div>
                <div class="detail-row">
                    <strong>Current Value:</strong> ${alert.value}
                </div>
                <div class="detail-row">
                    <strong>Threshold:</strong> ${alert.threshold}
                </div>
                <div class="detail-row">
                    <strong>Time:</strong> ${new Date(alert.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}
                </div>
                <div class="detail-row">
                    <strong>Status:</strong> ${formatStatus(alert.status)}
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal handlers
    modal.querySelector('.modal-close').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

function updateSummary() {
    const summary = {
        critical: currentAlerts.filter(a => a.severity === 'critical' && a.status === 'active').length,
        warning: currentAlerts.filter(a => a.severity === 'warning' && a.status === 'active').length,
        info: currentAlerts.filter(a => a.severity === 'info' && a.status === 'active').length,
        resolved: currentAlerts.filter(a => a.status === 'resolved').length
    };
    
    // Update summary cards
    updateSummaryCard('critical', summary.critical);
    updateSummaryCard('warning', summary.warning);
    updateSummaryCard('info', summary.info);
    updateSummaryCard('success', summary.resolved);
}

function updateSummaryCard(type, count) {
    const card = document.querySelector(`.summary-card.${type}`);
    if (card) {
        const countElement = card.querySelector('.summary-count');
        if (countElement) {
            countElement.textContent = count;
        }
    }
}

function getSeverityIcon(severity) {
    const icons = {
        critical: 'error',
        warning: 'warning',
        info: 'info'
    };
    return icons[severity] || 'info';
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString('en-IN');
}

function formatStatus(status) {
    const statusMap = {
        active: 'Active',
        acknowledged: 'Acknowledged',
        resolved: 'Resolved'
    };
    return statusMap[status] || status;
}

function getActionButtons(alert) {
    const buttons = [];
    
    if (alert.status === 'active') {
        buttons.push('<button class="action-btn acknowledge" data-action="acknowledge">Acknowledge</button>');
        buttons.push('<button class="action-btn resolve" data-action="resolve">Resolve</button>');
    } else if (alert.status === 'acknowledged') {
        buttons.push('<button class="action-btn resolve" data-action="resolve">Resolve</button>');
    }
    
    buttons.push('<button class="action-btn" data-action="details">Details</button>');
    
    return buttons.join('');
}

function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    if (!container) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.innerHTML = `
        <span class="material-icons-round">${getMessageIcon(type)}</span>
        ${message}
    `;
    
    container.innerHTML = '';
    container.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

function getMessageIcon(type) {
    switch (type) {
        case 'success': return 'check_circle';
        case 'error': return 'error';
        case 'warning': return 'warning';
        default: return 'info';
    }
}

// Add modal styles
const modalStyles = `
<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    color: #1e293b;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #64748b;
}

.modal-body {
    padding: 20px;
}

.detail-row {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f1f5f9;
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.severity-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.severity-badge.critical {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.severity-badge.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.severity-badge.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', modalStyles);
