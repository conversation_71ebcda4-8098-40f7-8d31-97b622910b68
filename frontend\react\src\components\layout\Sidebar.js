import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../contexts/ThemeContext';

const Sidebar = ({
  onSettingsClick,
  onCustomizeClick,
  onAnalyticsClick,
  onThemeBuilderClick,
  isMobileView
}) => {
  const { darkMode, toggleDarkMode } = useTheme();
  const [collapsed, setCollapsed] = useState(isMobileView);

  // Toggle sidebar collapse
  const toggleCollapse = () => {
    setCollapsed(prev => !prev);
  };

  // Update collapsed state when isMobileView changes
  useEffect(() => {
    setCollapsed(isMobileView);
  }, [isMobileView]);

  return (
    <SidebarContainer className={collapsed ? 'sidebar-collapsed' : ''}>
      <SidebarHeader>
        <CompanyLogo src="../images/company-logo.svg" alt="PowerMonitor Pro" />
        <CollapseButton onClick={toggleCollapse}>
          <span className="material-icons-round">
            {collapsed ? 'menu' : 'menu_open'}
          </span>
        </CollapseButton>
      </SidebarHeader>

      <SidebarNav>
        <NavList>
          <NavItem className="active">
            <NavLink href="#">
              <span className="material-icons-round">dashboard</span>
              <span>Live Dashboard</span>
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink href="../history.php">
              <span className="material-icons-round">history</span>
              <span>Historical Data</span>
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink href="#" onClick={onAnalyticsClick}>
              <span className="material-icons-round">analytics</span>
              <span>Analytics</span>
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink href="#">
              <span className="material-icons-round">warning</span>
              <span>Alerts & Events</span>
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink href="#">
              <span className="material-icons-round">insights</span>
              <span>Reports</span>
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink href="#" onClick={onCustomizeClick}>
              <span className="material-icons-round">dashboard_customize</span>
              <span>Customize</span>
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink href="#" onClick={onThemeBuilderClick}>
              <span className="material-icons-round">palette</span>
              <span>Theme Builder</span>
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink href="#" onClick={onSettingsClick}>
              <span className="material-icons-round">settings</span>
              <span>System Settings</span>
            </NavLink>
          </NavItem>
        </NavList>
      </SidebarNav>

      <SidebarFooter>
        <ThemeToggle onClick={toggleDarkMode}>
          <span className="material-icons-round">
            {darkMode ? 'light_mode' : 'dark_mode'}
          </span>
          <span>{darkMode ? 'Light Mode' : 'Dark Mode'}</span>
        </ThemeToggle>
        <SystemStatus>
          <StatusIndicator className="online" />
          <span>System Online</span>
        </SystemStatus>
      </SidebarFooter>
    </SidebarContainer>
  );
};

const SidebarContainer = styled.aside`
  width: 280px;
  background-color: var(--card-color);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  z-index: 100;
  transition: all var(--animation-medium) ease;
  box-shadow: var(--shadow-md);

  &.sidebar-collapsed {
    width: 5rem;
    overflow: hidden;
  }

  @media (max-width: 992px) {
    width: 5rem;
    overflow: hidden;
  }
`;

const SidebarHeader = styled.div`
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);

  ${SidebarContainer}.sidebar-collapsed & {
    justify-content: center;
    padding: 1.25rem 0;
  }

  @media (max-width: 992px) {
    justify-content: center;
    padding: 1.25rem 0;
  }
`;

const CollapseButton = styled.button`
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--animation-medium) ease;

  &:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
    border-color: var(--primary-light);
  }

  ${SidebarContainer}.sidebar-collapsed & {
    display: none;
  }

  @media (max-width: 992px) {
    display: none;
  }
`;

const CompanyLogo = styled.img`
  max-width: 100%;
  height: auto;
  display: block;
  transition: all var(--animation-medium) ease;

  ${SidebarContainer}.sidebar-collapsed & {
    width: 2.5rem;
  }

  @media (max-width: 992px) {
    width: 2.5rem;
  }
`;

const SidebarNav = styled.nav`
  flex: 1;
  padding: 1.25rem 0;
  overflow-y: auto;
`;

const NavList = styled.ul`
  list-style: none;
`;

const NavItem = styled.li`
  margin-bottom: 0.25rem;
  padding: 0 0.75rem;

  &.active a {
    background-color: rgba(0, 86, 179, 0.08);
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);

    &::before {
      opacity: 1;
    }
  }
`;

const NavLink = styled.a`
  display: flex;
  align-items: center;
  padding: 0.875rem 1.25rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: all var(--animation-medium) ease;
  gap: 0.875rem;
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0.25rem;
    background: var(--gradient-primary);
    border-radius: 0.125rem;
    opacity: 0;
    transition: opacity var(--animation-medium) ease;
  }

  &:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
  }

  ${SidebarContainer}.sidebar-collapsed & {
    justify-content: center;
    padding: 0.875rem 0;

    span:not(.material-icons-round) {
      display: none;
    }

    &::before {
      width: 0.25rem;
      height: 1.5rem;
      top: calc(50% - 0.75rem);
    }
  }

  @media (max-width: 992px) {
    justify-content: center;
    padding: 0.875rem 0;

    span:not(.material-icons-round) {
      display: none;
    }

    &::before {
      width: 0.25rem;
      height: 1.5rem;
      top: calc(50% - 0.75rem);
    }
  }
`;

const SidebarFooter = styled.div`
  padding: 1.25rem;
  border-top: 1px solid var(--border-color);
`;

const ThemeToggle = styled.button`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  width: 100%;
  transition: all var(--animation-medium) ease;
  margin-bottom: 1rem;
  font-weight: var(--font-weight-medium);

  &:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-light);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  ${SidebarContainer}.sidebar-collapsed & {
    justify-content: center;
    padding: 0.75rem;

    span:not(.material-icons-round) {
      display: none;
    }
  }

  @media (max-width: 992px) {
    justify-content: center;
    padding: 0.75rem;

    span:not(.material-icons-round) {
      display: none;
    }
  }
`;

const SystemStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.8125rem;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius);
  background-color: var(--hover-color);
  transition: all var(--animation-medium) ease;

  &:hover {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
  }

  ${SidebarContainer}.sidebar-collapsed & {
    justify-content: center;

    span:not(.status-indicator) {
      display: none;
    }
  }

  @media (max-width: 992px) {
    justify-content: center;

    span:not(.status-indicator) {
      display: none;
    }
  }
`;

const StatusIndicator = styled.span`
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: var(--success-color);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: -0.125rem;
    left: -0.125rem;
    right: -0.125rem;
    bottom: -0.125rem;
    border-radius: 50%;
    background-color: var(--success-color);
    opacity: 0.3;
    animation: pulse 2s infinite;
  }

  &.online {
    background-color: var(--success-color);

    &::after {
      background-color: var(--success-color);
    }
  }

  &.offline {
    background-color: var(--accent-color);

    &::after {
      background-color: var(--accent-color);
    }
  }

  &.warning {
    background-color: var(--warning-color);

    &::after {
      background-color: var(--warning-color);
    }
  }

  ${SystemStatus}:hover &.online::after {
    animation: pulse 1s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 0.3;
    }
    70% {
      transform: scale(1.5);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 0;
    }
  }
`;

export default Sidebar;
