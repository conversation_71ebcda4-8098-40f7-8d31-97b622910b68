<?php
// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if database exists
$result = $conn->query("SHOW DATABASES LIKE '$dbname'");
if ($result->num_rows == 0) {
    echo "Creating database $dbname...<br>";
    $conn->query("CREATE DATABASE IF NOT EXISTS $dbname");
}

// Select database
$conn->select_db($dbname);

// Check if table exists
$result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
if ($result->num_rows == 0) {
    echo "Creating table electrical_data...<br>";
    $sql = "CREATE TABLE IF NOT EXISTS electrical_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        voltage_1 FLOAT,
        voltage_2 FLOAT,
        voltage_3 FLOAT,
        current_1 FLOAT,
        current_2 FLOAT,
        current_3 FLOAT,
        pf_1 FLOAT,
        pf_2 FLOAT,
        pf_3 FLOAT,
        kva_1 FLOAT,
        kva_2 FLOAT,
        kva_3 FLOAT,
        total_kva FLOAT,
        total_kw FLOAT,
        total_kvar FLOAT,
        frequency FLOAT,
        timestamp DATETIME
    )";
    $conn->query($sql);
}

// Function to generate data
function generateData($time) {
    $angle = $time * 0.1; // For creating sine wave variations
    
    // Base values
    $baseVoltage = 400;
    $baseCurrent = 50;
    $basePF = 0.9;
    $baseKVA = 20;
    $baseFrequency = 50;
    
    // Create simulated data with some randomness and sine wave patterns
    $data = [
        "voltage_1" => $baseVoltage + sin($angle) * 10 + (rand(-5, 5) / 10),
        "voltage_2" => $baseVoltage + sin($angle + 2.09) * 10 + (rand(-5, 5) / 10), // 120° phase shift
        "voltage_3" => $baseVoltage + sin($angle + 4.19) * 10 + (rand(-5, 5) / 10), // 240° phase shift
        
        "current_1" => $baseCurrent + sin($angle) * 5 + (rand(-10, 10) / 10),
        "current_2" => $baseCurrent + sin($angle + 2.09) * 5 + (rand(-10, 10) / 10),
        "current_3" => $baseCurrent + sin($angle + 4.19) * 5 + (rand(-10, 10) / 10),
        
        "pf_1" => $basePF + sin($angle) * 0.05 + (rand(-5, 5) / 100),
        "pf_2" => $basePF + sin($angle + 2.09) * 0.05 + (rand(-5, 5) / 100),
        "pf_3" => $basePF + sin($angle + 4.19) * 0.05 + (rand(-5, 5) / 100),
        
        "kva_1" => $baseKVA + sin($angle) * 2 + (rand(-5, 5) / 10),
        "kva_2" => $baseKVA + sin($angle + 2.09) * 2 + (rand(-5, 5) / 10),
        "kva_3" => $baseKVA + sin($angle + 4.19) * 2 + (rand(-5, 5) / 10),
    ];
    
    // Calculate total values
    $totalKVA = $data["kva_1"] + $data["kva_2"] + $data["kva_3"];
    $totalKW = $totalKVA * (($data["pf_1"] + $data["pf_2"] + $data["pf_3"]) / 3);
    $totalKVAR = sqrt(pow($totalKVA, 2) - pow($totalKW, 2));
    
    // Add total values to data
    $data["total_kva"] = $totalKVA;
    $data["total_kw"] = $totalKW;
    $data["total_kvar"] = $totalKVAR;
    
    // Add frequency with small variations
    $data["frequency"] = $baseFrequency + sin($angle) * 0.1 + (rand(-5, 5) / 100);
    
    return $data;
}

// Generate and insert data
$count = isset($_GET['count']) ? intval($_GET['count']) : 10;
$interval = isset($_GET['interval']) ? intval($_GET['interval']) : 5;

echo "<h1>Data Generator</h1>";
echo "<p>Generating $count data points with $interval second intervals...</p>";

echo "<div style='height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";

for ($i = 0; $i < $count; $i++) {
    // Generate data
    $data = generateData($i);
    
    // Current timestamp
    $timestamp = date('Y-m-d H:i:s', time() - ($count - $i - 1) * $interval);
    
    // Insert data into database
    $stmt = $conn->prepare("INSERT INTO electrical_data (
        voltage_1, voltage_2, voltage_3,
        current_1, current_2, current_3,
        pf_1, pf_2, pf_3,
        kva_1, kva_2, kva_3,
        total_kva, total_kw, total_kvar,
        frequency, timestamp
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->bind_param("dddddddddddddddds", 
        $data["voltage_1"], $data["voltage_2"], $data["voltage_3"],
        $data["current_1"], $data["current_2"], $data["current_3"],
        $data["pf_1"], $data["pf_2"], $data["pf_3"],
        $data["kva_1"], $data["kva_2"], $data["kva_3"],
        $data["total_kva"], $data["total_kw"], $data["total_kvar"],
        $data["frequency"], $timestamp
    );
    
    if ($stmt->execute()) {
        echo "Data point $i inserted with timestamp: $timestamp<br>";
        flush();
    } else {
        echo "Error inserting data point $i: " . $stmt->error . "<br>";
    }
    
    $stmt->close();
    
    // Sleep for a moment to simulate real-time data
    if ($i < $count - 1) {
        sleep(1);
    }
}

echo "</div>";

// Count records in the database
$result = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
$row = $result->fetch_assoc();
$totalCount = $row['count'];

echo "<p>Total records in database: $totalCount</p>";

// Close the connection
$conn->close();

echo "<p>Done! <a href='frontend/'>Go to Dashboard</a> | <a href='frontend/simple.php'>Go to Simple Dashboard</a></p>";

// Add auto-refresh option
echo "<p><a href='?count=1&interval=0'>Add one more data point</a> | <a href='?count=10&interval=0'>Add 10 more data points</a></p>";

// Add auto-refresh
echo "<script>
    // Auto-refresh every 10 seconds
    setTimeout(function() {
        window.location.href = '?count=1&interval=0';
    }, 10000);
</script>";
?>
