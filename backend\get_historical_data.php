<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Set timezone to India
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

try {
    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Get parameters
    $start_date = $_GET['start_date'] ?? '';
    $end_date = $_GET['end_date'] ?? '';
    $limit = intval($_GET['limit'] ?? 25);
    $page = intval($_GET['page'] ?? 1);
    $offset = ($page - 1) * $limit;
    
    // Validate dates
    if (empty($start_date) || empty($end_date)) {
        throw new Exception("Start date and end date are required");
    }
    
    // Build WHERE clause
    $where_conditions = [];
    $params = [];
    $types = '';
    
    if (!empty($start_date)) {
        $where_conditions[] = "timestamp >= ?";
        $params[] = $start_date;
        $types .= 's';
    }
    
    if (!empty($end_date)) {
        $where_conditions[] = "timestamp <= ?";
        $params[] = $end_date;
        $types .= 's';
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM electrical_data $where_clause";
    $count_stmt = $conn->prepare($count_sql);
    
    if (!empty($params)) {
        $count_stmt->bind_param($types, ...$params);
    }
    
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $limit);
    
    // Get data
    $data_sql = "SELECT * FROM electrical_data $where_clause ORDER BY timestamp DESC LIMIT ? OFFSET ?";
    $data_stmt = $conn->prepare($data_sql);
    
    // Add limit and offset to parameters
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    
    $data_stmt->bind_param($types, ...$params);
    $data_stmt->execute();
    $result = $data_stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        // Convert timestamp to IST and format data
        $timestamp = new DateTime($row["timestamp"]);
        $timestamp->setTimezone(new DateTimeZone('Asia/Kolkata'));
        $timestamp_ist = $timestamp->format('Y-m-d H:i:s');
        
        $data[] = [
            "id" => intval($row["id"]),
            "voltage_1" => floatval($row["voltage_1"]),
            "voltage_2" => floatval($row["voltage_2"]),
            "voltage_3" => floatval($row["voltage_3"]),
            "current_1" => floatval($row["current_1"]),
            "current_2" => floatval($row["current_2"]),
            "current_3" => floatval($row["current_3"]),
            "pf_1" => floatval($row["pf_1"]),
            "pf_2" => floatval($row["pf_2"]),
            "pf_3" => floatval($row["pf_3"]),
            "kva_1" => floatval($row["kva_1"]),
            "kva_2" => floatval($row["kva_2"]),
            "kva_3" => floatval($row["kva_3"]),
            "total_kva" => floatval($row["total_kva"]),
            "total_kw" => floatval($row["total_kw"]),
            "total_kvar" => floatval($row["total_kvar"]),
            "frequency" => floatval($row["frequency"]),
            "timestamp" => $timestamp_ist
        ];
    }
    
    $count_stmt->close();
    $data_stmt->close();
    
    echo json_encode([
        'success' => true,
        'data' => $data,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_records' => $total_records,
            'per_page' => $limit
        ],
        'total_pages' => $total_pages, // For backward compatibility
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
