/* Import base light theme */
@import url('light-theme.css');

/* Alerts & Events Page Specific Styles */
.alerts-container {
    background-color: var(--light-card-bg);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--light-shadow);
}

/* Alert Summary Cards */
.alert-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.summary-card {
    background-color: var(--light-hover);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.2s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--light-shadow);
}

.summary-card.critical {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.summary-card.warning {
    background-color: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.summary-card.info {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.summary-card.success {
    background-color: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
}

.summary-card.critical .summary-icon {
    background-color: var(--accent-danger);
    color: white;
}

.summary-card.warning .summary-icon {
    background-color: var(--accent-warning);
    color: white;
}

.summary-card.info .summary-icon {
    background-color: var(--accent-info);
    color: white;
}

.summary-card.success .summary-icon {
    background-color: var(--accent-success);
    color: white;
}

.summary-count {
    font-size: 2rem;
    font-weight: 700;
    color: var(--light-text);
    margin-bottom: 5px;
}

.summary-label {
    font-size: 0.875rem;
    color: var(--light-text-secondary);
    font-weight: 500;
}

/* Alert Filters */
.alert-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--light-text);
}

.severity-filters {
    display: flex;
    gap: 8px;
}

.severity-filter {
    padding: 6px 12px;
    border: 1px solid var(--light-border);
    border-radius: 16px;
    background-color: var(--light-card-bg);
    color: var(--light-text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.severity-filter:hover {
    background-color: var(--light-hover);
}

.severity-filter.active {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.severity-filter.critical {
    border-color: var(--accent-danger);
    color: var(--accent-danger);
}

.severity-filter.critical.active {
    background-color: var(--accent-danger);
    color: white;
}

.severity-filter.warning {
    border-color: var(--accent-warning);
    color: var(--accent-warning);
}

.severity-filter.warning.active {
    background-color: var(--accent-warning);
    color: white;
}

/* Alert List */
.alert-list {
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 8px;
    overflow: hidden;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    border-bottom: 1px solid var(--light-border);
    transition: all 0.2s ease;
}

.alert-item:hover {
    background-color: var(--light-hover);
}

.alert-item:last-child {
    border-bottom: none;
}

.alert-severity {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.alert-severity.critical {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--accent-danger);
}

.alert-severity.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--accent-warning);
}

.alert-severity.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--accent-info);
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--light-text);
    margin-bottom: 5px;
}

.alert-description {
    font-size: 0.875rem;
    color: var(--light-text-secondary);
    line-height: 1.4;
}

.alert-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
    flex-shrink: 0;
}

.alert-time {
    font-size: 0.75rem;
    color: var(--light-text-secondary);
}

.alert-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.alert-status.active {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--accent-danger);
}

.alert-status.acknowledged {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--accent-warning);
}

.alert-status.resolved {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--accent-success);
}

/* Alert Actions */
.alert-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 8px;
    border: 1px solid var(--light-border);
    border-radius: 4px;
    background-color: var(--light-card-bg);
    color: var(--light-text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.action-btn:hover {
    background-color: var(--light-hover);
    color: var(--accent-primary);
    border-color: var(--accent-primary);
}

.action-btn.acknowledge {
    color: var(--accent-warning);
    border-color: var(--accent-warning);
}

.action-btn.acknowledge:hover {
    background-color: var(--accent-warning);
    color: white;
}

.action-btn.resolve {
    color: var(--accent-success);
    border-color: var(--accent-success);
}

.action-btn.resolve:hover {
    background-color: var(--accent-success);
    color: white;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--light-text-secondary);
}

.empty-state .material-icons-round {
    font-size: 64px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 10px;
    color: var(--light-text);
}

.empty-state p {
    margin: 0;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .alert-summary {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .alert-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .severity-filters {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .alert-item {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 10px;
    }
    
    .alert-meta {
        align-items: center;
        flex-direction: row;
        justify-content: space-between;
    }
}

@media (max-width: 576px) {
    .alerts-container {
        padding: 15px;
    }
    
    .alert-summary {
        grid-template-columns: 1fr;
    }
    
    .alert-item {
        padding: 15px;
    }
    
    .alert-actions {
        justify-content: center;
        width: 100%;
    }
}
