import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import Modal from './Modal';
import axios from 'axios';
import {
  downloadCSV,
  formatTimestampForExport,
  formatNumberForExport,
  generateCSVFilename,
  fetchAllPagesForExport
} from '../../utils/exportUtils';

const ExportModal = ({ isOpen, onClose, addNotification }) => {
  const [exportType, setExportType] = useState('csv');
  const [timeRange, setTimeRange] = useState('visible');
  const [includeAllParameters, setIncludeAllParameters] = useState(true);
  const [customDateRange, setCustomDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState({
    current: 0,
    total: 0,
    percentage: 0
  });

  // Reference to cancel export
  const cancelExportRef = useRef(false);

  // Handle export type change
  const handleExportTypeChange = (e) => {
    setExportType(e.target.value);
  };

  // Handle time range change
  const handleTimeRangeChange = (e) => {
    setTimeRange(e.target.value);
  };

  // Handle include all parameters change
  const handleIncludeAllParametersChange = (e) => {
    setIncludeAllParameters(e.target.checked);
  };

  // Handle custom date range change
  const handleCustomDateRangeChange = (e) => {
    const { name, value } = e.target;
    setCustomDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle export
  const handleExport = async () => {
    // Reset cancel flag
    cancelExportRef.current = false;

    // Show loading notification and set exporting state
    addNotification('Preparing data for export...', 'info');
    setIsExporting(true);
    setExportProgress({ current: 0, total: 0, percentage: 0 });

    try {
      // Calculate date range
      let startDate, endDate;

      switch (timeRange) {
        case 'visible':
          // Use currently visible data range (fallback to last hour)
          endDate = new Date();
          startDate = new Date(endDate.getTime() - (60 * 60 * 1000)); // 1 hour ago
          break;
        case 'hour':
          endDate = new Date();
          startDate = new Date(endDate.getTime() - (60 * 60 * 1000)); // 1 hour ago
          break;
        case 'day':
          endDate = new Date();
          startDate = new Date(endDate.getTime() - (24 * 60 * 60 * 1000)); // 24 hours ago
          break;
        case 'week':
          endDate = new Date();
          startDate = new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000)); // 7 days ago
          break;
        case 'custom':
          startDate = new Date(customDateRange.startDate);
          endDate = new Date(customDateRange.endDate);
          break;
        default:
          endDate = new Date();
          startDate = new Date(endDate.getTime() - (60 * 60 * 1000)); // Default to 1 hour
      }

      // Format dates for API
      const startDateStr = startDate.toISOString();
      const endDateStr = endDate.toISOString();

      // Define fetch function for pagination
      const fetchPage = async (page, pageSize) => {
        // Check if export was cancelled
        if (cancelExportRef.current) {
          throw new Error('Export cancelled');
        }

        // Update progress
        setExportProgress(prev => ({
          ...prev,
          current: (page - 1) * pageSize
        }));

        // Fetch data for this page
        const response = await axios.get(
          `../backend/get_historical_data.php?start=${startDateStr}&end=${endDateStr}&page=${page}&limit=${pageSize}`
        );

        return {
          data: response.data,
          page,
          pageSize
        };
      };

      // Progress callback
      const onProgress = (progress) => {
        // Calculate percentage
        const percentage = progress.totalItems > 0
          ? Math.round((progress.totalItems / (progress.totalItems + (progress.hasMore ? pageSize : 0))) * 100)
          : 0;

        // Update progress state
        setExportProgress({
          current: progress.totalItems,
          total: progress.totalItems + (progress.hasMore ? pageSize : 0),
          percentage
        });
      };

      // Fetch all pages of data
      const pageSize = 1000; // Fetch 1000 records at a time
      const allData = await fetchAllPagesForExport(
        (page, limit) => fetchPage(page, limit),
        pageSize,
        onProgress
      );

      // Check if export was cancelled
      if (cancelExportRef.current) {
        throw new Error('Export cancelled');
      }

      // Check if we have data
      if (!allData || allData.length === 0) {
        addNotification('No data found for the selected time range', 'warning');
        setIsExporting(false);
        return;
      }

      // Update progress to 100%
      setExportProgress({
        current: allData.length,
        total: allData.length,
        percentage: 100
      });

      // Process data based on export type
      switch (exportType) {
        case 'csv':
          await exportAsCSV(allData, includeAllParameters, startDate, endDate);
          break;
        case 'json':
          await exportAsJSON(allData, includeAllParameters, startDate, endDate);
          break;
        case 'pdf':
          addNotification('PDF export is not yet implemented', 'warning');
          break;
        default:
          await exportAsCSV(allData, includeAllParameters, startDate, endDate);
      }

      // Reset exporting state
      setIsExporting(false);

      // Close modal
      onClose();
    } catch (error) {
      console.error('Error exporting data:', error);

      // Show error notification only if not cancelled
      if (error.message !== 'Export cancelled') {
        addNotification('Failed to export data: ' + error.message, 'error');
      }

      // Reset exporting state
      setIsExporting(false);
    }
  };

  // Cancel export
  const handleCancelExport = () => {
    cancelExportRef.current = true;
    addNotification('Export cancelled', 'info');
  };

  // Export as CSV
  const exportAsCSV = async (data, includeAllParameters, startDate, endDate) => {
    if (!data || !Array.isArray(data)) {
      addNotification('No data to export', 'warning');
      return;
    }

    // Define headers based on includeAllParameters
    let headers = [];

    if (includeAllParameters) {
      headers = [
        { key: 'timestamp', label: 'Timestamp' },
        { key: 'voltage_1', label: 'Voltage Phase 1 (V)' },
        { key: 'voltage_2', label: 'Voltage Phase 2 (V)' },
        { key: 'voltage_3', label: 'Voltage Phase 3 (V)' },
        { key: 'current_1', label: 'Current Phase 1 (A)' },
        { key: 'current_2', label: 'Current Phase 2 (A)' },
        { key: 'current_3', label: 'Current Phase 3 (A)' },
        { key: 'pf_1', label: 'Power Factor Phase 1' },
        { key: 'pf_2', label: 'Power Factor Phase 2' },
        { key: 'pf_3', label: 'Power Factor Phase 3' },
        { key: 'kva_1', label: 'KVA Phase 1' },
        { key: 'kva_2', label: 'KVA Phase 2' },
        { key: 'kva_3', label: 'KVA Phase 3' },
        { key: 'total_kw', label: 'Total KW' },
        { key: 'total_kva', label: 'Total KVA' },
        { key: 'total_kvar', label: 'Total KVAR' },
        { key: 'frequency', label: 'Frequency (Hz)' }
      ];
    } else {
      // Only include essential parameters
      headers = [
        { key: 'timestamp', label: 'Timestamp' },
        { key: 'voltage_avg', label: 'Average Voltage (V)' },
        { key: 'current_total', label: 'Total Current (A)' },
        { key: 'pf_avg', label: 'Average Power Factor' },
        { key: 'total_kw', label: 'Total KW' },
        { key: 'total_kva', label: 'Total KVA' },
        { key: 'frequency', label: 'Frequency (Hz)' }
      ];

      // Calculate derived values
      data = data.map(item => {
        const voltageAvg = ((parseFloat(item.voltage_1) + parseFloat(item.voltage_2) + parseFloat(item.voltage_3)) / 3).toFixed(3);
        const currentTotal = (parseFloat(item.current_1) + parseFloat(item.current_2) + parseFloat(item.current_3)).toFixed(3);
        const pfAvg = ((parseFloat(item.pf_1) + parseFloat(item.pf_2) + parseFloat(item.pf_3)) / 3).toFixed(3);

        return {
          ...item,
          voltage_avg: voltageAvg,
          current_total: currentTotal,
          pf_avg: pfAvg
        };
      });
    }

    // Format data for CSV
    const formattedData = data.map(item => {
      const formattedItem = { ...item };

      // Format timestamp
      formattedItem.timestamp = formatTimestampForExport(item.timestamp);

      // Format numeric values with 3 decimal places
      headers.forEach(header => {
        if (header.key !== 'timestamp' && formattedItem[header.key] !== undefined) {
          formattedItem[header.key] = formatNumberForExport(formattedItem[header.key], 3);
        }
      });

      return formattedItem;
    });

    // Generate filename
    const filename = generateCSVFilename('power_data', startDate, endDate);

    // Download CSV
    downloadCSV(formattedData, headers, filename);

    // Show success notification
    addNotification(`Data exported successfully as CSV (${data.length} records)`, 'success');
  };

  // Export as JSON
  const exportAsJSON = async (data, includeAllParameters, startDate, endDate) => {
    if (!data || !Array.isArray(data)) {
      addNotification('No data to export', 'warning');
      return;
    }

    let exportData;

    if (includeAllParameters) {
      // Export all data as is
      exportData = data;
    } else {
      // Only include essential parameters with calculated values
      exportData = data.map(item => {
        const voltageAvg = ((parseFloat(item.voltage_1) + parseFloat(item.voltage_2) + parseFloat(item.voltage_3)) / 3).toFixed(3);
        const currentTotal = (parseFloat(item.current_1) + parseFloat(item.current_2) + parseFloat(item.current_3)).toFixed(3);
        const pfAvg = ((parseFloat(item.pf_1) + parseFloat(item.pf_2) + parseFloat(item.pf_3)) / 3).toFixed(3);

        return {
          timestamp: formatTimestampForExport(item.timestamp),
          voltage_avg: parseFloat(voltageAvg),
          current_total: parseFloat(currentTotal),
          pf_avg: parseFloat(pfAvg),
          total_kw: parseFloat(formatNumberForExport(item.total_kw, 3)),
          total_kva: parseFloat(formatNumberForExport(item.total_kva, 3)),
          frequency: parseFloat(formatNumberForExport(item.frequency, 3))
        };
      });
    }

    // Add metadata
    const jsonExport = {
      metadata: {
        exportDate: new Date().toISOString(),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        recordCount: data.length,
        exportType: includeAllParameters ? 'full' : 'summary'
      },
      data: exportData
    };

    // Create JSON content
    const jsonContent = JSON.stringify(jsonExport, null, 2);

    // Generate filename
    const filename = `power_data_${startDate.toISOString().split('T')[0]}_to_${endDate.toISOString().split('T')[0]}.json`;

    // Create download link
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success notification
    addNotification(`Data exported successfully as JSON (${data.length} records)`, 'success');
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Export Data"
    >
      <ExportOptions>
        <ExportOption>
          <input
            type="radio"
            name="exportType"
            id="exportCSV"
            value="csv"
            checked={exportType === 'csv'}
            onChange={handleExportTypeChange}
          />
          <label htmlFor="exportCSV">
            <span className="material-icons-round">description</span>
            <div>
              <h4>CSV File</h4>
              <p>Export data as comma-separated values</p>
            </div>
          </label>
        </ExportOption>

        <ExportOption>
          <input
            type="radio"
            name="exportType"
            id="exportJSON"
            value="json"
            checked={exportType === 'json'}
            onChange={handleExportTypeChange}
          />
          <label htmlFor="exportJSON">
            <span className="material-icons-round">data_object</span>
            <div>
              <h4>JSON File</h4>
              <p>Export data in JSON format</p>
            </div>
          </label>
        </ExportOption>

        <ExportOption>
          <input
            type="radio"
            name="exportType"
            id="exportPDF"
            value="pdf"
            checked={exportType === 'pdf'}
            onChange={handleExportTypeChange}
          />
          <label htmlFor="exportPDF">
            <span className="material-icons-round">picture_as_pdf</span>
            <div>
              <h4>PDF Report</h4>
              <p>Export data as a PDF report</p>
            </div>
          </label>
        </ExportOption>
      </ExportOptions>

      <ExportSettings>
        <h3>Export Settings</h3>
        <SettingItem>
          <label htmlFor="exportTimeRange">Time Range</label>
          <select
            id="exportTimeRange"
            value={timeRange}
            onChange={handleTimeRangeChange}
          >
            <option value="visible">Currently Visible Data</option>
            <option value="hour">Last Hour</option>
            <option value="day">Last 24 Hours</option>
            <option value="week">Last Week</option>
            <option value="custom">Custom Range</option>
          </select>
        </SettingItem>

        {timeRange === 'custom' && (
          <DateRange>
            <SettingItem>
              <label htmlFor="startDate">Start Date</label>
              <input
                type="datetime-local"
                id="startDate"
                name="startDate"
                value={customDateRange.startDate}
                onChange={handleCustomDateRangeChange}
              />
            </SettingItem>
            <SettingItem>
              <label htmlFor="endDate">End Date</label>
              <input
                type="datetime-local"
                id="endDate"
                name="endDate"
                value={customDateRange.endDate}
                onChange={handleCustomDateRangeChange}
              />
            </SettingItem>
          </DateRange>
        )}

        <SettingItem className="checkbox">
          <input
            type="checkbox"
            id="includeAllParameters"
            checked={includeAllParameters}
            onChange={handleIncludeAllParametersChange}
          />
          <label htmlFor="includeAllParameters">Include All Parameters</label>
        </SettingItem>
      </ExportSettings>

      {isExporting ? (
        <ExportProgress>
          <ProgressTitle>Exporting Data...</ProgressTitle>
          <ProgressBar>
            <ProgressFill style={{ width: `${exportProgress.percentage}%` }} />
          </ProgressBar>
          <ProgressDetails>
            {exportProgress.current} / {exportProgress.total || '?'} records ({exportProgress.percentage}%)
          </ProgressDetails>
          <ModalButton
            className="secondary"
            onClick={handleCancelExport}
          >
            Cancel Export
          </ModalButton>
        </ExportProgress>
      ) : (
        <ModalFooter>
          <ModalButton
            className="secondary"
            onClick={onClose}
          >
            Cancel
          </ModalButton>
          <ModalButton
            className="primary"
            onClick={handleExport}
          >
            Export Data
          </ModalButton>
        </ModalFooter>
      )}
    </Modal>
  );
};

const ExportOptions = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const ExportOption = styled.div`
  position: relative;

  input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    box-shadow: var(--shadow-sm);
    height: 100%;

    &:hover {
      border-color: var(--primary-light);
      background-color: var(--hover-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    span.material-icons-round {
      font-size: 1.5rem;
      color: var(--primary-color);
      width: 2.5rem;
      height: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: rgba(0, 86, 179, 0.1);
    }

    h4 {
      font-size: 0.9375rem;
      font-weight: var(--font-weight-semibold);
      color: var(--text-color);
      margin-bottom: 0.25rem;
    }

    p {
      font-size: 0.8125rem;
      color: var(--text-secondary);
    }
  }

  input[type="radio"]:checked + label {
    border-color: var(--primary-color);
    background-color: rgba(0, 86, 179, 0.05);
    box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.15);
  }
`;

const ExportSettings = styled.div`
  margin-bottom: 1.5rem;

  h3 {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    letter-spacing: -0.01em;
  }
`;

const SettingItem = styled.div`
  margin-bottom: 1.25rem;

  label {
    display: block;
    font-size: 0.9375rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: 0.5rem;
  }

  input[type="datetime-local"],
  select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9375rem;
    color: var(--text-color);
    background-color: var(--card-color);
    transition: all var(--transition-speed) ease;
    box-shadow: var(--shadow-sm);

    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.15);
      outline: none;
    }
  }

  &.checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    input {
      width: 1.125rem;
      height: 1.125rem;
      accent-color: var(--primary-color);
    }

    label {
      margin-bottom: 0;
      cursor: pointer;
    }
  }
`;

const DateRange = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1rem;
`;

const ModalButton = styled.button`
  padding: 0.625rem 1.25rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
  transition: all var(--transition-speed) ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;

  &.primary {
    background: var(--gradient-primary);
    color: white;
    border: none;

    &:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }
  }

  &.secondary {
    background-color: var(--card-color);
    color: var(--text-color);

    &:hover {
      background-color: var(--hover-color);
      border-color: var(--primary-light);
    }
  }
`;

const ExportProgress = styled.div`
  margin-top: 1.5rem;
  padding: 1.25rem;
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const ProgressTitle = styled.h4`
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin-bottom: 1rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 0.5rem;
  background-color: var(--hover-color);
  border-radius: 9999px;
  overflow: hidden;
  margin-bottom: 0.5rem;
`;

const ProgressFill = styled.div`
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 9999px;
  transition: width 0.3s ease;
`;

const ProgressDetails = styled.div`
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 1.25rem;
`;

export default ExportModal;
