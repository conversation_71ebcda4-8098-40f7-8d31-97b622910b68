/* Import base light theme */
@import url('light-theme.css');

/* Stability Analysis Page Specific Styles */
.stability-analysis-container {
    background-color: var(--light-card-bg);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--light-shadow);
}

/* Analysis Controls */
.analysis-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background-color: var(--light-hover);
    border-radius: 8px;
}

/* Analysis Type Selector */
.analysis-type-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.analysis-type-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 12px 8px;
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 8px;
    color: var(--light-text-secondary);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
}

.analysis-type-btn:hover {
    background-color: var(--light-hover);
    color: var(--accent-primary);
    border-color: var(--accent-primary);
    transform: translateY(-1px);
}

.analysis-type-btn.active {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
    box-shadow: 0 4px 8px rgba(14, 165, 233, 0.3);
}

.analysis-type-btn .material-icons-round {
    font-size: 20px;
}

/* Loading Spinner */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background-color: var(--light-hover);
    border-radius: 8px;
    margin-bottom: 20px;
}

.loading-spinner::before {
    content: '';
    width: 40px;
    height: 40px;
    border: 3px solid var(--light-border);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.loading-spinner span {
    color: var(--light-text-secondary);
    font-size: 0.875rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Stability Metrics Grid */
.stability-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
}

.metric-card:hover {
    box-shadow: var(--light-shadow);
    transform: translateY(-2px);
}

.metric-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(14, 165, 233, 0.1);
    color: var(--accent-primary);
}

.metric-icon .material-icons-round {
    font-size: 20px;
}

.metric-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--light-text);
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--light-text);
    margin-bottom: 5px;
}

.metric-description {
    font-size: 0.875rem;
    color: var(--light-text-secondary);
    line-height: 1.4;
}

.metric-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 10px;
}

.metric-status.good {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--accent-success);
}

.metric-status.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--accent-warning);
}

.metric-status.critical {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--accent-danger);
}

/* Analysis Charts Container */
.analysis-charts-container {
    margin-bottom: 30px;
}

.chart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-card {
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 8px;
    overflow: hidden;
}

.chart-header {
    padding: 15px 20px;
    background-color: var(--light-hover);
    border-bottom: 1px solid var(--light-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--light-text);
}

.chart-container {
    padding: 20px;
    height: 300px;
}

/* Recommendations Section */
.recommendations-section {
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 8px;
    overflow: hidden;
}

.recommendations-header {
    padding: 15px 20px;
    background-color: var(--light-hover);
    border-bottom: 1px solid var(--light-border);
}

.recommendations-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--light-text);
    display: flex;
    align-items: center;
    gap: 8px;
}

.recommendations-content {
    padding: 20px;
}

.recommendation-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    background-color: var(--light-hover);
    border-radius: 8px;
    margin-bottom: 15px;
}

.recommendation-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.recommendation-icon.priority-high {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--accent-danger);
}

.recommendation-icon.priority-medium {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--accent-warning);
}

.recommendation-icon.priority-low {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--accent-info);
}

.recommendation-content {
    flex: 1;
}

.recommendation-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--light-text);
    margin-bottom: 5px;
}

.recommendation-description {
    font-size: 0.875rem;
    color: var(--light-text-secondary);
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .analysis-controls {
        grid-template-columns: 1fr;
    }
    
    .analysis-type-selector {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stability-metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-container {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .stability-analysis-container {
        padding: 15px;
    }
    
    .analysis-type-selector {
        grid-template-columns: 1fr;
    }
    
    .analysis-type-btn {
        flex-direction: row;
        justify-content: center;
        padding: 10px 15px;
    }
    
    .recommendation-item {
        flex-direction: column;
        text-align: center;
    }
}
