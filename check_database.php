<?php
// <PERSON>ript to check if there's data in the database

// Database configuration
$servername = "localhost";
$username = "root";  // Change this to your database username
$password = "";      // Change this to your database password
$dbname = "esp32_data";  // Change this to your database name

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Database Check</h1>";

// Check if database exists
$result = $conn->query("SHOW DATABASES LIKE '$dbname'");
if ($result->num_rows > 0) {
    echo "<p style='color:green'>Database '$dbname' exists.</p>";
} else {
    echo "<p style='color:red'>Database '$dbname' does not exist.</p>";
    exit;
}

// Select the database
$conn->select_db($dbname);

// Check if table exists
$result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
if ($result->num_rows > 0) {
    echo "<p style='color:green'>Table 'electrical_data' exists.</p>";
} else {
    echo "<p style='color:red'>Table 'electrical_data' does not exist.</p>";
    exit;
}

// Count records in the table
$result = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
$row = $result->fetch_assoc();
$count = $row['count'];

echo "<p>Number of records in the table: <strong>$count</strong></p>";

if ($count > 0) {
    // Get the latest record
    $result = $conn->query("SELECT * FROM electrical_data ORDER BY timestamp DESC LIMIT 1");
    $row = $result->fetch_assoc();
    
    echo "<h2>Latest Record</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    
    foreach ($row as $key => $value) {
        echo "<tr>";
        echo "<td>$key</td>";
        echo "<td>$value</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p style='color:red'>No data found in the table.</p>";
}

// Close the connection
$conn->close();
?>
