<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Time Test - Online Data Logger</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .time-display { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0; font-family: monospace; font-size: 16px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .info { background: #d1ecf1; color: #0c5460; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕐 Time Configuration Test</h1>
        <p>This page tests if all time zones are properly configured for India (IST).</p>
        
        <h3>📅 Current Times</h3>
        <div class="time-display">
            <strong>Server Time (PHP):</strong> 
            <?php 
            date_default_timezone_set('Asia/Kolkata');
            echo date('Y-m-d H:i:s T (P)'); 
            ?>
        </div>
        
        <div class="time-display">
            <strong>Browser Time (JavaScript):</strong> 
            <span id="browserTime">Loading...</span>
        </div>
        
        <div class="time-display">
            <strong>Database Time:</strong> 
            <span id="dbTime">Loading...</span>
        </div>
        
        <button class="button" onclick="sendTestDataWithTime()">Send Test Data & Check Time</button>
        <button class="button" onclick="checkAPITime()">Check API Time</button>
        <button class="button" onclick="refreshTimes()">Refresh All Times</button>
        
        <div id="results"></div>
        
        <h3>📊 Time Comparison Table</h3>
        <table id="timeTable">
            <thead>
                <tr>
                    <th>Source</th>
                    <th>Time</th>
                    <th>Timezone</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="timeTableBody">
                <tr><td colspan="4">Click "Refresh All Times" to populate</td></tr>
            </tbody>
        </table>
        
        <h3>🔧 Expected Behavior</h3>
        <ul>
            <li><strong>All times should be in IST (India Standard Time)</strong></li>
            <li><strong>Dashboard "Last Updated" should show IST time</strong></li>
            <li><strong>Database timestamps should be stored in IST</strong></li>
            <li><strong>API responses should return IST timestamps</strong></li>
        </ul>
    </div>

    <script>
        // Update browser time every second
        function updateBrowserTime() {
            const now = new Date();
            const istTime = now.toLocaleString('en-IN', {
                timeZone: 'Asia/Kolkata',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('browserTime').textContent = istTime + ' IST';
        }
        
        // Update browser time immediately and then every second
        updateBrowserTime();
        setInterval(updateBrowserTime, 1000);
        
        async function checkDatabaseTime() {
            try {
                const response = await fetch('backend/get_latest_data.php?records=1');
                const data = await response.json();
                
                if (data && data.length > 0) {
                    document.getElementById('dbTime').textContent = data[0].timestamp + ' (from latest record)';
                } else {
                    document.getElementById('dbTime').textContent = 'No data in database';
                }
            } catch (error) {
                document.getElementById('dbTime').textContent = 'Error: ' + error.message;
            }
        }
        
        // Check database time on load
        checkDatabaseTime();
        
        async function sendTestDataWithTime() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Sending test data with timestamp...</div>';
            
            const testData = {
                voltage_1: 230.5, voltage_2: 231.2, voltage_3: 229.8,
                current_1: 5.1, current_2: 5.2, current_3: 5.0,
                pf_1: 0.92, pf_2: 0.93, pf_3: 0.91,
                kva_1: 1150.5, kva_2: 1151.2, kva_3: 1149.8,
                total_kva: 3450.5, total_kw: 3277.8, total_kvar: 1076.5,
                frequency: 50.1
            };
            
            try {
                const response = await fetch('backend/receive_data.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ Test data sent successfully!</div>
                        <div class="info">
                            <strong>Server Response Time:</strong> ${result.timestamp}<br>
                            <strong>Record ID:</strong> ${result.id}
                        </div>
                    `;
                    
                    // Refresh database time
                    checkDatabaseTime();
                } else {
                    throw new Error(result.message || 'Unknown error');
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Failed to send test data: ${error.message}</div>
                `;
            }
        }
        
        async function checkAPITime() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Checking API time...</div>';
            
            try {
                const response = await fetch('backend/get_latest_data.php?records=1');
                const data = await response.json();
                
                if (data && data.length > 0) {
                    const record = data[0];
                    resultsDiv.innerHTML = `
                        <div class="success">✅ API time check successful!</div>
                        <div class="info">
                            <strong>Latest Record Timestamp:</strong> ${record.timestamp}<br>
                            <strong>Record ID:</strong> ${record.id}<br>
                            <strong>Voltage 1:</strong> ${record.voltage_1} V
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="info">No data found in database. Send test data first.</div>
                    `;
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ API time check failed: ${error.message}</div>
                `;
            }
        }
        
        async function refreshTimes() {
            const tableBody = document.getElementById('timeTableBody');
            
            // Get current times
            const browserTime = new Date().toLocaleString('en-IN', {
                timeZone: 'Asia/Kolkata',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            
            let dbTime = 'Loading...';
            let apiTime = 'Loading...';
            
            try {
                const response = await fetch('backend/get_latest_data.php?records=1');
                const data = await response.json();
                
                if (data && data.length > 0) {
                    dbTime = data[0].timestamp;
                    apiTime = 'Available';
                } else {
                    dbTime = 'No data';
                    apiTime = 'No data';
                }
            } catch (error) {
                dbTime = 'Error';
                apiTime = 'Error';
            }
            
            tableBody.innerHTML = `
                <tr>
                    <td>Browser (JavaScript)</td>
                    <td>${browserTime}</td>
                    <td>Asia/Kolkata (IST)</td>
                    <td>✅ Active</td>
                </tr>
                <tr>
                    <td>Server (PHP)</td>
                    <td><?php echo date('Y-m-d H:i:s'); ?></td>
                    <td>Asia/Kolkata (IST)</td>
                    <td>✅ Configured</td>
                </tr>
                <tr>
                    <td>Database (Latest Record)</td>
                    <td>${dbTime}</td>
                    <td>Asia/Kolkata (IST)</td>
                    <td>${dbTime !== 'No data' && dbTime !== 'Error' ? '✅ Available' : '⚠️ ' + dbTime}</td>
                </tr>
                <tr>
                    <td>API Response</td>
                    <td>${apiTime}</td>
                    <td>Asia/Kolkata (IST)</td>
                    <td>${apiTime === 'Available' ? '✅ Working' : '⚠️ ' + apiTime}</td>
                </tr>
            `;
            
            // Update database time display
            checkDatabaseTime();
        }
        
        // Auto-refresh times every 30 seconds
        setInterval(refreshTimes, 30000);
    </script>
</body>
</html>
