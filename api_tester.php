<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        pre {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 0;
            cursor: pointer;
            border-radius: 5px;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Test</h1>
        <p>This page tests the connection to the backend API and displays the results.</p>
        
        <button id="testBtn">Test API Connection</button>
        <button id="insertBtn">Insert Test Data</button>
        
        <h2>API Response:</h2>
        <pre id="response">Click "Test API Connection" to see the response...</pre>
        
        <h2>Parsed Data:</h2>
        <div id="parsedData"></div>
    </div>

    <script>
        document.getElementById('testBtn').addEventListener('click', async function() {
            const responseElement = document.getElementById('response');
            const parsedDataElement = document.getElementById('parsedData');
            
            responseElement.textContent = 'Fetching data...';
            parsedDataElement.innerHTML = '';
            
            try {
                const response = await fetch('backend/get_latest_data.php');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Display raw response
                responseElement.textContent = JSON.stringify(data, null, 2);
                
                // Display parsed data
                let html = '<table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">';
                html += '<tr><th>Field</th><th>Value</th></tr>';
                
                for (const [key, value] of Object.entries(data)) {
                    html += `<tr><td>${key}</td><td>${value}</td></tr>`;
                }
                
                html += '</table>';
                parsedDataElement.innerHTML = html;
                
            } catch (error) {
                responseElement.innerHTML = `<span class="error">Error: ${error.message}</span>`;
                console.error('Error:', error);
            }
        });
        
        document.getElementById('insertBtn').addEventListener('click', async function() {
            const responseElement = document.getElementById('response');
            
            responseElement.textContent = 'Inserting test data...';
            
            try {
                const response = await fetch('insert_test_data.php');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const text = await response.text();
                responseElement.innerHTML = `<span class="success">Test data inserted successfully!</span><br>${text}`;
                
            } catch (error) {
                responseElement.innerHTML = `<span class="error">Error: ${error.message}</span>`;
                console.error('Error:', error);
            }
        });
    </script>
</body>
</html>
