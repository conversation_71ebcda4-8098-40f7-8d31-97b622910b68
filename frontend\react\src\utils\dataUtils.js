/**
 * Utility functions for data processing and analysis
 */

/**
 * Calculate the moving average of a dataset
 * @param {Array} data - Array of data points
 * @param {number} window - Window size for moving average
 * @param {string} valueKey - Key to access the value in data objects
 * @returns {Array} - Moving average data points
 */
export const calculateMovingAverage = (data, window = 5, valueKey = 'y') => {
  if (!data || data.length === 0) return [];
  if (window <= 1) return data;

  const result = [];
  for (let i = 0; i < data.length; i++) {
    const start = Math.max(0, i - window + 1);
    const end = i + 1;
    const windowData = data.slice(start, end);
    
    const sum = windowData.reduce((acc, point) => acc + parseFloat(point[valueKey] || 0), 0);
    const avg = sum / windowData.length;
    
    result.push({
      ...data[i],
      [valueKey]: avg
    });
  }
  
  return result;
};

/**
 * Calculate linear regression for trend analysis
 * @param {Array} data - Array of data points with x and y values
 * @returns {Object} - Slope, intercept, and r-squared values
 */
export const calculateLinearRegression = (data) => {
  if (!data || data.length < 2) return { slope: 0, intercept: 0, rSquared: 0 };
  
  // Convert timestamps to numbers if needed
  const points = data.map(point => ({
    x: point.x instanceof Date ? point.x.getTime() : parseFloat(point.x),
    y: parseFloat(point.y)
  }));
  
  let sumX = 0;
  let sumY = 0;
  let sumXY = 0;
  let sumXX = 0;
  let sumYY = 0;
  const n = points.length;
  
  for (const point of points) {
    sumX += point.x;
    sumY += point.y;
    sumXY += point.x * point.y;
    sumXX += point.x * point.x;
    sumYY += point.y * point.y;
  }
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;
  
  // Calculate R-squared
  const yMean = sumY / n;
  let totalVariation = 0;
  let explainedVariation = 0;
  
  for (const point of points) {
    const yPredicted = slope * point.x + intercept;
    totalVariation += Math.pow(point.y - yMean, 2);
    explainedVariation += Math.pow(yPredicted - yMean, 2);
  }
  
  const rSquared = explainedVariation / totalVariation;
  
  return { slope, intercept, rSquared };
};

/**
 * Generate trend line data points
 * @param {Array} data - Original data points
 * @param {Object} regression - Regression parameters (slope, intercept)
 * @returns {Array} - Trend line data points
 */
export const generateTrendLine = (data, regression) => {
  if (!data || data.length < 2) return [];
  
  const { slope, intercept } = regression;
  
  // Use only first and last points for the trend line
  const firstPoint = data[0];
  const lastPoint = data[data.length - 1];
  
  const firstX = firstPoint.x instanceof Date ? firstPoint.x.getTime() : parseFloat(firstPoint.x);
  const lastX = lastPoint.x instanceof Date ? lastPoint.x.getTime() : parseFloat(lastPoint.x);
  
  const firstY = slope * firstX + intercept;
  const lastY = slope * lastX + intercept;
  
  // Return points in the same format as the original data
  return [
    { x: firstPoint.x, y: firstY },
    { x: lastPoint.x, y: lastY }
  ];
};

/**
 * Detect anomalies in data using Z-score method
 * @param {Array} data - Array of data points
 * @param {number} threshold - Z-score threshold for anomaly detection (default: 2.5)
 * @param {string} valueKey - Key to access the value in data objects
 * @returns {Array} - Indices of anomalous data points
 */
export const detectAnomalies = (data, threshold = 2.5, valueKey = 'y') => {
  if (!data || data.length < 3) return [];
  
  // Calculate mean and standard deviation
  const values = data.map(point => parseFloat(point[valueKey] || 0));
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  
  const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
  const variance = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  const stdDev = Math.sqrt(variance);
  
  // Find anomalies
  const anomalies = [];
  for (let i = 0; i < data.length; i++) {
    const value = values[i];
    const zScore = Math.abs((value - mean) / stdDev);
    
    if (zScore > threshold) {
      anomalies.push(i);
    }
  }
  
  return anomalies;
};

/**
 * Forecast future values using simple exponential smoothing
 * @param {Array} data - Historical data points
 * @param {number} periods - Number of periods to forecast
 * @param {number} alpha - Smoothing factor (0-1)
 * @param {string} valueKey - Key to access the value in data objects
 * @returns {Array} - Forecasted data points
 */
export const forecastValues = (data, periods = 10, alpha = 0.3, valueKey = 'y') => {
  if (!data || data.length === 0 || periods <= 0) return [];
  
  const values = data.map(point => parseFloat(point[valueKey] || 0));
  const lastTimestamp = data[data.length - 1].x;
  const isDate = lastTimestamp instanceof Date;
  
  // Calculate time interval for forecasting
  let timeInterval;
  if (data.length > 1) {
    const secondLastTimestamp = data[data.length - 2].x;
    if (isDate) {
      timeInterval = lastTimestamp.getTime() - secondLastTimestamp.getTime();
    } else {
      timeInterval = lastTimestamp - secondLastTimestamp;
    }
  } else {
    timeInterval = isDate ? 60000 : 1; // Default to 1 minute if only one data point
  }
  
  // Initialize forecast with last actual value
  let forecast = values[values.length - 1];
  const forecasts = [];
  
  for (let i = 1; i <= periods; i++) {
    // Generate next timestamp
    let nextTimestamp;
    if (isDate) {
      nextTimestamp = new Date(lastTimestamp.getTime() + timeInterval * i);
    } else {
      nextTimestamp = lastTimestamp + timeInterval * i;
    }
    
    // Simple exponential smoothing formula
    // We don't have new actual values, so we use the previous forecast
    forecast = forecast; // No update without new data
    
    forecasts.push({
      x: nextTimestamp,
      [valueKey]: forecast,
      isForecast: true
    });
  }
  
  return forecasts;
};
