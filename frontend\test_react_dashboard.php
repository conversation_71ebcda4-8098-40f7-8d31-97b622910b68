<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test React Dashboard</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        #root {
            min-height: 400px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>React Dashboard Test</h1>
    <div id="root">
        <div class="loading">Loading React Dashboard...</div>
    </div>

    <!-- Load React from CDN for testing -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <script>
        // Simple React test component
        const { useState, useEffect } = React;
        
        function TestDashboard() {
            const [data, setData] = useState(null);
            const [loading, setLoading] = useState(true);
            
            useEffect(() => {
                // Test API connection
                fetch('../backend/get_latest_data.php?records=1')
                    .then(response => response.json())
                    .then(data => {
                        console.log('API Response:', data);
                        setData(data);
                        setLoading(false);
                    })
                    .catch(error => {
                        console.error('API Error:', error);
                        setLoading(false);
                    });
            }, []);
            
            if (loading) {
                return React.createElement('div', { className: 'loading' }, 'Loading data...');
            }
            
            if (!data || !Array.isArray(data) || data.length === 0) {
                return React.createElement('div', { style: { color: 'red' } }, 'No data available');
            }
            
            const latestData = data[0];
            
            return React.createElement('div', null,
                React.createElement('h2', null, 'React Dashboard Working!'),
                React.createElement('div', { style: { display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px', marginTop: '20px' } },
                    React.createElement('div', { style: { background: '#e3f2fd', padding: '16px', borderRadius: '8px' } },
                        React.createElement('h3', { style: { margin: '0 0 8px 0', color: '#1976d2' } }, 'Voltage 1'),
                        React.createElement('div', { style: { fontSize: '24px', fontWeight: 'bold' } }, latestData.voltage_1 + ' V')
                    ),
                    React.createElement('div', { style: { background: '#e8f5e8', padding: '16px', borderRadius: '8px' } },
                        React.createElement('h3', { style: { margin: '0 0 8px 0', color: '#388e3c' } }, 'Current 1'),
                        React.createElement('div', { style: { fontSize: '24px', fontWeight: 'bold' } }, latestData.current_1 + ' A')
                    ),
                    React.createElement('div', { style: { background: '#fff3e0', padding: '16px', borderRadius: '8px' } },
                        React.createElement('h3', { style: { margin: '0 0 8px 0', color: '#f57c00' } }, 'Frequency'),
                        React.createElement('div', { style: { fontSize: '24px', fontWeight: 'bold' } }, latestData.frequency + ' Hz')
                    ),
                    React.createElement('div', { style: { background: '#fce4ec', padding: '16px', borderRadius: '8px' } },
                        React.createElement('h3', { style: { margin: '0 0 8px 0', color: '#c2185b' } }, 'Power Factor'),
                        React.createElement('div', { style: { fontSize: '24px', fontWeight: 'bold' } }, latestData.pf_1)
                    )
                ),
                React.createElement('div', { style: { marginTop: '20px', padding: '16px', background: '#f5f5f5', borderRadius: '8px' } },
                    React.createElement('h3', null, 'Last Updated'),
                    React.createElement('div', null, new Date(latestData.timestamp).toLocaleString())
                )
            );
        }
        
        // Render the test dashboard
        const container = document.getElementById('root');
        const root = ReactDOM.createRoot(container);
        root.render(React.createElement(TestDashboard));
    </script>
</body>
</html>
