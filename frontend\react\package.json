{"name": "power-monitor-pro-dashboard", "version": "2.0.0", "description": "Advanced React dashboard for power monitoring system with analytics and customization", "main": "src/index.js", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "test": "jest", "lint": "eslint src/**/*.js", "format": "prettier --write \"src/**/*.{js,jsx,css,json}\""}, "dependencies": {"axios": "^1.4.0", "chart.js": "^4.3.0", "chartjs-adapter-luxon": "^1.3.1", "chartjs-plugin-annotation": "^2.2.1", "chartjs-plugin-zoom": "^2.0.0", "luxon": "^3.3.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-icons": "^4.9.0", "react-transition-group": "^4.4.5", "styled-components": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.22.1", "@babel/preset-env": "^7.22.4", "@babel/preset-react": "^7.22.3", "babel-loader": "^9.1.2", "css-loader": "^6.8.1", "eslint": "^8.42.0", "eslint-plugin-react": "^7.32.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.1", "jest": "^29.5.0", "prettier": "^2.8.8", "style-loader": "^3.3.3", "webpack": "^5.85.0", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.0"}, "keywords": [], "author": "", "license": "ISC"}