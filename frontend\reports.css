/* Import base light theme */
@import url('light-theme.css');

/* Reports Page Specific Styles */
.reports-container {
    background-color: var(--light-card-bg);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--light-shadow);
}

/* Report Types Grid */
.report-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.report-type-card {
    background-color: var(--light-hover);
    border: 1px solid var(--light-border);
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.report-type-card:hover {
    background-color: var(--light-card-bg);
    border-color: var(--accent-primary);
    transform: translateY(-2px);
    box-shadow: var(--light-shadow);
}

.report-type-card.active {
    background-color: rgba(14, 165, 233, 0.1);
    border-color: var(--accent-primary);
}

.report-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.report-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(14, 165, 233, 0.1);
    color: var(--accent-primary);
}

.report-icon .material-icons-round {
    font-size: 20px;
}

.report-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--light-text);
}

.report-description {
    font-size: 0.875rem;
    color: var(--light-text-secondary);
    line-height: 1.4;
    margin-bottom: 15px;
}

.report-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.report-features li {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.75rem;
    color: var(--light-text-secondary);
    margin-bottom: 5px;
}

.report-features li .material-icons-round {
    font-size: 14px;
    color: var(--accent-success);
}

/* Report Configuration */
.report-configuration {
    background-color: var(--light-hover);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.config-section {
    margin-bottom: 25px;
}

.config-section:last-child {
    margin-bottom: 0;
}

.config-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--light-text);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

/* Parameter Selection */
.parameter-selection {
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 6px;
    padding: 15px;
}

.parameter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.parameter-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.parameter-checkbox:hover {
    background-color: var(--light-hover);
}

.parameter-checkbox input[type="checkbox"] {
    margin: 0;
    accent-color: var(--accent-primary);
}

.parameter-checkbox label {
    font-size: 0.875rem;
    color: var(--light-text);
    cursor: pointer;
    margin: 0;
}

/* Report Format Selection */
.format-selection {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.format-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.format-option:hover {
    background-color: var(--light-hover);
    border-color: var(--accent-primary);
}

.format-option.active {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.format-option .material-icons-round {
    font-size: 18px;
}

.format-option span {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Report Actions */
.report-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 30px;
}

.report-actions .button {
    min-width: 150px;
    justify-content: center;
}

/* Generated Reports */
.generated-reports {
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 8px;
    overflow: hidden;
}

.reports-header {
    padding: 15px 20px;
    background-color: var(--light-hover);
    border-bottom: 1px solid var(--light-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.reports-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--light-text);
}

.reports-list {
    max-height: 400px;
    overflow-y: auto;
}

.report-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    border-bottom: 1px solid var(--light-border);
    transition: background-color 0.2s ease;
}

.report-item:hover {
    background-color: var(--light-hover);
}

.report-item:last-child {
    border-bottom: none;
}

.report-file-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(14, 165, 233, 0.1);
    color: var(--accent-primary);
    flex-shrink: 0;
}

.report-file-icon .material-icons-round {
    font-size: 20px;
}

.report-info {
    flex: 1;
}

.report-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--light-text);
    margin-bottom: 5px;
}

.report-details {
    font-size: 0.875rem;
    color: var(--light-text-secondary);
}

.report-actions-list {
    display: flex;
    gap: 8px;
}

.report-action-btn {
    padding: 6px 8px;
    border: 1px solid var(--light-border);
    border-radius: 4px;
    background-color: var(--light-card-bg);
    color: var(--light-text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.report-action-btn:hover {
    background-color: var(--light-hover);
    color: var(--accent-primary);
    border-color: var(--accent-primary);
}

.report-action-btn.download {
    color: var(--accent-success);
    border-color: var(--accent-success);
}

.report-action-btn.download:hover {
    background-color: var(--accent-success);
    color: white;
}

.report-action-btn.delete {
    color: var(--accent-danger);
    border-color: var(--accent-danger);
}

.report-action-btn.delete:hover {
    background-color: var(--accent-danger);
    color: white;
}

/* Progress Indicator */
.progress-container {
    background-color: var(--light-hover);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: none;
}

.progress-container.active {
    display: block;
}

.progress-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--light-text);
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--light-border);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background-color: var(--accent-primary);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.875rem;
    color: var(--light-text-secondary);
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .report-types {
        grid-template-columns: 1fr;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
    
    .parameter-grid {
        grid-template-columns: 1fr;
    }
    
    .format-selection {
        justify-content: center;
    }
    
    .report-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .report-item {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 10px;
    }
    
    .report-actions-list {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .reports-container {
        padding: 15px;
    }
    
    .report-type-card {
        padding: 15px;
    }
    
    .report-configuration {
        padding: 15px;
    }
}
