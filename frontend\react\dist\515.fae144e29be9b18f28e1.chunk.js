"use strict";(self.webpackChunkpower_monitor_pro_dashboard=self.webpackChunkpower_monitor_pro_dashboard||[]).push([[515],{515:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(540);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(523);\nvar _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;\nfunction _taggedTemplateLiteral(e, t) { return t || (t = e.slice(0)), Object.freeze(Object.defineProperties(e, { raw: { value: Object.freeze(t) } })); }\n\n\nvar Modal = function Modal(_ref) {\n  var isOpen = _ref.isOpen,\n    onClose = _ref.onClose,\n    title = _ref.title,\n    children = _ref.children;\n  // Close modal on escape key\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    var handleEscape = function handleEscape(e) {\n      if (e.key === \'Escape\') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener(\'keydown\', handleEscape);\n    }\n    return function () {\n      document.removeEventListener(\'keydown\', handleEscape);\n    };\n  }, [isOpen, onClose]);\n\n  // Prevent body scroll when modal is open\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (isOpen) {\n      document.body.style.overflow = \'hidden\';\n    } else {\n      document.body.style.overflow = \'\';\n    }\n    return function () {\n      document.body.style.overflow = \'\';\n    };\n  }, [isOpen]);\n  if (!isOpen) return null;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ModalOverlay, {\n    onClick: onClose\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ModalContainer, {\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ModalHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h2", null, title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(CloseButton, {\n    onClick: onClose\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {\n    className: "material-icons-round"\n  }, "close"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ModalBody, null, children)));\n};\nvar modalIn = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* .keyframes */ .i7)(_templateObject || (_templateObject = _taggedTemplateLiteral(["\\n  from {\\n    transform: translateY(30px) scale(0.95);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0) scale(1);\\n    opacity: 1;\\n  }\\n"])));\nvar ModalOverlay = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = _taggedTemplateLiteral(["\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(15, 23, 42, 0.75);\\n  backdrop-filter: blur(4px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n"])));\nvar ModalContainer = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = _taggedTemplateLiteral(["\\n  background-color: var(--card-color);\\n  border-radius: var(--border-radius-lg);\\n  box-shadow: var(--shadow-xl);\\n  width: 100%;\\n  max-width: 640px;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  animation: ", " 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;\\n  border: 1px solid var(--border-color);\\n  \\n  @media (max-width: 576px) {\\n    width: 95%;\\n    max-height: 85vh;\\n  }\\n"])), modalIn);\nvar ModalHeader = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = _taggedTemplateLiteral(["\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 1px solid var(--border-color);\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  position: sticky;\\n  top: 0;\\n  background-color: var(--card-color);\\n  z-index: 1;\\n  \\n  h2 {\\n    font-size: 1.25rem;\\n    font-weight: var(--font-weight-semibold);\\n    color: var(--text-color);\\n    letter-spacing: -0.01em;\\n  }\\n"])));\nvar CloseButton = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.button(_templateObject5 || (_templateObject5 = _taggedTemplateLiteral(["\\n  background: none;\\n  border: none;\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  font-size: 1.5rem;\\n  padding: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: 50%;\\n  transition: all var(--transition-speed) ease;\\n  \\n  &:hover {\\n    background-color: var(--hover-color);\\n    color: var(--accent-color);\\n  }\\n"])));\nvar ModalBody = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = _taggedTemplateLiteral(["\\n  padding: 1.5rem;\\n"])));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Modal);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///515\n')}}]);