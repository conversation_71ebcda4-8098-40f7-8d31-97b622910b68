/**
 * Utility functions for layout and dashboard customization
 */

/**
 * Default dashboard layout configuration
 */
export const defaultLayout = {
  lg: [
    { i: 'summaryCards', x: 0, y: 0, w: 12, h: 2, static: true },
    { i: 'voltageWidget', x: 0, y: 2, w: 6, h: 8 },
    { i: 'currentWidget', x: 6, y: 2, w: 6, h: 8 },
    { i: 'pfWidget', x: 0, y: 10, w: 6, h: 8 },
    { i: 'kvaWidget', x: 6, y: 10, w: 6, h: 8 },
    { i: 'totalPowerWidget', x: 0, y: 18, w: 6, h: 8 },
    { i: 'frequencyWidget', x: 6, y: 18, w: 6, h: 8 }
  ],
  md: [
    { i: 'summaryCards', x: 0, y: 0, w: 10, h: 2, static: true },
    { i: 'voltageWidget', x: 0, y: 2, w: 5, h: 7 },
    { i: 'currentWidget', x: 5, y: 2, w: 5, h: 7 },
    { i: 'pfWidget', x: 0, y: 9, w: 5, h: 7 },
    { i: 'kvaWidget', x: 5, y: 9, w: 5, h: 7 },
    { i: 'totalPowerWidget', x: 0, y: 16, w: 5, h: 7 },
    { i: 'frequencyWidget', x: 5, y: 16, w: 5, h: 7 }
  ],
  sm: [
    { i: 'summaryCards', x: 0, y: 0, w: 6, h: 4, static: true },
    { i: 'voltageWidget', x: 0, y: 4, w: 6, h: 6 },
    { i: 'currentWidget', x: 0, y: 10, w: 6, h: 6 },
    { i: 'pfWidget', x: 0, y: 16, w: 6, h: 6 },
    { i: 'kvaWidget', x: 0, y: 22, w: 6, h: 6 },
    { i: 'totalPowerWidget', x: 0, y: 28, w: 6, h: 6 },
    { i: 'frequencyWidget', x: 0, y: 34, w: 6, h: 6 }
  ],
  xs: [
    { i: 'summaryCards', x: 0, y: 0, w: 4, h: 6, static: true },
    { i: 'voltageWidget', x: 0, y: 6, w: 4, h: 6 },
    { i: 'currentWidget', x: 0, y: 12, w: 4, h: 6 },
    { i: 'pfWidget', x: 0, y: 18, w: 4, h: 6 },
    { i: 'kvaWidget', x: 0, y: 24, w: 4, h: 6 },
    { i: 'totalPowerWidget', x: 0, y: 30, w: 4, h: 6 },
    { i: 'frequencyWidget', x: 0, y: 36, w: 4, h: 6 }
  ]
};

/**
 * Available widgets configuration
 */
export const availableWidgets = [
  {
    id: 'voltageWidget',
    title: 'Line Voltages',
    icon: 'bolt',
    description: 'Displays voltage readings for all three phases',
    category: 'power',
    dataKeys: ['voltage_1', 'voltage_2', 'voltage_3'],
    labels: ['Phase 1', 'Phase 2', 'Phase 3'],
    yAxisLabel: 'Voltage (V)',
    parameterType: 'voltage'
  },
  {
    id: 'currentWidget',
    title: 'Phase Currents',
    icon: 'electric_bolt',
    description: 'Displays current readings for all three phases',
    category: 'power',
    dataKeys: ['current_1', 'current_2', 'current_3'],
    labels: ['Phase 1', 'Phase 2', 'Phase 3'],
    yAxisLabel: 'Current (A)',
    parameterType: 'current'
  },
  {
    id: 'pfWidget',
    title: 'Power Factors',
    icon: 'show_chart',
    description: 'Displays power factor readings for all three phases',
    category: 'power',
    dataKeys: ['pf_1', 'pf_2', 'pf_3'],
    labels: ['Phase 1', 'Phase 2', 'Phase 3'],
    yAxisLabel: 'Power Factor',
    parameterType: 'pf'
  },
  {
    id: 'kvaWidget',
    title: 'KVA Values',
    icon: 'electric_meter',
    description: 'Displays KVA readings for all three phases',
    category: 'power',
    dataKeys: ['kva_1', 'kva_2', 'kva_3'],
    labels: ['Phase 1', 'Phase 2', 'Phase 3'],
    yAxisLabel: 'KVA',
    parameterType: 'kva'
  },
  {
    id: 'totalPowerWidget',
    title: 'Total Power',
    icon: 'power',
    description: 'Displays total power readings',
    category: 'power',
    dataKeys: ['total_kw', 'total_kva', 'total_kvar'],
    labels: ['Total KW', 'Total KVA', 'Total KVAR'],
    yAxisLabel: 'Power',
    parameterType: 'totalPower'
  },
  {
    id: 'frequencyWidget',
    title: 'Frequency',
    icon: 'speed',
    description: 'Displays frequency readings',
    category: 'power',
    dataKeys: ['frequency'],
    labels: ['Frequency'],
    yAxisLabel: 'Frequency (Hz)',
    parameterType: 'frequency'
  },
  {
    id: 'energyConsumptionWidget',
    title: 'Energy Consumption',
    icon: 'bolt',
    description: 'Displays energy consumption over time',
    category: 'energy',
    dataKeys: ['energy_consumption'],
    labels: ['Energy'],
    yAxisLabel: 'Energy (kWh)',
    parameterType: 'energy'
  },
  {
    id: 'powerQualityWidget',
    title: 'Power Quality',
    icon: 'analytics',
    description: 'Displays power quality metrics',
    category: 'quality',
    dataKeys: ['thd_voltage', 'thd_current'],
    labels: ['THD Voltage', 'THD Current'],
    yAxisLabel: 'THD (%)',
    parameterType: 'quality'
  }
];

/**
 * Save dashboard layout to localStorage
 * @param {string} dashboardId - Dashboard identifier
 * @param {Object} layout - Layout configuration
 */
export const saveLayout = (dashboardId, layout) => {
  try {
    localStorage.setItem(`dashboard_layout_${dashboardId}`, JSON.stringify(layout));
    return true;
  } catch (error) {
    console.error('Error saving layout:', error);
    return false;
  }
};

/**
 * Load dashboard layout from localStorage
 * @param {string} dashboardId - Dashboard identifier
 * @returns {Object|null} - Layout configuration or null if not found
 */
export const loadLayout = (dashboardId) => {
  try {
    const savedLayout = localStorage.getItem(`dashboard_layout_${dashboardId}`);
    return savedLayout ? JSON.parse(savedLayout) : null;
  } catch (error) {
    console.error('Error loading layout:', error);
    return null;
  }
};

/**
 * Reset dashboard layout to default
 * @param {string} dashboardId - Dashboard identifier
 */
export const resetLayout = (dashboardId) => {
  try {
    localStorage.removeItem(`dashboard_layout_${dashboardId}`);
    return true;
  } catch (error) {
    console.error('Error resetting layout:', error);
    return false;
  }
};

/**
 * Save dashboard configuration to localStorage
 * @param {string} dashboardId - Dashboard identifier
 * @param {Object} config - Dashboard configuration
 */
export const saveDashboardConfig = (dashboardId, config) => {
  try {
    localStorage.setItem(`dashboard_config_${dashboardId}`, JSON.stringify(config));
    return true;
  } catch (error) {
    console.error('Error saving dashboard config:', error);
    return false;
  }
};

/**
 * Load dashboard configuration from localStorage
 * @param {string} dashboardId - Dashboard identifier
 * @returns {Object|null} - Dashboard configuration or null if not found
 */
export const loadDashboardConfig = (dashboardId) => {
  try {
    const savedConfig = localStorage.getItem(`dashboard_config_${dashboardId}`);
    return savedConfig ? JSON.parse(savedConfig) : null;
  } catch (error) {
    console.error('Error loading dashboard config:', error);
    return null;
  }
};

/**
 * Get widget configuration by ID
 * @param {string} widgetId - Widget identifier
 * @returns {Object|null} - Widget configuration or null if not found
 */
export const getWidgetConfig = (widgetId) => {
  return availableWidgets.find(widget => widget.id === widgetId) || null;
};
