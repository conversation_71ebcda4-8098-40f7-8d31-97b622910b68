import {
  defaultLayout,
  availableWidgets,
  saveLayout,
  loadLayout,
  resetLayout,
  saveDashboardConfig,
  loadDashboardConfig,
  getWidgetConfig
} from '../layoutUtils';

describe('layoutUtils', () => {
  // Mock localStorage
  const mockLocalStorage = (() => {
    let store = {};
    return {
      getItem: jest.fn(key => store[key] || null),
      setItem: jest.fn((key, value) => {
        store[key] = value.toString();
      }),
      removeItem: jest.fn(key => {
        delete store[key];
      }),
      clear: jest.fn(() => {
        store = {};
      })
    };
  })();
  
  Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage
  });
  
  beforeEach(() => {
    mockLocalStorage.clear();
    jest.clearAllMocks();
  });
  
  describe('defaultLayout', () => {
    it('should have layouts for different screen sizes', () => {
      expect(defaultLayout).toHaveProperty('lg');
      expect(defaultLayout).toHaveProperty('md');
      expect(defaultLayout).toHaveProperty('sm');
      expect(defaultLayout).toHaveProperty('xs');
    });
    
    it('should have the same widgets in all layouts', () => {
      const lgWidgets = defaultLayout.lg.map(item => item.i);
      const mdWidgets = defaultLayout.md.map(item => item.i);
      const smWidgets = defaultLayout.sm.map(item => item.i);
      const xsWidgets = defaultLayout.xs.map(item => item.i);
      
      // All layouts should have the same widgets
      expect(lgWidgets.sort()).toEqual(mdWidgets.sort());
      expect(lgWidgets.sort()).toEqual(smWidgets.sort());
      expect(lgWidgets.sort()).toEqual(xsWidgets.sort());
    });
  });
  
  describe('availableWidgets', () => {
    it('should have required properties for each widget', () => {
      availableWidgets.forEach(widget => {
        expect(widget).toHaveProperty('id');
        expect(widget).toHaveProperty('title');
        expect(widget).toHaveProperty('icon');
        expect(widget).toHaveProperty('description');
        expect(widget).toHaveProperty('category');
        expect(widget).toHaveProperty('dataKeys');
        expect(widget).toHaveProperty('labels');
        expect(widget).toHaveProperty('yAxisLabel');
        expect(widget).toHaveProperty('parameterType');
      });
    });
  });
  
  describe('saveLayout and loadLayout', () => {
    it('should save and load layout', () => {
      const dashboardId = 'test';
      const layout = { lg: [{ i: 'test', x: 0, y: 0, w: 1, h: 1 }] };
      
      // Save layout
      saveLayout(dashboardId, layout);
      
      // localStorage.setItem should have been called
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        `dashboard_layout_${dashboardId}`,
        JSON.stringify(layout)
      );
      
      // Load layout
      const loadedLayout = loadLayout(dashboardId);
      
      // localStorage.getItem should have been called
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith(
        `dashboard_layout_${dashboardId}`
      );
      
      // Loaded layout should match saved layout
      expect(loadedLayout).toEqual(layout);
    });
    
    it('should handle errors', () => {
      // Mock localStorage.getItem to throw an error
      mockLocalStorage.getItem.mockImplementationOnce(() => {
        throw new Error('Test error');
      });
      
      // Load layout should return null on error
      const loadedLayout = loadLayout('test');
      expect(loadedLayout).toBeNull();
    });
  });
  
  describe('resetLayout', () => {
    it('should remove layout from localStorage', () => {
      const dashboardId = 'test';
      
      // Reset layout
      resetLayout(dashboardId);
      
      // localStorage.removeItem should have been called
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
        `dashboard_layout_${dashboardId}`
      );
    });
  });
  
  describe('saveDashboardConfig and loadDashboardConfig', () => {
    it('should save and load dashboard config', () => {
      const dashboardId = 'test';
      const config = { name: 'Test Dashboard', refreshRate: 5 };
      
      // Save config
      saveDashboardConfig(dashboardId, config);
      
      // localStorage.setItem should have been called
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        `dashboard_config_${dashboardId}`,
        JSON.stringify(config)
      );
      
      // Load config
      const loadedConfig = loadDashboardConfig(dashboardId);
      
      // localStorage.getItem should have been called
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith(
        `dashboard_config_${dashboardId}`
      );
      
      // Loaded config should match saved config
      expect(loadedConfig).toEqual(config);
    });
  });
  
  describe('getWidgetConfig', () => {
    it('should return widget config by ID', () => {
      // Get config for an existing widget
      const widgetConfig = getWidgetConfig('voltageWidget');
      
      // Should return the correct widget
      expect(widgetConfig).toHaveProperty('id', 'voltageWidget');
      expect(widgetConfig).toHaveProperty('title', 'Line Voltages');
    });
    
    it('should return null for non-existent widget', () => {
      // Get config for a non-existent widget
      const widgetConfig = getWidgetConfig('nonExistentWidget');
      
      // Should return null
      expect(widgetConfig).toBeNull();
    });
  });
});
