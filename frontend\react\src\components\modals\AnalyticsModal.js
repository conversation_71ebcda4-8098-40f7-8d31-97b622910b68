import React from 'react';
import styled from 'styled-components';
import Modal from './Modal';
import { useAnalytics } from '../../contexts/AnalyticsContext';

const AnalyticsModal = ({ isOpen, onClose, addNotification }) => {
  const {
    analyticsConfig,
    toggleMovingAverage,
    updateMovingAverageWindow,
    toggleTrendLine,
    toggleAnomalyDetection,
    updateAnomalyThreshold,
    toggleForecast,
    updateForecastPeriods
  } = useAnalytics();
  
  // Handle moving average toggle
  const handleMovingAverageToggle = () => {
    toggleMovingAverage();
    addNotification(`Moving average ${analyticsConfig.showMovingAverage ? 'disabled' : 'enabled'}`, 'info');
  };
  
  // Handle moving average window change
  const handleMovingAverageWindowChange = (e) => {
    const value = parseInt(e.target.value);
    updateMovingAverageWindow(value);
  };
  
  // Handle trend line toggle
  const handleTrendLineToggle = () => {
    toggleTrendLine();
    addNotification(`Trend line ${analyticsConfig.showTrendLine ? 'disabled' : 'enabled'}`, 'info');
  };
  
  // Handle anomaly detection toggle
  const handleAnomalyDetectionToggle = () => {
    toggleAnomalyDetection();
    addNotification(`Anomaly detection ${analyticsConfig.showAnomalies ? 'disabled' : 'enabled'}`, 'info');
  };
  
  // Handle anomaly threshold change
  const handleAnomalyThresholdChange = (e) => {
    const value = parseFloat(e.target.value);
    updateAnomalyThreshold(value);
  };
  
  // Handle forecast toggle
  const handleForecastToggle = () => {
    toggleForecast();
    addNotification(`Forecast ${analyticsConfig.showForecast ? 'disabled' : 'enabled'}`, 'info');
  };
  
  // Handle forecast periods change
  const handleForecastPeriodsChange = (e) => {
    const value = parseInt(e.target.value);
    updateForecastPeriods(value);
  };
  
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      title="Analytics Settings"
    >
      <AnalyticsOptions>
        <AnalyticsSection>
          <h3>Data Smoothing</h3>
          <AnalyticsOption>
            <OptionHeader>
              <OptionTitle>
                <span className="material-icons-round">show_chart</span>
                <div>
                  <h4>Moving Average</h4>
                  <p>Smooth out noise in the data to see trends more clearly</p>
                </div>
              </OptionTitle>
              <OptionToggle 
                type="checkbox"
                checked={analyticsConfig.showMovingAverage}
                onChange={handleMovingAverageToggle}
              />
            </OptionHeader>
            
            {analyticsConfig.showMovingAverage && (
              <OptionSettings>
                <SettingItem>
                  <label htmlFor="movingAverageWindow">Window Size</label>
                  <div className="setting-control">
                    <input 
                      type="range" 
                      id="movingAverageWindow" 
                      min="2" 
                      max="20" 
                      value={analyticsConfig.movingAverageWindow}
                      onChange={handleMovingAverageWindowChange}
                    />
                    <span className="setting-value">{analyticsConfig.movingAverageWindow} points</span>
                  </div>
                </SettingItem>
              </OptionSettings>
            )}
          </AnalyticsOption>
        </AnalyticsSection>
        
        <AnalyticsSection>
          <h3>Trend Analysis</h3>
          <AnalyticsOption>
            <OptionHeader>
              <OptionTitle>
                <span className="material-icons-round">trending_up</span>
                <div>
                  <h4>Trend Line</h4>
                  <p>Show linear trend to identify overall direction</p>
                </div>
              </OptionTitle>
              <OptionToggle 
                type="checkbox"
                checked={analyticsConfig.showTrendLine}
                onChange={handleTrendLineToggle}
              />
            </OptionHeader>
          </AnalyticsOption>
        </AnalyticsSection>
        
        <AnalyticsSection>
          <h3>Anomaly Detection</h3>
          <AnalyticsOption>
            <OptionHeader>
              <OptionTitle>
                <span className="material-icons-round">error_outline</span>
                <div>
                  <h4>Detect Anomalies</h4>
                  <p>Highlight unusual data points that deviate from normal patterns</p>
                </div>
              </OptionTitle>
              <OptionToggle 
                type="checkbox"
                checked={analyticsConfig.showAnomalies}
                onChange={handleAnomalyDetectionToggle}
              />
            </OptionHeader>
            
            {analyticsConfig.showAnomalies && (
              <OptionSettings>
                <SettingItem>
                  <label htmlFor="anomalyThreshold">Sensitivity (Z-score threshold)</label>
                  <div className="setting-control">
                    <input 
                      type="range" 
                      id="anomalyThreshold" 
                      min="1" 
                      max="5" 
                      step="0.1" 
                      value={analyticsConfig.anomalyThreshold}
                      onChange={handleAnomalyThresholdChange}
                    />
                    <span className="setting-value">{analyticsConfig.anomalyThreshold}</span>
                  </div>
                  <div className="setting-hint">
                    Lower values detect more anomalies, higher values are more selective
                  </div>
                </SettingItem>
              </OptionSettings>
            )}
          </AnalyticsOption>
        </AnalyticsSection>
        
        <AnalyticsSection>
          <h3>Forecasting</h3>
          <AnalyticsOption>
            <OptionHeader>
              <OptionTitle>
                <span className="material-icons-round">timeline</span>
                <div>
                  <h4>Forecast Future Values</h4>
                  <p>Predict future values based on historical data</p>
                </div>
              </OptionTitle>
              <OptionToggle 
                type="checkbox"
                checked={analyticsConfig.showForecast}
                onChange={handleForecastToggle}
              />
            </OptionHeader>
            
            {analyticsConfig.showForecast && (
              <OptionSettings>
                <SettingItem>
                  <label htmlFor="forecastPeriods">Forecast Length</label>
                  <div className="setting-control">
                    <input 
                      type="range" 
                      id="forecastPeriods" 
                      min="5" 
                      max="30" 
                      value={analyticsConfig.forecastPeriods}
                      onChange={handleForecastPeriodsChange}
                    />
                    <span className="setting-value">{analyticsConfig.forecastPeriods} points</span>
                  </div>
                </SettingItem>
              </OptionSettings>
            )}
          </AnalyticsOption>
        </AnalyticsSection>
      </AnalyticsOptions>
      
      <ModalFooter>
        <ModalButton 
          onClick={onClose}
        >
          Close
        </ModalButton>
      </ModalFooter>
    </Modal>
  );
};

const AnalyticsOptions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const AnalyticsSection = styled.div`
  h3 {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    letter-spacing: -0.01em;
  }
`;

const AnalyticsOption = styled.div`
  background-color: var(--card-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all var(--animation-medium) ease;
  margin-bottom: 1rem;
  
  &:hover {
    box-shadow: var(--shadow-md);
  }
`;

const OptionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  cursor: pointer;
`;

const OptionTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  
  span.material-icons-round {
    font-size: 1.5rem;
    color: var(--primary-color);
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(37, 99, 235, 0.1);
    border-radius: 50%;
  }
  
  h4 {
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: 0.25rem;
  }
  
  p {
    font-size: 0.8125rem;
    color: var(--text-secondary);
    line-height: 1.4;
  }
`;

const OptionToggle = styled.input`
  width: 3rem;
  height: 1.5rem;
  appearance: none;
  background-color: var(--border-color);
  border-radius: 9999px;
  position: relative;
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:checked {
    background-color: var(--primary-color);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 1.25rem;
    height: 1.25rem;
    background-color: white;
    border-radius: 50%;
    transition: all var(--animation-medium) ease;
  }
  
  &:checked::before {
    left: 1.625rem;
  }
`;

const OptionSettings = styled.div`
  padding: 0 1.25rem 1.25rem;
  border-top: 1px solid var(--border-color);
  animation: slideDown var(--animation-medium) ease forwards;
  
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const SettingItem = styled.div`
  margin-top: 1rem;
  
  label {
    display: block;
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: 0.5rem;
  }
  
  .setting-control {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  input[type="range"] {
    flex: 1;
    height: 0.5rem;
    -webkit-appearance: none;
    background: var(--hover-color);
    border-radius: 0.25rem;
    outline: none;
    
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 1.25rem;
      height: 1.25rem;
      border-radius: 50%;
      background: var(--primary-color);
      cursor: pointer;
      box-shadow: var(--shadow-sm);
      border: 2px solid white;
    }
  }
  
  .setting-value {
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    color: var(--primary-color);
    min-width: 4rem;
    text-align: right;
  }
  
  .setting-hint {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
  }
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
`;

const ModalButton = styled.button`
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
`;

export default AnalyticsModal;
