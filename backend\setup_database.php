<?php
// Database connection parameters for initial connection (without database)
$host = 'localhost';
$username = 'root';  // Default XAMPP username
$password = '';      // Default XAMPP password (empty)

// Create connection without specifying database
$conn = new mysqli($host, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Database name
$database = 'esp32_data';

// Create database if it doesn't exist
$sql = "CREATE DATABASE IF NOT EXISTS $database";
if ($conn->query($sql) === TRUE) {
    echo "Database created or already exists<br>";
} else {
    echo "Error creating database: " . $conn->error . "<br>";
    die();
}

// Select the database
$conn->select_db($database);

// Create electrical_data table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS electrical_data (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    voltage_1 FLOAT,
    voltage_2 FLOAT,
    voltage_3 FLOAT,
    current_1 FLOAT,
    current_2 FLOAT,
    current_3 FLOAT,
    pf_1 FLOAT,
    pf_2 FLOAT,
    pf_3 FLOAT,
    kva_1 FLOAT,
    kva_2 FLOAT,
    kva_3 FLOAT,
    total_kw FLOAT,
    total_kva FLOAT,
    total_kvar FLOAT,
    frequency FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Table 'electrical_data' created or already exists<br>";
} else {
    echo "Error creating table: " . $conn->error . "<br>";
}

// Insert sample data if the table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    echo "Inserting sample data...<br>";
    
    // Generate sample data for the last 24 hours
    $startTime = strtotime('-24 hours');
    $endTime = time();
    $interval = 60; // 1 minute interval
    
    for ($timestamp = $startTime; $timestamp <= $endTime; $timestamp += $interval) {
        // Generate random values with some realistic patterns
        $baseVoltage = 220 + (sin($timestamp / 3600) * 10);
        $baseCurrent = 5 + (cos($timestamp / 7200) * 2);
        $basePf = 0.85 + (sin($timestamp / 5400) * 0.1);
        $baseFrequency = 50 + (sin($timestamp / 1800) * 0.5);
        
        // Add some random variation
        $voltage1 = $baseVoltage + (rand(-5, 5) / 10);
        $voltage2 = $baseVoltage + (rand(-5, 5) / 10);
        $voltage3 = $baseVoltage + (rand(-5, 5) / 10);
        
        $current1 = $baseCurrent + (rand(-10, 10) / 10);
        $current2 = $baseCurrent + (rand(-10, 10) / 10);
        $current3 = $baseCurrent + (rand(-10, 10) / 10);
        
        $pf1 = min(1, max(0, $basePf + (rand(-5, 5) / 100)));
        $pf2 = min(1, max(0, $basePf + (rand(-5, 5) / 100)));
        $pf3 = min(1, max(0, $basePf + (rand(-5, 5) / 100)));
        
        $kva1 = $voltage1 * $current1 / 1000;
        $kva2 = $voltage2 * $current2 / 1000;
        $kva3 = $voltage3 * $current3 / 1000;
        
        $totalKw = ($kva1 * $pf1 + $kva2 * $pf2 + $kva3 * $pf3);
        $totalKva = $kva1 + $kva2 + $kva3;
        $totalKvar = sqrt(pow($totalKva, 2) - pow($totalKw, 2));
        
        $frequency = $baseFrequency + (rand(-10, 10) / 100);
        
        $dateTime = date('Y-m-d H:i:s', $timestamp);
        
        $sql = "INSERT INTO electrical_data (
            timestamp, 
            voltage_1, voltage_2, voltage_3, 
            current_1, current_2, current_3, 
            pf_1, pf_2, pf_3, 
            kva_1, kva_2, kva_3, 
            total_kw, total_kva, total_kvar, 
            frequency
        ) VALUES (
            '$dateTime', 
            $voltage1, $voltage2, $voltage3, 
            $current1, $current2, $current3, 
            $pf1, $pf2, $pf3, 
            $kva1, $kva2, $kva3, 
            $totalKw, $totalKva, $totalKvar, 
            $frequency
        )";
        
        if (!$conn->query($sql)) {
            echo "Error inserting sample data: " . $conn->error . "<br>";
            break;
        }
    }
    
    echo "Sample data inserted successfully!<br>";
} else {
    echo "Data already exists in the table. Skipping sample data insertion.<br>";
}

echo "<br>Database setup completed!";

// Close connection
$conn->close();
?>
