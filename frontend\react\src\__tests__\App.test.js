import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import App from '../App';
import { ThemeProvider } from '../contexts/ThemeContext';

// Mock the components
jest.mock('../components/layout/Sidebar', () => {
  return jest.fn(props => (
    <div data-testid="sidebar">
      <button 
        data-testid="settings-button" 
        onClick={props.onSettingsClick}
      >
        Settings
      </button>
      <button 
        data-testid="customize-button" 
        onClick={props.onCustomizeClick}
      >
        Customize
      </button>
      <button 
        data-testid="analytics-button" 
        onClick={props.onAnalyticsClick}
      >
        Analytics
      </button>
      <button 
        data-testid="theme-builder-button" 
        onClick={props.onThemeBuilderClick}
      >
        Theme Builder
      </button>
    </div>
  ));
});

jest.mock('../components/layout/MainContent', () => {
  return jest.fn(props => (
    <div data-testid="main-content">
      <button 
        data-testid="export-button" 
        onClick={props.onExportClick}
      >
        Export
      </button>
      <button 
        data-testid="fullscreen-button" 
        onClick={() => props.toggleFullscreenWidget(true, 'Test', 'icon', 'chart1')}
      >
        Fullscreen
      </button>
    </div>
  ));
});

// Mock the modals
const mockModals = [
  'SettingsModal',
  'ExportModal',
  'CustomizeModal',
  'AnalyticsModal',
  'ThemeBuilderModal',
  'FullscreenWidget'
];

mockModals.forEach(modalName => {
  jest.mock(`../components/modals/${modalName}`, () => {
    return jest.fn(props => (
      props.isOpen ? (
        <div data-testid={`${modalName.toLowerCase()}`}>
          <button 
            data-testid={`close-${modalName.toLowerCase()}`} 
            onClick={props.onClose}
          >
            Close
          </button>
        </div>
      ) : null
    ));
  });
});

// Mock the contexts
jest.mock('../contexts/ThemeContext', () => ({
  useTheme: () => ({
    darkMode: false
  }),
  ThemeProvider: ({ children }) => <div>{children}</div>
}));

describe('App', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders without crashing', () => {
    render(<App />);
    
    // Main components should be rendered
    expect(screen.getByTestId('sidebar')).toBeInTheDocument();
    expect(screen.getByTestId('main-content')).toBeInTheDocument();
  });
  
  it('opens and closes modals', () => {
    render(<App />);
    
    // Initially, no modals should be open
    mockModals.forEach(modalName => {
      expect(screen.queryByTestId(modalName.toLowerCase())).not.toBeInTheDocument();
    });
    
    // Open settings modal
    fireEvent.click(screen.getByTestId('settings-button'));
    expect(screen.getByTestId('settingsmodal')).toBeInTheDocument();
    
    // Close settings modal
    fireEvent.click(screen.getByTestId('close-settingsmodal'));
    expect(screen.queryByTestId('settingsmodal')).not.toBeInTheDocument();
    
    // Open customize modal
    fireEvent.click(screen.getByTestId('customize-button'));
    expect(screen.getByTestId('customizemodal')).toBeInTheDocument();
    
    // Close customize modal
    fireEvent.click(screen.getByTestId('close-customizemodal'));
    expect(screen.queryByTestId('customizemodal')).not.toBeInTheDocument();
    
    // Open analytics modal
    fireEvent.click(screen.getByTestId('analytics-button'));
    expect(screen.getByTestId('analyticsmodal')).toBeInTheDocument();
    
    // Close analytics modal
    fireEvent.click(screen.getByTestId('close-analyticsmodal'));
    expect(screen.queryByTestId('analyticsmodal')).not.toBeInTheDocument();
    
    // Open theme builder modal
    fireEvent.click(screen.getByTestId('theme-builder-button'));
    expect(screen.getByTestId('themebuildermodal')).toBeInTheDocument();
    
    // Close theme builder modal
    fireEvent.click(screen.getByTestId('close-themebuildermodal'));
    expect(screen.queryByTestId('themebuildermodal')).not.toBeInTheDocument();
    
    // Open export modal
    fireEvent.click(screen.getByTestId('export-button'));
    expect(screen.getByTestId('exportmodal')).toBeInTheDocument();
    
    // Close export modal
    fireEvent.click(screen.getByTestId('close-exportmodal'));
    expect(screen.queryByTestId('exportmodal')).not.toBeInTheDocument();
    
    // Open fullscreen widget
    fireEvent.click(screen.getByTestId('fullscreen-button'));
    expect(screen.getByTestId('fullscreenwidget')).toBeInTheDocument();
    
    // Close fullscreen widget
    fireEvent.click(screen.getByTestId('close-fullscreenwidget'));
    expect(screen.queryByTestId('fullscreenwidget')).not.toBeInTheDocument();
  });
  
  it('handles notifications', () => {
    render(<App />);
    
    // Add a notification
    act(() => {
      // Find the notification container
      const notificationContainer = document.querySelector('[class*="NotificationContainer"]');
      
      // Simulate adding a notification
      const event = new CustomEvent('notification', {
        detail: { message: 'Test notification', type: 'info' }
      });
      notificationContainer.dispatchEvent(event);
    });
    
    // Notification should be displayed
    expect(screen.getByText('Test notification')).toBeInTheDocument();
  });
});
