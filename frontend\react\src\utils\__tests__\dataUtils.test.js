import {
  calculateMovingAverage,
  calculateLinearRegression,
  generateTrendLine,
  detectAnomalies,
  forecastValues
} from '../dataUtils';

describe('dataUtils', () => {
  describe('calculateMovingAverage', () => {
    it('should return empty array for empty input', () => {
      expect(calculateMovingAverage([])).toEqual([]);
    });

    it('should return the same array if window size is 1', () => {
      const data = [
        { x: 1, y: 10 },
        { x: 2, y: 20 },
        { x: 3, y: 30 }
      ];
      expect(calculateMovingAverage(data, 1)).toEqual(data);
    });

    it('should calculate moving average correctly', () => {
      const data = [
        { x: 1, y: 10 },
        { x: 2, y: 20 },
        { x: 3, y: 30 },
        { x: 4, y: 40 },
        { x: 5, y: 50 }
      ];
      
      const result = calculateMovingAverage(data, 3);
      
      expect(result).toHaveLength(5);
      expect(result[0].y).toBeCloseTo(10);
      expect(result[1].y).toBeCloseTo(15);
      expect(result[2].y).toBeCloseTo(20);
      expect(result[3].y).toBeCloseTo(30);
      expect(result[4].y).toBeCloseTo(40);
    });
  });

  describe('calculateLinearRegression', () => {
    it('should return zero values for insufficient data', () => {
      expect(calculateLinearRegression([])).toEqual({
        slope: 0,
        intercept: 0,
        rSquared: 0
      });
      
      expect(calculateLinearRegression([{ x: 1, y: 10 }])).toEqual({
        slope: 0,
        intercept: 0,
        rSquared: 0
      });
    });

    it('should calculate linear regression correctly', () => {
      const data = [
        { x: 1, y: 1 },
        { x: 2, y: 2 },
        { x: 3, y: 3 },
        { x: 4, y: 4 },
        { x: 5, y: 5 }
      ];
      
      const result = calculateLinearRegression(data);
      
      expect(result.slope).toBeCloseTo(1);
      expect(result.intercept).toBeCloseTo(0);
      expect(result.rSquared).toBeCloseTo(1);
    });
  });

  describe('detectAnomalies', () => {
    it('should return empty array for insufficient data', () => {
      expect(detectAnomalies([])).toEqual([]);
      expect(detectAnomalies([{ y: 10 }, { y: 20 }])).toEqual([]);
    });

    it('should detect anomalies correctly', () => {
      const data = [
        { y: 10 },
        { y: 12 },
        { y: 11 },
        { y: 13 },
        { y: 50 }, // Anomaly
        { y: 12 },
        { y: 11 }
      ];
      
      const result = detectAnomalies(data, 2);
      
      expect(result).toContain(4); // Index of the anomaly
    });
  });

  // Add more tests for other functions
});
