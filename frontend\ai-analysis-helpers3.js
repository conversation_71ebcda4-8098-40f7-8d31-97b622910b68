/**
 * Final set of helper functions for AI analysis of electrical system data
 */

/**
 * Identify peak periods in power consumption
 * @param {Array} timestamps - Array of timestamp objects
 * @param {Array} powerData - Array of power values
 * @returns {Array} Identified peak periods
 */
function identifyPeakPeriods(timestamps, powerData) {
    if (timestamps.length === 0 || powerData.length === 0) {
        return [];
    }
    
    // Calculate power statistics
    const powerStats = calculateStatistics(powerData);
    
    // Define peak threshold (e.g., top 20% of power values)
    const peakThreshold = powerStats.mean + (0.5 * powerStats.stdDev);
    
    // Find continuous periods above threshold
    const peakPeriods = [];
    let currentPeriod = null;
    
    for (let i = 0; i < powerData.length; i++) {
        if (powerData[i] > peakThreshold) {
            // Start a new period or extend current one
            if (!currentPeriod) {
                currentPeriod = {
                    start: timestamps[i],
                    end: timestamps[i],
                    maxPower: powerData[i],
                    dataPoints: 1
                };
            } else {
                currentPeriod.end = timestamps[i];
                currentPeriod.maxPower = Math.max(currentPeriod.maxPower, powerData[i]);
                currentPeriod.dataPoints++;
            }
        } else if (currentPeriod) {
            // End current period if it's significant (more than a few data points)
            if (currentPeriod.dataPoints > 3) {
                peakPeriods.push({
                    start: currentPeriod.start,
                    end: currentPeriod.end,
                    duration: (currentPeriod.end - currentPeriod.start) / (1000 * 60), // minutes
                    maxPower: currentPeriod.maxPower.toFixed(2),
                    averagePower: calculateAverageDuringPeriod(
                        timestamps, 
                        powerData, 
                        currentPeriod.start, 
                        currentPeriod.end
                    ).toFixed(2)
                });
            }
            currentPeriod = null;
        }
    }
    
    // Add the last period if it exists
    if (currentPeriod && currentPeriod.dataPoints > 3) {
        peakPeriods.push({
            start: currentPeriod.start,
            end: currentPeriod.end,
            duration: (currentPeriod.end - currentPeriod.start) / (1000 * 60), // minutes
            maxPower: currentPeriod.maxPower.toFixed(2),
            averagePower: calculateAverageDuringPeriod(
                timestamps, 
                powerData, 
                currentPeriod.start, 
                currentPeriod.end
            ).toFixed(2)
        });
    }
    
    // Format timestamps for readability
    return peakPeriods.map(period => ({
        ...period,
        startTime: formatTime(period.start),
        endTime: formatTime(period.end),
        threshold: peakThreshold.toFixed(2)
    }));
}

/**
 * Calculate average power during a specific period
 * @param {Array} timestamps - Array of timestamp objects
 * @param {Array} powerData - Array of power values
 * @param {Date} startTime - Start time of the period
 * @param {Date} endTime - End time of the period
 * @returns {number} Average power during the period
 */
function calculateAverageDuringPeriod(timestamps, powerData, startTime, endTime) {
    let sum = 0;
    let count = 0;
    
    for (let i = 0; i < timestamps.length; i++) {
        if (timestamps[i] >= startTime && timestamps[i] <= endTime) {
            sum += powerData[i];
            count++;
        }
    }
    
    return count > 0 ? sum / count : 0;
}

/**
 * Format time for display
 * @param {Date} date - Date object
 * @returns {string} Formatted time string
 */
function formatTime(date) {
    return date.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

/**
 * Calculate load factor from power data
 * @param {Array} powerData - Array of power values
 * @returns {number} Load factor (0-1)
 */
function calculateLoadFactor(powerData) {
    if (powerData.length === 0) {
        return 0;
    }
    
    const avgPower = powerData.reduce((sum, val) => sum + val, 0) / powerData.length;
    const peakPower = Math.max(...powerData);
    
    return peakPower > 0 ? avgPower / peakPower : 0;
}

/**
 * Assess power quality based on power measurements
 * @param {Array} realPower - Array of real power values
 * @param {Array} apparentPower - Array of apparent power values
 * @param {Array} reactivePower - Array of reactive power values
 * @returns {Object} Power quality assessment
 */
function assessPowerQuality(realPower, apparentPower, reactivePower) {
    // Calculate average power factor
    const avgPF = calculateAveragePowerFactor(realPower, apparentPower);
    
    // Calculate average distortion power factor (simplified)
    const avgDPF = estimateDistortionPowerFactor(realPower, apparentPower, reactivePower);
    
    // Calculate power quality score (0-1)
    const pfScore = avgPF >= 0.95 ? 1 : avgPF >= 0.9 ? 0.9 : avgPF >= 0.85 ? 0.8 : avgPF >= 0.8 ? 0.7 : 0.6;
    const dpfScore = avgDPF >= 0.98 ? 1 : avgDPF >= 0.95 ? 0.9 : avgDPF >= 0.9 ? 0.8 : 0.7;
    
    const score = (pfScore * 0.7) + (dpfScore * 0.3);
    
    return {
        score: score.toFixed(2),
        powerFactor: avgPF.toFixed(3),
        distortionPowerFactor: avgDPF.toFixed(3),
        rating: score >= 0.9 ? 'Excellent' : score >= 0.8 ? 'Good' : score >= 0.7 ? 'Fair' : 'Poor',
        issues: score < 0.8 ? [
            'Power factor correction may be needed',
            'Harmonic filtering should be considered'
        ] : []
    };
}

/**
 * Calculate average power factor from real and apparent power
 * @param {Array} realPower - Array of real power values
 * @param {Array} apparentPower - Array of apparent power values
 * @returns {number} Average power factor
 */
function calculateAveragePowerFactor(realPower, apparentPower) {
    let sumPF = 0;
    let count = 0;
    
    for (let i = 0; i < realPower.length; i++) {
        if (apparentPower[i] > 0) {
            sumPF += realPower[i] / apparentPower[i];
            count++;
        }
    }
    
    return count > 0 ? sumPF / count : 0;
}

/**
 * Estimate distortion power factor
 * @param {Array} realPower - Array of real power values
 * @param {Array} apparentPower - Array of apparent power values
 * @param {Array} reactivePower - Array of reactive power values
 * @returns {number} Estimated distortion power factor
 */
function estimateDistortionPowerFactor(realPower, apparentPower, reactivePower) {
    // This is a simplified estimation
    // True DPF requires harmonic analysis
    
    let sumDPF = 0;
    let count = 0;
    
    for (let i = 0; i < realPower.length; i++) {
        if (apparentPower[i] > 0) {
            // Calculate displacement power factor
            const displacementPF = Math.sqrt(Math.pow(realPower[i], 2) + Math.pow(reactivePower[i], 2)) / apparentPower[i];
            
            // Estimate distortion power factor (typically close to 1 unless harmonics are present)
            const estimatedDPF = displacementPF > 1 ? 0.98 : 0.98 * displacementPF;
            
            sumDPF += estimatedDPF;
            count++;
        }
    }
    
    return count > 0 ? sumDPF / count : 0;
}

/**
 * Estimate energy efficiency of the system
 * @param {Array} data - Array of data points
 * @returns {Object} Energy efficiency assessment
 */
function estimateEnergyEfficiency(data) {
    // Calculate average power factor
    const pfValues = [];
    for (let i = 0; i < data.length; i++) {
        const avgPF = (parseFloat(data[i].pf_1) + parseFloat(data[i].pf_2) + parseFloat(data[i].pf_3)) / 3;
        pfValues.push(avgPF);
    }
    
    const avgPF = pfValues.reduce((sum, val) => sum + val, 0) / pfValues.length;
    
    // Calculate losses based on power factor
    // Lower PF means higher losses
    const lossFactor = 1 - avgPF;
    
    // Calculate efficiency score (0-1)
    const pfEfficiency = avgPF >= 0.95 ? 1 : avgPF >= 0.9 ? 0.9 : avgPF >= 0.85 ? 0.8 : avgPF >= 0.8 ? 0.7 : 0.6;
    
    // Estimate overall efficiency
    const score = pfEfficiency;
    
    return {
        score: score.toFixed(2),
        powerFactor: avgPF.toFixed(3),
        estimatedLosses: (lossFactor * 100).toFixed(2) + '%',
        rating: score >= 0.9 ? 'Excellent' : score >= 0.8 ? 'Good' : score >= 0.7 ? 'Fair' : 'Poor',
        improvementPotential: score < 0.9 ? 'High' : 'Low'
    };
}

/**
 * Generate overall insights and recommendations
 * @param {Object} voltageAnalysis - Voltage analysis results
 * @param {Object} currentAnalysis - Current analysis results
 * @param {Object} powerFactorAnalysis - Power factor analysis results
 * @param {Object} frequencyAnalysis - Frequency analysis results
 * @param {Object} loadPatternAnalysis - Load pattern analysis results
 * @param {Object} efficiencyAnalysis - Efficiency analysis results
 * @returns {Object} Overall insights and recommendations
 */
function generateInsights(
    voltageAnalysis, 
    currentAnalysis, 
    powerFactorAnalysis, 
    frequencyAnalysis,
    loadPatternAnalysis,
    efficiencyAnalysis
) {
    // Collect all issues
    const issues = [];
    
    // Voltage issues
    if (voltageAnalysis.issues.highVoltage.detected) {
        issues.push({
            parameter: 'Voltage',
            issue: 'High voltage detected',
            severity: voltageAnalysis.issues.highVoltage.severity,
            details: `Maximum voltage reached ${Math.max(
                parseFloat(voltageAnalysis.issues.highVoltage.phases.phase1.maxValue),
                parseFloat(voltageAnalysis.issues.highVoltage.phases.phase2.maxValue),
                parseFloat(voltageAnalysis.issues.highVoltage.phases.phase3.maxValue)
            ).toFixed(1)} V`
        });
    }
    
    if (voltageAnalysis.issues.lowVoltage.detected) {
        issues.push({
            parameter: 'Voltage',
            issue: 'Low voltage detected',
            severity: voltageAnalysis.issues.lowVoltage.severity,
            details: `Minimum voltage reached ${Math.min(
                parseFloat(voltageAnalysis.issues.lowVoltage.phases.phase1.minValue),
                parseFloat(voltageAnalysis.issues.lowVoltage.phases.phase2.minValue),
                parseFloat(voltageAnalysis.issues.lowVoltage.phases.phase3.minValue)
            ).toFixed(1)} V`
        });
    }
    
    if (voltageAnalysis.issues.imbalance.detected) {
        issues.push({
            parameter: 'Voltage',
            issue: 'Phase imbalance detected',
            severity: voltageAnalysis.issues.imbalance.severity,
            details: `Average imbalance of ${voltageAnalysis.issues.imbalance.statistics.mean}%`
        });
    }
    
    // Current issues
    if (currentAnalysis.issues.imbalance.detected) {
        issues.push({
            parameter: 'Current',
            issue: 'Phase imbalance detected',
            severity: currentAnalysis.issues.imbalance.severity,
            details: `Average imbalance of ${currentAnalysis.issues.imbalance.statistics.mean}%`
        });
    }
    
    if (currentAnalysis.issues.overload.detected) {
        issues.push({
            parameter: 'Current',
            issue: 'Potential overload detected',
            severity: currentAnalysis.issues.overload.severity,
            details: 'One or more phases approaching capacity limits'
        });
    }
    
    // Power factor issues
    if (powerFactorAnalysis.issues.lowPowerFactor.detected) {
        issues.push({
            parameter: 'Power Factor',
            issue: 'Low power factor detected',
            severity: powerFactorAnalysis.issues.lowPowerFactor.severity,
            details: `Average power factor is ${powerFactorAnalysis.averagePF}`
        });
    }
    
    // Frequency issues
    if (frequencyAnalysis.issues.deviation.detected) {
        issues.push({
            parameter: 'Frequency',
            issue: 'Frequency deviation detected',
            severity: frequencyAnalysis.issues.deviation.severity,
            details: `Maximum deviation of ${frequencyAnalysis.issues.deviation.statistics.maxDeviation} Hz from nominal`
        });
    }
    
    // Collect all recommendations
    const recommendations = [
        ...voltageAnalysis.recommendations,
        ...currentAnalysis.recommendations,
        ...powerFactorAnalysis.recommendations,
        ...frequencyAnalysis.recommendations,
        ...loadPatternAnalysis.recommendations,
        ...efficiencyAnalysis.recommendations
    ];
    
    // Remove duplicates
    const uniqueRecommendations = [...new Set(recommendations)];
    
    // Prioritize recommendations
    const prioritizedRecommendations = prioritizeRecommendations(uniqueRecommendations, issues);
    
    return {
        summary: generateSummary(issues, efficiencyAnalysis, powerFactorAnalysis),
        issues: issues,
        recommendations: prioritizedRecommendations
    };
}

/**
 * Generate a summary of the analysis
 * @param {Array} issues - Array of identified issues
 * @param {Object} efficiencyAnalysis - Efficiency analysis results
 * @param {Object} powerFactorAnalysis - Power factor analysis results
 * @returns {string} Summary text
 */
function generateSummary(issues, efficiencyAnalysis, powerFactorAnalysis) {
    const highSeverityCount = issues.filter(issue => issue.severity === 'high').length;
    const mediumSeverityCount = issues.filter(issue => issue.severity === 'medium').length;
    
    let summary = '';
    
    if (highSeverityCount === 0 && mediumSeverityCount === 0) {
        summary = 'The electrical system is operating within normal parameters. ';
    } else if (highSeverityCount > 0) {
        summary = `The electrical system has ${highSeverityCount} critical issue(s) that require immediate attention. `;
    } else {
        summary = `The electrical system has ${mediumSeverityCount} issue(s) that should be addressed. `;
    }
    
    summary += `System efficiency is rated as ${efficiencyAnalysis.rating}. `;
    
    if (powerFactorAnalysis.potentialSavings.required) {
        summary += `Power factor correction could yield approximately ${powerFactorAnalysis.potentialSavings.estimatedSavingsPercent}% reduction in apparent power demand. `;
    }
    
    return summary;
}

/**
 * Prioritize recommendations based on issues
 * @param {Array} recommendations - Array of recommendations
 * @param {Array} issues - Array of identified issues
 * @returns {Array} Prioritized recommendations
 */
function prioritizeRecommendations(recommendations, issues) {
    // Create a score for each recommendation based on related issues
    const recommendationScores = recommendations.map(recommendation => {
        let score = 1; // Base score
        
        // Increase score based on related high-severity issues
        issues.forEach(issue => {
            if (issue.severity === 'high' && recommendation.toLowerCase().includes(issue.parameter.toLowerCase())) {
                score += 3;
            } else if (issue.severity === 'medium' && recommendation.toLowerCase().includes(issue.parameter.toLowerCase())) {
                score += 1;
            }
        });
        
        return { recommendation, score };
    });
    
    // Sort by score (descending)
    recommendationScores.sort((a, b) => b.score - a.score);
    
    // Return just the recommendations
    return recommendationScores.map(item => item.recommendation);
}

// Export helper functions to global scope
window.identifyPeakPeriods = identifyPeakPeriods;
window.calculateLoadFactor = calculateLoadFactor;
window.assessPowerQuality = assessPowerQuality;
window.estimateEnergyEfficiency = estimateEnergyEfficiency;
window.generateInsights = generateInsights;
