<?php
// Data Simulator for 3-Phase Data Logger System
// This script simulates an ESP32 sending data to the backend

// Configuration
$simulationDuration = 60; // seconds
$interval = 3; // seconds between data points
$serverUrl = "http://localhost/online%20data%20logger/backend/receive_data.php";

echo "Starting 3-Phase Data Simulator\n";
echo "==============================\n";
echo "Duration: $simulationDuration seconds\n";
echo "Interval: $interval seconds\n";
echo "Server URL: $serverUrl\n\n";

// Base values
$baseVoltage = 400;
$baseCurrent = 50;
$basePF = 0.9;
$baseKVA = 20;
$baseFrequency = 50;

// Simulation loop
$startTime = time();
$count = 0;

while (time() - $startTime < $simulationDuration) {
    // Generate random variations
    $angle = $count * 0.1; // For creating sine wave variations
    
    // Create simulated data with some randomness and sine wave patterns
    $data = [
        "voltage_1" => $baseVoltage + sin($angle) * 10 + (rand(-5, 5) / 10),
        "voltage_2" => $baseVoltage + sin($angle + 2.09) * 10 + (rand(-5, 5) / 10), // 120° phase shift
        "voltage_3" => $baseVoltage + sin($angle + 4.19) * 10 + (rand(-5, 5) / 10), // 240° phase shift
        
        "current_1" => $baseCurrent + sin($angle) * 5 + (rand(-10, 10) / 10),
        "current_2" => $baseCurrent + sin($angle + 2.09) * 5 + (rand(-10, 10) / 10),
        "current_3" => $baseCurrent + sin($angle + 4.19) * 5 + (rand(-10, 10) / 10),
        
        "pf_1" => $basePF + sin($angle) * 0.05 + (rand(-5, 5) / 100),
        "pf_2" => $basePF + sin($angle + 2.09) * 0.05 + (rand(-5, 5) / 100),
        "pf_3" => $basePF + sin($angle + 4.19) * 0.05 + (rand(-5, 5) / 100),
        
        "kva_1" => $baseKVA + sin($angle) * 2 + (rand(-5, 5) / 10),
        "kva_2" => $baseKVA + sin($angle + 2.09) * 2 + (rand(-5, 5) / 10),
        "kva_3" => $baseKVA + sin($angle + 4.19) * 2 + (rand(-5, 5) / 10),
    ];
    
    // Calculate total values
    $totalKVA = $data["kva_1"] + $data["kva_2"] + $data["kva_3"];
    $totalKW = $totalKVA * (($data["pf_1"] + $data["pf_2"] + $data["pf_3"]) / 3);
    $totalKVAR = sqrt(pow($totalKVA, 2) - pow($totalKW, 2));
    
    // Add total values to data
    $data["total_kva"] = $totalKVA;
    $data["total_kw"] = $totalKW;
    $data["total_kvar"] = $totalKVAR;
    
    // Add frequency with small variations
    $data["frequency"] = $baseFrequency + sin($angle) * 0.1 + (rand(-5, 5) / 100);
    
    // Send data to server
    $options = [
        'http' => [
            'header'  => "Content-type: application/json\r\n",
            'method'  => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    
    echo "Sending data point " . ($count + 1) . "...\n";
    
    try {
        $result = file_get_contents($serverUrl, false, $context);
        echo "Response: $result\n\n";
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n\n";
    }
    
    // Wait for the next interval
    sleep($interval);
    $count++;
}

echo "\nSimulation completed. Generated $count data points.\n";
?>
