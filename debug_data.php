<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Data Flow - Online Data Logger</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        #liveTest {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Data Flow Debug Tool</h1>
        <p>This tool helps diagnose why data isn't showing on the frontend.</p>

        <!-- Database Connection Test -->
        <div class="section">
            <h3>1. Database Connection Test</h3>
            <?php
            $servername = "localhost";
            $username = "root";
            $password = "";
            $dbname = "esp32_data";

            try {
                $conn = new mysqli($servername, $username, $password, $dbname);
                if ($conn->connect_error) {
                    throw new Exception("Connection failed: " . $conn->connect_error);
                }
                echo '<div class="status success">✅ Database connection successful</div>';
                
                // Check if table exists
                $result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
                if ($result->num_rows > 0) {
                    echo '<div class="status success">✅ Table "electrical_data" exists</div>';
                    
                    // Check record count
                    $count_result = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
                    $count = $count_result->fetch_assoc()['count'];
                    echo '<div class="status info">📊 Total records in database: ' . $count . '</div>';
                    
                    if ($count > 0) {
                        // Show latest record
                        $latest_result = $conn->query("SELECT * FROM electrical_data ORDER BY timestamp DESC LIMIT 1");
                        $latest = $latest_result->fetch_assoc();
                        echo '<div class="status info">🕒 Latest record timestamp: ' . $latest['timestamp'] . '</div>';
                        echo '<h4>Latest Record Data:</h4>';
                        echo '<pre>' . json_encode($latest, JSON_PRETTY_PRINT) . '</pre>';
                    } else {
                        echo '<div class="status warning">⚠️ No data in database. ESP32 might not be sending data.</div>';
                    }
                } else {
                    echo '<div class="status error">❌ Table "electrical_data" does not exist</div>';
                }
                
            } catch (Exception $e) {
                echo '<div class="status error">❌ Database error: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <!-- API Endpoint Tests -->
        <div class="section">
            <h3>2. API Endpoint Tests</h3>
            <button class="test-button" onclick="testAPI('backend/get_latest_data.php')">Test get_latest_data.php</button>
            <button class="test-button" onclick="testAPI('backend/get_latest_data.php?records=20')">Test with records=20</button>
            <button class="test-button" onclick="testAPI('backend/test_connection.php')">Test ESP32 connection endpoint</button>
            <button class="test-button" onclick="testAPI('backend/receive_data.php')">Test receive_data.php (GET)</button>
            
            <div id="apiResults"></div>
        </div>

        <!-- ESP32 Connection Test -->
        <div class="section">
            <h3>3. ESP32 Connection Status</h3>
            <?php
            // Check if ESP32 has sent data recently (within last 5 minutes)
            if (isset($conn) && $conn && !$conn->connect_error) {
                $recent_result = $conn->query("SELECT COUNT(*) as count FROM electrical_data WHERE timestamp > DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
                if ($recent_result) {
                    $recent_count = $recent_result->fetch_assoc()['count'];
                    if ($recent_count > 0) {
                        echo '<div class="status success">✅ ESP32 is actively sending data (received ' . $recent_count . ' records in last 5 minutes)</div>';
                    } else {
                        echo '<div class="status warning">⚠️ No recent data from ESP32 (no records in last 5 minutes)</div>';
                        echo '<div class="status info">💡 Check ESP32 Serial Monitor for connection status</div>';
                    }
                }
            }
            ?>
        </div>

        <!-- Frontend JavaScript Test -->
        <div class="section">
            <h3>4. Frontend JavaScript Test</h3>
            <button class="test-button" onclick="testFrontendFetch()">Test Frontend Data Fetch</button>
            <button class="test-button" onclick="startLiveTest()">Start Live Data Test</button>
            <button class="test-button" onclick="stopLiveTest()">Stop Live Test</button>
            
            <div id="liveTest">
                <h4>Live Data Test Results:</h4>
                <div id="liveResults">Click "Start Live Data Test" to begin...</div>
            </div>
        </div>

        <!-- Troubleshooting Steps -->
        <div class="section">
            <h3>5. Troubleshooting Steps</h3>
            <ol>
                <li><strong>If database connection fails:</strong> Check XAMPP MySQL service is running</li>
                <li><strong>If table doesn't exist:</strong> Run <a href="setup.php" target="_blank">setup.php</a></li>
                <li><strong>If no data in database:</strong> Check ESP32 Serial Monitor for errors</li>
                <li><strong>If ESP32 not sending data:</strong> Verify WiFi connection and server IP (**************)</li>
                <li><strong>If API endpoints fail:</strong> Check file permissions and PHP errors</li>
                <li><strong>If frontend not updating:</strong> Check browser console for JavaScript errors</li>
            </ol>
        </div>
    </div>

    <script>
        let liveTestInterval = null;

        async function testAPI(endpoint) {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="status info">Testing ' + endpoint + '...</div>';
            
            try {
                const response = await fetch(endpoint);
                const data = await response.text();
                
                let statusClass = response.ok ? 'success' : 'error';
                let statusIcon = response.ok ? '✅' : '❌';
                
                resultsDiv.innerHTML = `
                    <div class="status ${statusClass}">${statusIcon} ${endpoint} - Status: ${response.status}</div>
                    <h4>Response:</h4>
                    <pre>${data}</pre>
                `;
                
                // Try to parse as JSON for better display
                try {
                    const jsonData = JSON.parse(data);
                    resultsDiv.innerHTML += `
                        <h4>Parsed JSON:</h4>
                        <pre>${JSON.stringify(jsonData, null, 2)}</pre>
                    `;
                } catch (e) {
                    // Not JSON, that's okay
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="status error">❌ Error testing ${endpoint}: ${error.message}</div>
                `;
            }
        }

        async function testFrontendFetch() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="status info">Testing frontend data fetch (same as dashboard)...</div>';
            
            try {
                const timestamp = new Date().getTime();
                const response = await fetch(`backend/get_latest_data.php?_=${timestamp}&records=20`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                const latestData = Array.isArray(data) ? data[0] : data;
                
                resultsDiv.innerHTML = `
                    <div class="status success">✅ Frontend fetch successful</div>
                    <h4>Data received (as dashboard would see it):</h4>
                    <pre>${JSON.stringify(latestData, null, 2)}</pre>
                    <div class="status info">📊 Records received: ${Array.isArray(data) ? data.length : 1}</div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="status error">❌ Frontend fetch failed: ${error.message}</div>
                    <div class="status warning">This is the same error the dashboard would see!</div>
                `;
            }
        }

        function startLiveTest() {
            if (liveTestInterval) {
                clearInterval(liveTestInterval);
            }
            
            const resultsDiv = document.getElementById('liveResults');
            resultsDiv.innerHTML = 'Starting live test...<br>';
            
            liveTestInterval = setInterval(async () => {
                try {
                    const timestamp = new Date().getTime();
                    const response = await fetch(`backend/get_latest_data.php?_=${timestamp}`);
                    const data = await response.json();
                    const latestData = Array.isArray(data) ? data[0] : data;
                    
                    const now = new Date().toLocaleTimeString();
                    resultsDiv.innerHTML += `
                        <div style="margin: 5px 0; padding: 5px; background: #e8f5e8; border-radius: 3px;">
                            [${now}] ✅ Data received - Voltage: ${latestData.voltage_1}V, Current: ${latestData.current_1}A, Freq: ${latestData.frequency}Hz
                        </div>
                    `;
                    
                    // Keep only last 10 entries
                    const entries = resultsDiv.querySelectorAll('div');
                    if (entries.length > 10) {
                        entries[0].remove();
                    }
                    
                } catch (error) {
                    const now = new Date().toLocaleTimeString();
                    resultsDiv.innerHTML += `
                        <div style="margin: 5px 0; padding: 5px; background: #ffe8e8; border-radius: 3px;">
                            [${now}] ❌ Error: ${error.message}
                        </div>
                    `;
                }
            }, 2000); // Test every 2 seconds
        }

        function stopLiveTest() {
            if (liveTestInterval) {
                clearInterval(liveTestInterval);
                liveTestInterval = null;
                document.getElementById('liveResults').innerHTML += '<div style="color: #666; margin-top: 10px;">Live test stopped.</div>';
            }
        }
    </script>
</body>
</html>
