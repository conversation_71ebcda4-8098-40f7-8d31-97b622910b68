import React, { createContext, useState, useContext } from 'react';
import { 
  calculateMovingAverage, 
  calculateLinearRegression, 
  generateTrendLine,
  detectAnomalies,
  forecastValues
} from '../utils/dataUtils';

const AnalyticsContext = createContext();

export const useAnalytics = () => useContext(AnalyticsContext);

export const AnalyticsProvider = ({ children }) => {
  const [analyticsConfig, setAnalyticsConfig] = useState({
    showMovingAverage: false,
    movingAverageWindow: 5,
    showTrendLine: false,
    showAnomalies: false,
    anomalyThreshold: 2.5,
    showForecast: false,
    forecastPeriods: 10
  });
  
  // Toggle moving average
  const toggleMovingAverage = () => {
    setAnalyticsConfig(prev => ({
      ...prev,
      showMovingAverage: !prev.showMovingAverage
    }));
  };
  
  // Update moving average window
  const updateMovingAverageWindow = (window) => {
    setAnalyticsConfig(prev => ({
      ...prev,
      movingAverageWindow: window
    }));
  };
  
  // Toggle trend line
  const toggleTrendLine = () => {
    setAnalyticsConfig(prev => ({
      ...prev,
      showTrendLine: !prev.showTrendLine
    }));
  };
  
  // Toggle anomaly detection
  const toggleAnomalyDetection = () => {
    setAnalyticsConfig(prev => ({
      ...prev,
      showAnomalies: !prev.showAnomalies
    }));
  };
  
  // Update anomaly threshold
  const updateAnomalyThreshold = (threshold) => {
    setAnalyticsConfig(prev => ({
      ...prev,
      anomalyThreshold: threshold
    }));
  };
  
  // Toggle forecast
  const toggleForecast = () => {
    setAnalyticsConfig(prev => ({
      ...prev,
      showForecast: !prev.showForecast
    }));
  };
  
  // Update forecast periods
  const updateForecastPeriods = (periods) => {
    setAnalyticsConfig(prev => ({
      ...prev,
      forecastPeriods: periods
    }));
  };
  
  // Process data with analytics
  const processData = (data, dataKey = 'y') => {
    if (!data || data.length === 0) return { processedData: [], analytics: {} };
    
    let processedData = [...data];
    const analytics = {};
    
    // Calculate moving average if enabled
    if (analyticsConfig.showMovingAverage) {
      const movingAverageData = calculateMovingAverage(
        data, 
        analyticsConfig.movingAverageWindow,
        dataKey
      );
      
      analytics.movingAverage = movingAverageData.map(point => ({
        x: point.x,
        y: point[dataKey],
        isMovingAverage: true
      }));
    }
    
    // Calculate trend line if enabled
    if (analyticsConfig.showTrendLine) {
      const regression = calculateLinearRegression(
        data.map(point => ({ x: point.x instanceof Date ? point.x.getTime() : point.x, y: point[dataKey] }))
      );
      
      analytics.regression = regression;
      
      const trendLineData = generateTrendLine(
        data.map(point => ({ x: point.x, y: point[dataKey] })),
        regression
      );
      
      analytics.trendLine = trendLineData.map(point => ({
        x: point.x,
        y: point.y,
        isTrendLine: true
      }));
    }
    
    // Detect anomalies if enabled
    if (analyticsConfig.showAnomalies) {
      const anomalyIndices = detectAnomalies(
        data.map(point => ({ y: point[dataKey] })),
        analyticsConfig.anomalyThreshold,
        'y'
      );
      
      analytics.anomalies = anomalyIndices.map(index => ({
        x: data[index].x,
        y: data[index][dataKey],
        isAnomaly: true
      }));
    }
    
    // Generate forecast if enabled
    if (analyticsConfig.showForecast) {
      const forecastData = forecastValues(
        data.map(point => ({ x: point.x, y: point[dataKey] })),
        analyticsConfig.forecastPeriods,
        0.3,
        'y'
      );
      
      analytics.forecast = forecastData;
    }
    
    return { processedData, analytics };
  };
  
  return (
    <AnalyticsContext.Provider value={{
      analyticsConfig,
      toggleMovingAverage,
      updateMovingAverageWindow,
      toggleTrendLine,
      toggleAnomalyDetection,
      updateAnomalyThreshold,
      toggleForecast,
      updateForecastPeriods,
      processData
    }}>
      {children}
    </AnalyticsContext.Provider>
  );
};
