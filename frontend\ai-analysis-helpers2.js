/**
 * Additional helper functions for AI analysis of electrical system data
 */

/**
 * Detect current overload conditions
 * @param {Object} currentData - Object with phase current arrays
 * @param {Object} stats - Statistics for each phase
 * @returns {Object} Current overload analysis
 */
function detectCurrentOverload(currentData, stats) {
    // Assume a rated capacity (this would ideally come from system configuration)
    const ratedCapacity = {
        phase1: Math.max(...currentData.phase1) * 1.2, // Estimate based on 120% of max observed
        phase2: Math.max(...currentData.phase2) * 1.2,
        phase3: Math.max(...currentData.phase3) * 1.2
    };
    
    const threshold = analysisConfig.current.overloadThreshold;
    
    // Calculate load percentage for each phase
    const phase1LoadPercent = (stats.phase1.p95 / ratedCapacity.phase1) * 100;
    const phase2LoadPercent = (stats.phase2.p95 / ratedCapacity.phase2) * 100;
    const phase3LoadPercent = (stats.phase3.p95 / ratedCapacity.phase3) * 100;
    
    // Determine if any phase is overloaded
    const phase1Overloaded = phase1LoadPercent > threshold;
    const phase2Overloaded = phase2LoadPercent > threshold;
    const phase3Overloaded = phase3LoadPercent > threshold;
    
    const detected = phase1Overloaded || phase2Overloaded || phase3Overloaded;
    
    return {
        detected,
        threshold,
        phases: {
            phase1: {
                overloaded: phase1Overloaded,
                loadPercentage: phase1LoadPercent.toFixed(2),
                p95Current: stats.phase1.p95.toFixed(2),
                estimatedCapacity: ratedCapacity.phase1.toFixed(2)
            },
            phase2: {
                overloaded: phase2Overloaded,
                loadPercentage: phase2LoadPercent.toFixed(2),
                p95Current: stats.phase2.p95.toFixed(2),
                estimatedCapacity: ratedCapacity.phase2.toFixed(2)
            },
            phase3: {
                overloaded: phase3Overloaded,
                loadPercentage: phase3LoadPercent.toFixed(2),
                p95Current: stats.phase3.p95.toFixed(2),
                estimatedCapacity: ratedCapacity.phase3.toFixed(2)
            }
        },
        severity: detected ? (
            Math.max(phase1LoadPercent, phase2LoadPercent, phase3LoadPercent) > 100 ? 'high' : 'medium'
        ) : 'low'
    };
}

/**
 * Estimate harmonic issues based on current patterns
 * @param {Object} currentData - Object with phase current arrays
 * @returns {Object} Harmonic issues analysis
 */
function estimateHarmonicIssues(currentData) {
    // This is a simplified estimation since actual harmonic measurement requires spectral analysis
    // We'll look for patterns in the data that might indicate harmonics
    
    // Calculate rate of change and look for oscillatory patterns
    const calculateOscillations = (values) => {
        const changes = [];
        let directionChanges = 0;
        let lastDirection = null;
        
        for (let i = 1; i < values.length; i++) {
            const change = values[i] - values[i-1];
            changes.push(change);
            
            const direction = change > 0 ? 'up' : change < 0 ? 'down' : lastDirection;
            
            if (lastDirection && direction !== lastDirection) {
                directionChanges++;
            }
            
            lastDirection = direction;
        }
        
        // Calculate oscillation index (higher means more oscillatory behavior)
        const oscillationIndex = directionChanges / (values.length - 1);
        return oscillationIndex;
    };
    
    const phase1Oscillation = calculateOscillations(currentData.phase1);
    const phase2Oscillation = calculateOscillations(currentData.phase2);
    const phase3Oscillation = calculateOscillations(currentData.phase3);
    
    // Higher oscillation index might indicate harmonics
    const harmonicThreshold = 0.4; // This is a heuristic value
    
    const phase1Harmonics = phase1Oscillation > harmonicThreshold;
    const phase2Harmonics = phase2Oscillation > harmonicThreshold;
    const phase3Harmonics = phase3Oscillation > harmonicThreshold;
    
    const detected = phase1Harmonics || phase2Harmonics || phase3Harmonics;
    
    return {
        detected,
        threshold: harmonicThreshold,
        phases: {
            phase1: {
                oscillationIndex: phase1Oscillation.toFixed(2),
                potentialHarmonics: phase1Harmonics
            },
            phase2: {
                oscillationIndex: phase2Oscillation.toFixed(2),
                potentialHarmonics: phase2Harmonics
            },
            phase3: {
                oscillationIndex: phase3Oscillation.toFixed(2),
                potentialHarmonics: phase3Harmonics
            }
        },
        severity: detected ? 'medium' : 'low',
        confidence: 'medium', // This analysis has medium confidence without actual harmonic measurements
        note: 'This is an estimation based on current patterns. Actual harmonic analysis requires specialized equipment.'
    };
}

/**
 * Detect low power factor conditions
 * @param {Object} pfData - Object with phase power factor arrays
 * @param {Object} stats - Statistics for each phase
 * @returns {Object} Low power factor analysis
 */
function detectLowPowerFactor(pfData, stats) {
    const threshold = analysisConfig.powerFactor.correctionNeededThreshold;
    
    // Check if any phase has a low power factor
    const phase1Low = stats.phase1.mean < threshold;
    const phase2Low = stats.phase2.mean < threshold;
    const phase3Low = stats.phase3.mean < threshold;
    
    // Calculate percentage of time below threshold for each phase
    const phase1Percentage = pfData.phase1.filter(pf => pf < threshold).length / pfData.phase1.length * 100;
    const phase2Percentage = pfData.phase2.filter(pf => pf < threshold).length / pfData.phase2.length * 100;
    const phase3Percentage = pfData.phase3.filter(pf => pf < threshold).length / pfData.phase3.length * 100;
    
    // Determine if low power factor is a significant issue
    const detected = phase1Percentage > 20 || phase2Percentage > 20 || phase3Percentage > 20;
    
    return {
        detected,
        threshold,
        phases: {
            phase1: {
                low: phase1Low,
                percentage: phase1Percentage.toFixed(2),
                meanValue: stats.phase1.mean.toFixed(3)
            },
            phase2: {
                low: phase2Low,
                percentage: phase2Percentage.toFixed(2),
                meanValue: stats.phase2.mean.toFixed(3)
            },
            phase3: {
                low: phase3Low,
                percentage: phase3Percentage.toFixed(2),
                meanValue: stats.phase3.mean.toFixed(3)
            }
        },
        severity: detected ? (
            Math.min(stats.phase1.mean, stats.phase2.mean, stats.phase3.mean) < 0.7 ? 'high' : 'medium'
        ) : 'low'
    };
}

/**
 * Detect leading power factor conditions
 * @param {Object} pfData - Object with phase power factor arrays
 * @param {Object} stats - Statistics for each phase
 * @returns {Object} Leading power factor analysis
 */
function detectLeadingPowerFactor(pfData, stats) {
    const threshold = analysisConfig.powerFactor.leadingThreshold;
    
    // Check if any phase has a leading power factor (above 1.0)
    const phase1Leading = stats.phase1.max >= threshold;
    const phase2Leading = stats.phase2.max >= threshold;
    const phase3Leading = stats.phase3.max >= threshold;
    
    // Calculate percentage of time above threshold for each phase
    const phase1Percentage = pfData.phase1.filter(pf => pf >= threshold).length / pfData.phase1.length * 100;
    const phase2Percentage = pfData.phase2.filter(pf => pf >= threshold).length / pfData.phase2.length * 100;
    const phase3Percentage = pfData.phase3.filter(pf => pf >= threshold).length / pfData.phase3.length * 100;
    
    // Determine if leading power factor is a significant issue
    const detected = phase1Percentage > 10 || phase2Percentage > 10 || phase3Percentage > 10;
    
    return {
        detected,
        threshold,
        phases: {
            phase1: {
                leading: phase1Leading,
                percentage: phase1Percentage.toFixed(2),
                maxValue: stats.phase1.max.toFixed(3)
            },
            phase2: {
                leading: phase2Leading,
                percentage: phase2Percentage.toFixed(2),
                maxValue: stats.phase2.max.toFixed(3)
            },
            phase3: {
                leading: phase3Leading,
                percentage: phase3Percentage.toFixed(2),
                maxValue: stats.phase3.max.toFixed(3)
            }
        },
        severity: detected ? 'medium' : 'low'
    };
}

/**
 * Estimate potential savings from power factor correction
 * @param {number} avgPF - Average power factor
 * @param {Array} data - Array of data points
 * @returns {Object} Potential savings estimate
 */
function estimatePFCorrectionSavings(avgPF, data) {
    // This is a simplified estimation
    if (avgPF >= analysisConfig.powerFactor.optimalMin) {
        return {
            required: false,
            currentPF: avgPF.toFixed(3),
            targetPF: analysisConfig.powerFactor.optimalMin,
            estimatedSavingsPercent: 0,
            note: 'Power factor is already optimal'
        };
    }
    
    // Calculate average kVA
    const avgKVA = data.reduce((sum, d) => sum + parseFloat(d.total_kva), 0) / data.length;
    
    // Calculate potential reduction in kVA demand
    const currentKW = avgKVA * avgPF;
    const targetPF = analysisConfig.powerFactor.optimalMin;
    const newKVA = currentKW / targetPF;
    const kVAReduction = avgKVA - newKVA;
    const savingsPercent = (kVAReduction / avgKVA) * 100;
    
    return {
        required: true,
        currentPF: avgPF.toFixed(3),
        targetPF: targetPF,
        estimatedSavingsPercent: savingsPercent.toFixed(2),
        kVAReduction: kVAReduction.toFixed(2),
        note: 'Estimated savings based on reduced kVA demand charges'
    };
}

/**
 * Detect frequency deviation from nominal
 * @param {Array} frequencyData - Array of frequency values
 * @param {Object} stats - Statistics for frequency data
 * @returns {Object} Frequency deviation analysis
 */
function detectFrequencyDeviation(frequencyData, stats) {
    const nominal = analysisConfig.frequency.nominal;
    const threshold = analysisConfig.frequency.deviationThreshold;
    
    // Calculate deviation from nominal
    const deviations = frequencyData.map(f => Math.abs(f - nominal));
    const deviationStats = calculateStatistics(deviations);
    
    // Determine if deviation is significant
    const detected = deviationStats.max > threshold;
    
    return {
        detected,
        nominal,
        threshold,
        statistics: {
            maxDeviation: deviationStats.max.toFixed(3),
            meanDeviation: deviationStats.mean.toFixed(3),
            percentageAboveThreshold: (deviations.filter(d => d > threshold).length / deviations.length * 100).toFixed(2)
        },
        severity: detected ? (
            deviationStats.max > threshold * 2 ? 'high' : 'medium'
        ) : 'low'
    };
}

/**
 * Assess frequency stability
 * @param {Array} frequencyData - Array of frequency values
 * @returns {Object} Frequency stability analysis
 */
function assessFrequencyStability(frequencyData) {
    // Calculate rate of change
    const changes = [];
    for (let i = 1; i < frequencyData.length; i++) {
        const change = Math.abs(frequencyData[i] - frequencyData[i-1]);
        changes.push(change);
    }
    
    const changeStats = calculateStatistics(changes);
    
    // Threshold for stability (Hz/sample)
    const stabilityThreshold = 0.1;
    
    // Determine if stability is an issue
    const detected = changeStats.p95 > stabilityThreshold;
    
    return {
        detected,
        threshold: stabilityThreshold,
        statistics: {
            maxChange: changeStats.max.toFixed(3),
            p95Change: changeStats.p95.toFixed(3),
            meanChange: changeStats.mean.toFixed(3)
        },
        severity: detected ? (
            changeStats.p95 > stabilityThreshold * 2 ? 'high' : 'medium'
        ) : 'low'
    };
}

// Export helper functions to global scope
window.detectCurrentOverload = detectCurrentOverload;
window.estimateHarmonicIssues = estimateHarmonicIssues;
window.detectLowPowerFactor = detectLowPowerFactor;
window.detectLeadingPowerFactor = detectLeadingPowerFactor;
window.estimatePFCorrectionSavings = estimatePFCorrectionSavings;
window.detectFrequencyDeviation = detectFrequencyDeviation;
window.assessFrequencyStability = assessFrequencyStability;
