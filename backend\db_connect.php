<?php
// Database connection configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

try {
    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Set charset to utf8
    $conn->set_charset("utf8");
    
} catch (Exception $e) {
    // Log error and return JSON response for API endpoints
    error_log("Database connection error: " . $e->getMessage());
    
    // Check if this is an API request (JSO<PERSON> expected)
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'error' => 'Database connection failed',
            'message' => 'Unable to connect to the database. Please check your configuration.',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    } else {
        // For web pages, show HTML error
        die("<p style='color:red'>Database connection failed: " . $e->getMessage() . "</p>");
    }
}
?>
