import React from 'react';
import styled from 'styled-components';
import GraphWidget from '../widgets/GraphWidget';

const DashboardGrid = ({ 
  data, 
  latestData, 
  settings, 
  isPaused, 
  setIsPaused,
  toggleFullscreenWidget
}) => {
  return (
    <DashboardGridContainer>
      {/* Voltage Graph */}
      <GraphWidget
        id="voltageWidget"
        title="Line Voltages"
        icon="bolt"
        chartId="voltageChart"
        labels={['Phase 1', 'Phase 2', 'Phase 3']}
        data={data}
        latestData={latestData}
        dataKeys={['voltage_1', 'voltage_2', 'voltage_3']}
        yAxisLabel="Voltage (V)"
        parameterType="voltage"
        settings={settings}
        isPaused={isPaused}
        setIsPaused={setIsPaused}
        toggleFullscreenWidget={toggleFullscreenWidget}
        formatValue={(value) => `${parseFloat(value).toFixed(settings.decimalPlaces)} V`}
      />

      {/* Current Graph */}
      <GraphWidget
        id="currentWidget"
        title="Phase Currents"
        icon="electric_bolt"
        chartId="currentChart"
        labels={['Phase 1', 'Phase 2', 'Phase 3']}
        data={data}
        latestData={latestData}
        dataKeys={['current_1', 'current_2', 'current_3']}
        yAxisLabel="Current (A)"
        parameterType="current"
        settings={settings}
        isPaused={isPaused}
        setIsPaused={setIsPaused}
        toggleFullscreenWidget={toggleFullscreenWidget}
        formatValue={(value) => `${parseFloat(value).toFixed(settings.decimalPlaces)} A`}
      />

      {/* Power Factor Graph */}
      <GraphWidget
        id="pfWidget"
        title="Power Factors"
        icon="show_chart"
        chartId="pfChart"
        labels={['Phase 1', 'Phase 2', 'Phase 3']}
        data={data}
        latestData={latestData}
        dataKeys={['pf_1', 'pf_2', 'pf_3']}
        yAxisLabel="Power Factor"
        parameterType="pf"
        settings={settings}
        isPaused={isPaused}
        setIsPaused={setIsPaused}
        toggleFullscreenWidget={toggleFullscreenWidget}
        formatValue={(value) => parseFloat(value).toFixed(settings.decimalPlaces)}
      />

      {/* KVA Graph */}
      <GraphWidget
        id="kvaWidget"
        title="KVA Values"
        icon="electric_meter"
        chartId="kvaChart"
        labels={['Phase 1', 'Phase 2', 'Phase 3']}
        data={data}
        latestData={latestData}
        dataKeys={['kva_1', 'kva_2', 'kva_3']}
        yAxisLabel="KVA"
        parameterType="kva"
        settings={settings}
        isPaused={isPaused}
        setIsPaused={setIsPaused}
        toggleFullscreenWidget={toggleFullscreenWidget}
        formatValue={(value) => `${parseFloat(value).toFixed(settings.decimalPlaces)} kVA`}
      />

      {/* Total Power Graph */}
      <GraphWidget
        id="totalPowerWidget"
        title="Total Power"
        icon="power"
        chartId="totalPowerChart"
        labels={['Total KW', 'Total KVA', 'Total KVAR']}
        data={data}
        latestData={latestData}
        dataKeys={['total_kw', 'total_kva', 'total_kvar']}
        yAxisLabel="Power"
        parameterType="totalPower"
        settings={settings}
        isPaused={isPaused}
        setIsPaused={setIsPaused}
        toggleFullscreenWidget={toggleFullscreenWidget}
        formatValue={(value, key) => {
          const unit = key === 'total_kw' ? 'kW' : key === 'total_kva' ? 'kVA' : 'kVAR';
          return `${parseFloat(value).toFixed(settings.decimalPlaces)} ${unit}`;
        }}
      />

      {/* Frequency Graph */}
      <GraphWidget
        id="frequencyWidget"
        title="Frequency"
        icon="speed"
        chartId="frequencyChart"
        labels={['Frequency']}
        data={data}
        latestData={latestData}
        dataKeys={['frequency']}
        yAxisLabel="Frequency (Hz)"
        parameterType="frequency"
        settings={settings}
        isPaused={isPaused}
        setIsPaused={setIsPaused}
        toggleFullscreenWidget={toggleFullscreenWidget}
        formatValue={(value) => `${parseFloat(value).toFixed(settings.decimalPlaces)} Hz`}
      />
    </DashboardGridContainer>
  );
};

const DashboardGridContainer = styled.section`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(640px, 1fr));
  gap: 1.5rem;
  
  @media (max-width: 1400px) {
    grid-template-columns: 1fr;
  }
`;

export default DashboardGrid;
