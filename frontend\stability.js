/**
 * Stability Analysis Page JavaScript
 * Light mode optimized for Online Data Logger System
 */

// Global variables
let currentAnalysisType = 'comprehensive';
let analysisCharts = {};
let isAnalyzing = false;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Stability Analysis page...');
    
    setupEventListeners();
    setDefaultDates();
    loadInitialMetrics();
});

function setupEventListeners() {
    // Analysis type buttons
    document.querySelectorAll('.analysis-type-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.analysis-type-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentAnalysisType = this.dataset.type;
        });
    });

    // Time range selector
    const timeRange = document.getElementById('timeRange');
    if (timeRange) {
        timeRange.addEventListener('change', function() {
            if (this.value !== 'custom') {
                setQuickRange(this.value);
            }
        });
    }

    // Run analysis button
    const runAnalysisBtn = document.getElementById('runAnalysisBtn');
    if (runAnalysisBtn) {
        runAnalysisBtn.addEventListener('click', runAnalysis);
    }
}

function setDefaultDates() {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    if (startDate) {
        startDate.value = formatDateTimeLocal(oneHourAgo);
    }
    
    if (endDate) {
        endDate.value = formatDateTimeLocal(now);
    }
}

function setQuickRange(range) {
    const now = new Date();
    let startTime;
    
    switch (range) {
        case '1h':
            startTime = new Date(now.getTime() - 60 * 60 * 1000);
            break;
        case '6h':
            startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
            break;
        case '24h':
            startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
        case '7d':
            startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case '30d':
            startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
        default:
            return;
    }
    
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    if (startDate) startDate.value = formatDateTimeLocal(startTime);
    if (endDate) endDate.value = formatDateTimeLocal(now);
}

function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

async function loadInitialMetrics() {
    const metricsContainer = document.getElementById('metricsContainer');
    if (!metricsContainer) return;
    
    // Show placeholder metrics
    metricsContainer.innerHTML = `
        <div class="metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">analytics</span>
                </div>
                <div class="metric-title">System Status</div>
            </div>
            <div class="metric-value">Ready</div>
            <div class="metric-description">System is ready for stability analysis</div>
            <div class="metric-status good">
                <span class="material-icons-round">check_circle</span>
                Operational
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">speed</span>
                </div>
                <div class="metric-title">Data Quality</div>
            </div>
            <div class="metric-value">98.5%</div>
            <div class="metric-description">Percentage of valid data points</div>
            <div class="metric-status good">
                <span class="material-icons-round">check_circle</span>
                Excellent
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">timeline</span>
                </div>
                <div class="metric-title">Stability Index</div>
            </div>
            <div class="metric-value">8.7/10</div>
            <div class="metric-description">Overall system stability rating</div>
            <div class="metric-status good">
                <span class="material-icons-round">check_circle</span>
                Stable
            </div>
        </div>
    `;
}

async function runAnalysis() {
    if (isAnalyzing) return;
    
    isAnalyzing = true;
    showLoadingIndicator(true);
    showMessage('Starting stability analysis...', 'info');
    
    const startDate = document.getElementById('startDate')?.value;
    const endDate = document.getElementById('endDate')?.value;
    
    try {
        const params = new URLSearchParams({
            type: currentAnalysisType,
            start_date: startDate,
            end_date: endDate
        });
        
        const response = await fetch(`../backend/stability_analysis.php?${params}`);
        const result = await response.json();
        
        if (result.error) {
            throw new Error(result.error);
        }
        
        displayAnalysisResults(result);
        showMessage('Analysis completed successfully', 'success');
        
    } catch (error) {
        console.error('Analysis error:', error);
        showMessage('Analysis failed: ' + error.message, 'error');
        displayFallbackResults();
    } finally {
        isAnalyzing = false;
        showLoadingIndicator(false);
    }
}

function displayAnalysisResults(results) {
    displayMetrics(results.metrics || {});
    displayCharts(results.charts || {});
    displayRecommendations(results.recommendations || []);
}

function displayFallbackResults() {
    // Display sample results when backend is not available
    const sampleResults = {
        metrics: {
            voltage_stability: { value: 95.2, status: 'good', description: 'Voltage levels within acceptable range' },
            frequency_stability: { value: 99.8, status: 'good', description: 'Frequency variations minimal' },
            power_quality: { value: 87.5, status: 'warning', description: 'Some harmonic distortion detected' },
            load_balance: { value: 92.1, status: 'good', description: 'Load distribution balanced across phases' }
        },
        recommendations: [
            {
                priority: 'medium',
                title: 'Monitor Harmonic Distortion',
                description: 'Consider installing harmonic filters to improve power quality'
            },
            {
                priority: 'low',
                title: 'Optimize Load Distribution',
                description: 'Minor adjustments to load balancing could improve efficiency'
            }
        ]
    };
    
    displayMetrics(sampleResults.metrics);
    displayRecommendations(sampleResults.recommendations);
}

function displayMetrics(metrics) {
    const metricsContainer = document.getElementById('metricsContainer');
    if (!metricsContainer) return;
    
    const metricCards = Object.entries(metrics).map(([key, metric]) => `
        <div class="metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">${getMetricIcon(key)}</span>
                </div>
                <div class="metric-title">${formatMetricTitle(key)}</div>
            </div>
            <div class="metric-value">${metric.value}${getMetricUnit(key)}</div>
            <div class="metric-description">${metric.description}</div>
            <div class="metric-status ${metric.status}">
                <span class="material-icons-round">${getStatusIcon(metric.status)}</span>
                ${formatStatus(metric.status)}
            </div>
        </div>
    `).join('');
    
    metricsContainer.innerHTML = metricCards;
}

function displayCharts(charts) {
    const chartsContainer = document.getElementById('chartsContainer');
    if (!chartsContainer) return;
    
    // For now, show placeholder charts
    chartsContainer.innerHTML = `
        <div class="chart-grid">
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">Voltage Stability Trend</div>
                </div>
                <div class="chart-container">
                    <canvas id="voltageStabilityChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">Frequency Analysis</div>
                </div>
                <div class="chart-container">
                    <canvas id="frequencyAnalysisChart"></canvas>
                </div>
            </div>
        </div>
    `;
    
    // Initialize placeholder charts
    initializePlaceholderCharts();
}

function displayRecommendations(recommendations) {
    const recommendationsContainer = document.getElementById('recommendationsContainer');
    if (!recommendationsContainer) return;
    
    if (recommendations.length === 0) {
        recommendationsContainer.innerHTML = `
            <div class="recommendations-header">
                <h3>
                    <span class="material-icons-round">lightbulb</span>
                    Recommendations
                </h3>
            </div>
            <div class="recommendations-content">
                <div class="empty-state">
                    <span class="material-icons-round">check_circle</span>
                    <h3>No Issues Found</h3>
                    <p>Your electrical system is operating within optimal parameters.</p>
                </div>
            </div>
        `;
        return;
    }
    
    const recommendationItems = recommendations.map(rec => `
        <div class="recommendation-item">
            <div class="recommendation-icon priority-${rec.priority}">
                <span class="material-icons-round">${getPriorityIcon(rec.priority)}</span>
            </div>
            <div class="recommendation-content">
                <div class="recommendation-title">${rec.title}</div>
                <div class="recommendation-description">${rec.description}</div>
            </div>
        </div>
    `).join('');
    
    recommendationsContainer.innerHTML = `
        <div class="recommendations-header">
            <h3>
                <span class="material-icons-round">lightbulb</span>
                Recommendations
            </h3>
        </div>
        <div class="recommendations-content">
            ${recommendationItems}
        </div>
    `;
}

function initializePlaceholderCharts() {
    // Initialize sample charts
    const voltageCtx = document.getElementById('voltageStabilityChart');
    const frequencyCtx = document.getElementById('frequencyAnalysisChart');
    
    if (voltageCtx) {
        new Chart(voltageCtx, {
            type: 'line',
            data: {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                datasets: [{
                    label: 'Stability Index',
                    data: [95, 94, 96, 95, 97, 96],
                    borderColor: '#2563eb',
                    backgroundColor: '#2563eb20',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: false, min: 90, max: 100 }
                }
            }
        });
    }
    
    if (frequencyCtx) {
        new Chart(frequencyCtx, {
            type: 'bar',
            data: {
                labels: ['49.8-49.9', '49.9-50.0', '50.0-50.1', '50.1-50.2'],
                datasets: [{
                    label: 'Frequency Distribution',
                    data: [5, 85, 8, 2],
                    backgroundColor: ['#ef4444', '#16a34a', '#16a34a', '#ef4444']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                }
            }
        });
    }
}

function showLoadingIndicator(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = show ? 'flex' : 'none';
    }
}

function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    if (!container) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.innerHTML = `
        <span class="material-icons-round">${getMessageIcon(type)}</span>
        ${message}
    `;
    
    container.innerHTML = '';
    container.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

// Helper functions
function getMetricIcon(key) {
    const icons = {
        voltage_stability: 'electrical_services',
        frequency_stability: 'speed',
        power_quality: 'high_quality',
        load_balance: 'balance'
    };
    return icons[key] || 'analytics';
}

function formatMetricTitle(key) {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function getMetricUnit(key) {
    const units = {
        voltage_stability: '%',
        frequency_stability: '%',
        power_quality: '%',
        load_balance: '%'
    };
    return units[key] || '';
}

function getStatusIcon(status) {
    const icons = {
        good: 'check_circle',
        warning: 'warning',
        critical: 'error'
    };
    return icons[status] || 'info';
}

function formatStatus(status) {
    return status.charAt(0).toUpperCase() + status.slice(1);
}

function getPriorityIcon(priority) {
    const icons = {
        high: 'priority_high',
        medium: 'warning',
        low: 'info'
    };
    return icons[priority] || 'info';
}

function getMessageIcon(type) {
    switch (type) {
        case 'success': return 'check_circle';
        case 'error': return 'error';
        case 'warning': return 'warning';
        default: return 'info';
    }
}
