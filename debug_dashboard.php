<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Debug - Online Data Logger</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.danger { background: #dc3545; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 200px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Dashboard Loading Debug</h1>
        <p>This tool helps diagnose why the dashboard isn't loading properly.</p>
        
        <div class="test-grid">
            <div class="test-card">
                <h4>📊 Database Check</h4>
                <div id="dbStatus" class="result info">Checking...</div>
                <button class="button" onclick="checkDatabase()">Check Database</button>
            </div>
            
            <div class="test-card">
                <h4>🔗 API Check</h4>
                <div id="apiStatus" class="result info">Checking...</div>
                <button class="button" onclick="checkAPI()">Check API</button>
            </div>
            
            <div class="test-card">
                <h4>📁 Files Check</h4>
                <div id="filesStatus" class="result info">Checking...</div>
                <button class="button" onclick="checkFiles()">Check Files</button>
            </div>
            
            <div class="test-card">
                <h4>🧪 Test Data</h4>
                <div id="testStatus" class="result info">Ready</div>
                <button class="button success" onclick="sendTestData()">Send Test Data</button>
            </div>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>🚀 Dashboard Actions</h3>
            <button class="button" onclick="openDashboard()">Open Dashboard</button>
            <button class="button" onclick="openDashboardNewTab()">Open Dashboard (New Tab)</button>
            <button class="button danger" onclick="clearDatabase()">Clear Database</button>
        </div>
        
        <div id="results"></div>
        
        <div style="margin: 20px 0;">
            <h3>📋 Common Issues & Solutions</h3>
            <ul>
                <li><strong>JavaScript Errors:</strong> Check browser console (F12) for red errors</li>
                <li><strong>API Not Working:</strong> Check if XAMPP MySQL is running</li>
                <li><strong>No Data:</strong> Send test data first</li>
                <li><strong>File Not Found:</strong> Check if all files exist</li>
                <li><strong>Loading Forever:</strong> Usually a JavaScript error or API issue</li>
            </ul>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>🔍 Debug Steps</h3>
            <ol>
                <li>Click all "Check" buttons above</li>
                <li>Send test data if database is empty</li>
                <li>Open dashboard and check browser console (F12)</li>
                <li>Look for error messages in console</li>
                <li>Report any red error messages you see</li>
            </ol>
        </div>
    </div>

    <script>
        // Auto-run checks on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkDatabase();
                checkAPI();
                checkFiles();
            }, 500);
        });
        
        async function checkDatabase() {
            const statusDiv = document.getElementById('dbStatus');
            statusDiv.innerHTML = '<span class="status-indicator status-pending"></span>Checking database...';
            
            try {
                const response = await fetch('backend/get_latest_data.php?records=1');
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (Array.isArray(data) && data.length > 0) {
                        statusDiv.innerHTML = `
                            <span class="status-indicator status-success"></span>
                            Database OK - ${data.length} record(s) found
                        `;
                    } else {
                        statusDiv.innerHTML = `
                            <span class="status-indicator status-error"></span>
                            Database empty - no records found
                        `;
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                statusDiv.innerHTML = `
                    <span class="status-indicator status-error"></span>
                    Database error: ${error.message}
                `;
            }
        }
        
        async function checkAPI() {
            const statusDiv = document.getElementById('apiStatus');
            statusDiv.innerHTML = '<span class="status-indicator status-pending"></span>Checking API...';
            
            try {
                // Test the exact same call the dashboard makes
                const timestamp = new Date().getTime();
                const response = await fetch(`backend/get_latest_data.php?_=${timestamp}&records=100`);
                
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.innerHTML = `
                        <span class="status-indicator status-success"></span>
                        API OK - Returns ${Array.isArray(data) ? data.length : 1} records
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                statusDiv.innerHTML = `
                    <span class="status-indicator status-error"></span>
                    API error: ${error.message}
                `;
            }
        }
        
        async function checkFiles() {
            const statusDiv = document.getElementById('filesStatus');
            statusDiv.innerHTML = '<span class="status-indicator status-pending"></span>Checking files...';
            
            const filesToCheck = [
                'frontend/advanced_dashboard.php',
                'frontend/advanced-dashboard.js',
                'frontend/light-theme.css',
                'backend/get_latest_data.php',
                'backend/receive_data.php'
            ];
            
            let allFilesOk = true;
            const results = [];
            
            for (const file of filesToCheck) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        results.push(`✅ ${file}`);
                    } else {
                        results.push(`❌ ${file} (${response.status})`);
                        allFilesOk = false;
                    }
                } catch (error) {
                    results.push(`❌ ${file} (${error.message})`);
                    allFilesOk = false;
                }
            }
            
            statusDiv.innerHTML = `
                <span class="status-indicator ${allFilesOk ? 'status-success' : 'status-error'}"></span>
                ${allFilesOk ? 'All files OK' : 'Some files missing'}
                <details>
                    <summary>File Details</summary>
                    <pre>${results.join('\n')}</pre>
                </details>
            `;
        }
        
        async function sendTestData() {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.innerHTML = '<span class="status-indicator status-pending"></span>Sending test data...';
            
            const testData = {
                voltage_1: 230.5, voltage_2: 231.2, voltage_3: 229.8,
                current_1: 5.1, current_2: 5.2, current_3: 5.0,
                pf_1: 0.92, pf_2: 0.93, pf_3: 0.91,
                kva_1: 1150.5, kva_2: 1151.2, kva_3: 1149.8,
                total_kva: 3450.5, total_kw: 3277.8, total_kvar: 1076.5,
                frequency: 50.1
            };
            
            try {
                const response = await fetch('backend/receive_data.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    statusDiv.innerHTML = `
                        <span class="status-indicator status-success"></span>
                        Test data sent successfully
                    `;
                    
                    // Refresh database check
                    setTimeout(checkDatabase, 500);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                statusDiv.innerHTML = `
                    <span class="status-indicator status-error"></span>
                    Failed to send test data: ${error.message}
                `;
            }
        }
        
        function openDashboard() {
            window.location.href = 'frontend/advanced_dashboard.php';
        }
        
        function openDashboardNewTab() {
            window.open('frontend/advanced_dashboard.php', '_blank');
        }
        
        async function clearDatabase() {
            if (!confirm('Are you sure you want to clear all data?')) {
                return;
            }
            
            try {
                const response = await fetch('backend/clear_data.php', { method: 'POST' });
                
                if (response.ok) {
                    document.getElementById('results').innerHTML = `
                        <div class="success">✅ Database cleared successfully</div>
                    `;
                    
                    // Refresh checks
                    setTimeout(() => {
                        checkDatabase();
                        checkAPI();
                    }, 500);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <div class="error">❌ Failed to clear database: ${error.message}</div>
                `;
            }
        }
    </script>
</body>
</html>
