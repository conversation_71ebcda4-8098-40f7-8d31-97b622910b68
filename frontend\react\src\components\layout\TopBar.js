import React from 'react';
import styled from 'styled-components';
import PrintButton from '../ui/PrintButton';

const TopBar = ({
  lastUpdatedTime,
  timeDisplay,
  onPrevHourClick,
  onNextHourClick,
  onAutoRangeClick,
  onExportClick
}) => {
  return (
    <TopBarContainer>
      <PageTitle>
        <TitleSection>
          <h1>Enterprise Power Monitoring</h1>
          <div className="subtitle">Real-time electrical parameter monitoring</div>
        </TitleSection>
        <StatusSection>
          <StatusItem>
            <span className="material-icons-round status-icon online">check_circle</span>
            <StatusDetails>
              <StatusLabel>System Status</StatusLabel>
              <StatusValue>Operational</StatusValue>
            </StatusDetails>
          </StatusItem>
          <StatusItem>
            <span className="material-icons-round status-icon">schedule</span>
            <StatusDetails>
              <StatusLabel>Last Updated</StatusLabel>
              <StatusValue>{lastUpdatedTime}</StatusValue>
            </StatusDetails>
          </StatusItem>
        </StatusSection>
      </PageTitle>

      <TopControls>
        <TimeNavigation>
          <IconButton onClick={onPrevHourClick} title="Previous Hour">
            <span className="material-icons-round">arrow_back</span>
          </IconButton>
          <TimeDisplay>{timeDisplay}</TimeDisplay>
          <IconButton onClick={onNextHourClick} title="Next Hour">
            <span className="material-icons-round">arrow_forward</span>
          </IconButton>
        </TimeNavigation>

        <ControlButtons>
          <ControlButton onClick={onAutoRangeClick}>
            <span className="material-icons-round">auto_fix_high</span>
            <span>Auto Range</span>
          </ControlButton>

          <ControlButton className="primary" onClick={() => window.location.href = 'history.php'}>
            <span className="material-icons-round">history</span>
            <span>Historical View</span>
          </ControlButton>

          <ControlButton className="secondary" onClick={onExportClick}>
            <span className="material-icons-round">download</span>
            <span>Export Data</span>
          </ControlButton>

          <PrintButton showOptions={true} />
        </ControlButtons>
      </TopControls>
    </TopBarContainer>
  );
};

const TopBarContainer = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1.25rem 1.5rem;
  background-color: var(--card-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0.25rem;
    background: var(--gradient-primary);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
`;

const PageTitle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2rem;
`;

const TitleSection = styled.div`
  display: flex;
  flex-direction: column;

  h1 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.25rem;
    letter-spacing: -0.01em;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }
`;

const StatusSection = styled.div`
  display: flex;
  gap: 1.5rem;
  margin-left: auto;
`;

const StatusItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--card-color);
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);

  .status-icon {
    font-size: 1.25rem;
    color: var(--text-secondary);
  }

  .status-icon.online {
    color: var(--success-color);
  }
`;

const StatusDetails = styled.div`
  display: flex;
  flex-direction: column;
`;

const StatusLabel = styled.div`
  font-size: 0.6875rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const StatusValue = styled.div`
  font-size: 0.8125rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
`;

const TopControls = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;

  @media (max-width: 768px) {
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
  }
`;

const TimeNavigation = styled.div`
  display: flex;
  align-items: center;
  background-color: rgba(0, 86, 179, 0.05);
  border-radius: var(--border-radius-lg);
  padding: 0.25rem;
  margin-right: 0.5rem;
  border: 1px solid rgba(0, 86, 179, 0.1);
  box-shadow: var(--shadow-sm);

  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
`;

const TimeDisplay = styled.div`
  padding: 0.5rem 0.875rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  margin: 0 0.25rem;
  font-size: 0.875rem;
  min-width: 7.5rem;
  text-align: center;
`;

const IconButton = styled.button`
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    opacity: 0;
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease, opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 0.1;
    transform: scale(1);
  }

  &:hover {
    color: var(--primary-color);
  }
`;

const ControlButtons = styled.div`
  display: flex;
  gap: 0.75rem;
  align-items: center;

  @media (max-width: 768px) {
    width: 100%;
  }
`;

const ControlButton = styled.button`
  padding: 0.625rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--card-color);
  color: var(--text-color);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
    z-index: -1;
  }

  &:hover {
    border-color: var(--primary-light);
    color: var(--primary-color);
  }

  &.primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 86, 179, 0.3);

    &::before {
      background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
      opacity: 0;
    }

    &:hover {
      box-shadow: 0 4px 10px rgba(0, 86, 179, 0.4);
      transform: translateY(-2px);
      color: white;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  &.secondary {
    background: var(--gradient-secondary);
    color: white;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 166, 126, 0.3);

    &::before {
      background: linear-gradient(135deg, var(--secondary-color), #00875a);
      opacity: 0;
    }

    &:hover {
      box-shadow: 0 4px 10px rgba(0, 166, 126, 0.4);
      transform: translateY(-2px);
      color: white;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  @media (max-width: 768px) {
    flex: 1;
    justify-content: center;
  }
`;

export default TopBar;
