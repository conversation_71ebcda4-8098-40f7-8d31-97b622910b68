<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.1/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0/dist/chartjs-adapter-luxon.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 30px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chart.js Time Series Test</h1>
        
        <div class="controls">
            <button id="addDataBtn">Add Data Point</button>
            <button id="clearDataBtn">Clear Data</button>
        </div>
        
        <div class="chart-container">
            <canvas id="testChart"></canvas>
        </div>
        
        <h2>Chart Data:</h2>
        <pre id="dataDisplay"></pre>
    </div>
    
    <script>
        // Initialize chart
        const ctx = document.getElementById('testChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [{
                    label: 'Test Data',
                    data: [],
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    pointRadius: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'second',
                            displayFormats: {
                                second: 'HH:mm:ss'
                            },
                            tooltipFormat: 'HH:mm:ss'
                        },
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    },
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Value'
                        }
                    }
                }
            }
        });
        
        // Function to add a data point
        function addDataPoint() {
            const now = new Date();
            const value = Math.random() * 100;
            
            chart.data.datasets[0].data.push({
                x: now,
                y: value
            });
            
            // Remove oldest data if we have more than 10 points
            if (chart.data.datasets[0].data.length > 10) {
                chart.data.datasets[0].data.shift();
            }
            
            // Update chart
            chart.update();
            
            // Update data display
            updateDataDisplay();
        }
        
        // Function to clear data
        function clearData() {
            chart.data.datasets[0].data = [];
            chart.update();
            updateDataDisplay();
        }
        
        // Function to update data display
        function updateDataDisplay() {
            const dataDisplay = document.getElementById('dataDisplay');
            const data = chart.data.datasets[0].data;
            
            let html = '';
            data.forEach((point, index) => {
                html += `Point ${index + 1}: x = ${point.x.toLocaleTimeString()}, y = ${point.y.toFixed(2)}\n`;
            });
            
            dataDisplay.textContent = html;
        }
        
        // Add event listeners
        document.getElementById('addDataBtn').addEventListener('click', addDataPoint);
        document.getElementById('clearDataBtn').addEventListener('click', clearData);
        
        // Add initial data points
        for (let i = 0; i < 5; i++) {
            setTimeout(addDataPoint, i * 500);
        }
    </script>
</body>
</html>
