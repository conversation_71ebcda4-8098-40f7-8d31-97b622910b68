/* Import base light theme */
@import url('light-theme.css');

/* History Page Specific Styles */
.history-container {
    background-color: var(--light-card-bg);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--light-shadow);
}

/* Quick Filters */
.quick-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.quick-filter {
    padding: 8px 16px;
    background-color: var(--light-hover);
    border: 1px solid var(--light-border);
    border-radius: 20px;
    color: var(--light-text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.quick-filter:hover {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.quick-filter.active {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

/* Filters */
.filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background-color: var(--light-hover);
    border-radius: 8px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: end;
}

/* Data Visualization */
.data-visualization {
    margin-bottom: 30px;
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 8px;
    overflow: hidden;
}

.visualization-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--light-hover);
    border-bottom: 1px solid var(--light-border);
}

.visualization-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--light-text);
}

.parameter-selection {
    display: flex;
    gap: 5px;
}

.parameter-group {
    display: flex;
    gap: 5px;
}

.parameter-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 6px;
    color: var(--light-text-secondary);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.parameter-btn:hover {
    background-color: var(--light-hover);
    color: var(--accent-primary);
    border-color: var(--accent-primary);
}

.parameter-btn.active {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.parameter-btn .material-icons-round {
    font-size: 16px;
}

.visualization-actions {
    display: flex;
    gap: 5px;
}

.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-border);
    border-radius: 6px;
    color: var(--light-text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background-color: var(--light-hover);
    color: var(--accent-primary);
    border-color: var(--accent-primary);
}

.action-button.active {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.action-button .material-icons-round {
    font-size: 18px;
}

/* Chart Container */
#historyChart {
    width: 100%;
    height: 400px;
    padding: 20px;
    background-color: var(--light-card-bg);
}

/* Data Table */
.data-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid var(--light-border);
}

.data-table-container table {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
}

.loading {
    text-align: center;
    color: var(--light-text-secondary);
    font-style: italic;
    padding: 40px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filters {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        grid-column: 1;
        justify-content: stretch;
    }
    
    .filter-actions .button {
        flex: 1;
        justify-content: center;
    }
    
    .visualization-controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .parameter-selection {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .visualization-actions {
        justify-content: center;
    }
    
    .quick-filters {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .history-container {
        padding: 15px;
    }
    
    .parameter-btn {
        padding: 6px 8px;
        font-size: 0.7rem;
    }
    
    .parameter-btn .material-icons-round {
        font-size: 14px;
    }
    
    .quick-filter {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    #historyChart {
        height: 300px;
        padding: 10px;
    }
}
