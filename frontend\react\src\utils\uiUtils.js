/**
 * Utility functions for UI and theming
 */

/**
 * Generate a custom theme based on primary color
 * @param {string} primaryColor - Primary color in hex format
 * @param {boolean} isDark - Whether to generate a dark theme
 * @returns {Object} - Theme object with color variables
 */
export const generateTheme = (primaryColor = '#2563eb', isDark = false) => {
  // Convert hex to RGB for calculations
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };
  
  // Adjust color brightness
  const adjustBrightness = (hex, factor) => {
    const rgb = hexToRgb(hex);
    if (!rgb) return hex;
    
    const newRgb = {
      r: Math.max(0, Math.min(255, Math.round(rgb.r * factor))),
      g: Math.max(0, Math.min(255, Math.round(rgb.g * factor))),
      b: Math.max(0, Math.min(255, Math.round(rgb.b * factor)))
    };
    
    return `#${newRgb.r.toString(16).padStart(2, '0')}${newRgb.g.toString(16).padStart(2, '0')}${newRgb.b.toString(16).padStart(2, '0')}`;
  };
  
  // Generate complementary colors
  const rgbPrimary = hexToRgb(primaryColor);
  const hslPrimary = rgbToHsl(rgbPrimary.r, rgbPrimary.g, rgbPrimary.b);
  
  // Generate secondary color (120° shift)
  const hslSecondary = { ...hslPrimary, h: (hslPrimary.h + 120) % 360 };
  const rgbSecondary = hslToRgb(hslSecondary.h, hslSecondary.s, hslSecondary.l);
  const secondaryColor = rgbToHex(rgbSecondary.r, rgbSecondary.g, rgbSecondary.b);
  
  // Generate accent color (180° shift)
  const hslAccent = { ...hslPrimary, h: (hslPrimary.h + 180) % 360 };
  const rgbAccent = hslToRgb(hslAccent.h, hslAccent.s, hslAccent.l);
  const accentColor = rgbToHex(rgbAccent.r, rgbAccent.g, rgbAccent.b);
  
  // Generate warning color (60° shift)
  const hslWarning = { ...hslPrimary, h: (hslPrimary.h + 60) % 360 };
  const rgbWarning = hslToRgb(hslWarning.h, hslWarning.s, hslWarning.l);
  const warningColor = rgbToHex(rgbWarning.r, rgbWarning.g, rgbWarning.b);
  
  // Generate shades
  const primaryDark = adjustBrightness(primaryColor, 0.8);
  const primaryLight = adjustBrightness(primaryColor, 1.2);
  
  // Base theme
  const baseTheme = {
    primaryColor,
    primaryDark,
    primaryLight,
    secondaryColor,
    accentColor,
    warningColor,
    successColor: secondaryColor, // Use secondary as success
    infoColor: primaryLight,
  };
  
  // Light or dark theme specific colors
  if (isDark) {
    return {
      ...baseTheme,
      backgroundColor: '#0f172a',
      cardColor: '#1e293b',
      borderColor: '#334155',
      textColor: '#f8fafc',
      textSecondary: '#cbd5e1',
      textMuted: '#94a3b8',
      hoverColor: '#273549',
    };
  } else {
    return {
      ...baseTheme,
      backgroundColor: '#f8fafc',
      cardColor: '#ffffff',
      borderColor: '#e2e8f0',
      textColor: '#0f172a',
      textSecondary: '#475569',
      textMuted: '#94a3b8',
      hoverColor: '#f1f5f9',
    };
  }
};

/**
 * Apply theme to document by setting CSS variables
 * @param {Object} theme - Theme object with color variables
 */
export const applyTheme = (theme) => {
  const root = document.documentElement;
  
  // Apply each theme property as a CSS variable
  Object.entries(theme).forEach(([key, value]) => {
    // Convert camelCase to kebab-case for CSS variables
    const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
    root.style.setProperty(`--${cssKey}`, value);
  });
};

/**
 * Convert RGB to HSL
 * @param {number} r - Red (0-255)
 * @param {number} g - Green (0-255)
 * @param {number} b - Blue (0-255)
 * @returns {Object} - HSL values
 */
function rgbToHsl(r, g, b) {
  r /= 255;
  g /= 255;
  b /= 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h, s, l = (max + min) / 2;
  
  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    
    h /= 6;
  }
  
  return { h: h * 360, s, l };
}

/**
 * Convert HSL to RGB
 * @param {number} h - Hue (0-360)
 * @param {number} s - Saturation (0-1)
 * @param {number} l - Lightness (0-1)
 * @returns {Object} - RGB values
 */
function hslToRgb(h, s, l) {
  h /= 360;
  
  let r, g, b;
  
  if (s === 0) {
    r = g = b = l; // achromatic
  } else {
    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };
    
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }
  
  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  };
}

/**
 * Convert RGB to hex
 * @param {number} r - Red (0-255)
 * @param {number} g - Green (0-255)
 * @param {number} b - Blue (0-255)
 * @returns {string} - Hex color
 */
function rgbToHex(r, g, b) {
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

/**
 * Check if a color is light or dark
 * @param {string} color - Color in hex format
 * @returns {boolean} - True if color is light
 */
export const isLightColor = (color) => {
  const rgb = hexToRgb(color);
  if (!rgb) return true;
  
  // Calculate relative luminance
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  return luminance > 0.5;
};
