<?php
// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>API Test</h1>";

// Check if database exists
$result = $conn->query("SHOW DATABASES LIKE '$dbname'");
if ($result->num_rows == 0) {
    echo "<p style='color:red'>Database $dbname does not exist!</p>";
    exit;
}

// Select database
$conn->select_db($dbname);

// Check if table exists
$result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
if ($result->num_rows == 0) {
    echo "<p style='color:red'>Table electrical_data does not exist!</p>";
    exit;
}

// Insert a test record with current timestamp
$timestamp = date('Y-m-d H:i:s');
$sql = "INSERT INTO electrical_data (
    voltage_1, voltage_2, voltage_3,
    current_1, current_2, current_3,
    pf_1, pf_2, pf_3,
    kva_1, kva_2, kva_3,
    total_kva, total_kw, total_kvar,
    frequency, timestamp
) VALUES (
    400.5, 401.2, 399.8,
    50.1, 49.8, 50.3,
    0.92, 0.93, 0.91,
    20.1, 19.8, 20.3,
    60.2, 55.4, 24.8,
    50.1, '$timestamp'
)";

if ($conn->query($sql) === TRUE) {
    echo "<p style='color:green'>Test record inserted successfully with timestamp: $timestamp</p>";
} else {
    echo "<p style='color:red'>Error inserting test record: " . $conn->error . "</p>";
}

// Test get_latest_data.php
echo "<h2>Testing get_latest_data.php</h2>";
echo "<pre>";
$url = "http://localhost/online%20data%20logger/backend/get_latest_data.php";
$response = file_get_contents($url);
echo htmlspecialchars($response);
echo "</pre>";

// Close the connection
$conn->close();

echo "<p><a href='frontend/'>Go to Dashboard</a></p>";
?>
