import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import { ThemeProvider } from './contexts/ThemeContext';
import { DashboardProvider } from './contexts/DashboardContext';
import { AnalyticsProvider } from './contexts/AnalyticsContext';
import './styles/global.css';

// Remove loading screen when React is ready
const hideLoadingScreen = () => {
  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    loadingElement.style.opacity = '0';
    setTimeout(() => {
      loadingElement.style.display = 'none';
    }, 500);
  }
};

// Initialize app
const initializeApp = () => {
  const container = document.getElementById('root');
  const root = createRoot(container);

  root.render(
    <React.StrictMode>
      <ThemeProvider>
        <App />
      </ThemeProvider>
    </React.StrictMode>
  );

  // Hide loading screen after React has rendered
  setTimeout(hideLoadingScreen, 500);
};

// Start the app
initializeApp();
