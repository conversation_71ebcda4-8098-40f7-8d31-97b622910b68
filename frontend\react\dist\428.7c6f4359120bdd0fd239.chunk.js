"use strict";(self.webpackChunkpower_monitor_pro_dashboard=self.webpackChunkpower_monitor_pro_dashboard||[]).push([[428],{428:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(540);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(523);\n/* harmony import */ var _Modal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(515);\nfunction _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }\nvar _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;\nfunction _taggedTemplateLiteral(e, t) { return t || (t = e.slice(0)), Object.freeze(Object.defineProperties(e, { raw: { value: Object.freeze(t) } })); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }\nfunction _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\n\n\n\nvar SettingsModal = function SettingsModal(_ref) {\n  var isOpen = _ref.isOpen,\n    onClose = _ref.onClose,\n    settings = _ref.settings,\n    saveSettings = _ref.saveSettings,\n    resetSettings = _ref.resetSettings;\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n      refreshRate: 2,\n      timeWindow: 1,\n      decimalPlaces: 3,\n      chartTheme: \'professional\',\n      lineThickness: 0.5,\n      showGridLines: true,\n      voltageAlertHigh: 450,\n      voltageAlertLow: 350,\n      frequencyAlertHigh: 55,\n      frequencyAlertLow: 45\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    formValues = _useState2[0],\n    setFormValues = _useState2[1];\n\n  // Update form values when settings change\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (settings) {\n      setFormValues(settings);\n    }\n  }, [settings]);\n\n  // Handle input change\n  var handleChange = function handleChange(e) {\n    var _e$target = e.target,\n      name = _e$target.name,\n      value = _e$target.value,\n      type = _e$target.type,\n      checked = _e$target.checked;\n    setFormValues(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, _defineProperty({}, name, type === \'checkbox\' ? checked : type === \'number\' ? parseFloat(value) : value));\n    });\n  };\n\n  // Handle form submission\n  var handleSubmit = function handleSubmit(e) {\n    e.preventDefault();\n    saveSettings(formValues);\n    onClose();\n  };\n\n  // Handle reset\n  var handleReset = function handleReset() {\n    resetSettings();\n    onClose();\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_Modal__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A, {\n    isOpen: isOpen,\n    onClose: onClose,\n    title: "Dashboard Settings"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingsForm, {\n    onSubmit: handleSubmit\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingsSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h3", null, "Display Settings"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "refreshRate"\n  }, "Data Refresh Rate (seconds)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {\n    type: "number",\n    id: "refreshRate",\n    name: "refreshRate",\n    min: "1",\n    max: "60",\n    value: formValues.refreshRate,\n    onChange: handleChange\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "timeWindow"\n  }, "Default Time Window (minutes)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {\n    type: "number",\n    id: "timeWindow",\n    name: "timeWindow",\n    min: "1",\n    max: "60",\n    value: formValues.timeWindow,\n    onChange: handleChange\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "decimalPlaces"\n  }, "Decimal Places"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {\n    type: "number",\n    id: "decimalPlaces",\n    name: "decimalPlaces",\n    min: "0",\n    max: "5",\n    value: formValues.decimalPlaces,\n    onChange: handleChange\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingsSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h3", null, "Chart Settings"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "chartTheme"\n  }, "Chart Theme"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("select", {\n    id: "chartTheme",\n    name: "chartTheme",\n    value: formValues.chartTheme,\n    onChange: handleChange\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("option", {\n    value: "professional"\n  }, "Professional"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("option", {\n    value: "material"\n  }, "Material"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("option", {\n    value: "pastel"\n  }, "Pastel"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "lineThickness"\n  }, "Line Thickness"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {\n    type: "range",\n    id: "lineThickness",\n    name: "lineThickness",\n    min: "0.5",\n    max: "3",\n    step: "0.5",\n    value: formValues.lineThickness,\n    onChange: handleChange\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, {\n    className: "checkbox"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {\n    type: "checkbox",\n    id: "showGridLines",\n    name: "showGridLines",\n    checked: formValues.showGridLines,\n    onChange: handleChange\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "showGridLines"\n  }, "Show Grid Lines"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingsSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h3", null, "Alert Settings"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "voltageAlertHigh"\n  }, "Voltage High Alert (V)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {\n    type: "number",\n    id: "voltageAlertHigh",\n    name: "voltageAlertHigh",\n    value: formValues.voltageAlertHigh,\n    onChange: handleChange\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "voltageAlertLow"\n  }, "Voltage Low Alert (V)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {\n    type: "number",\n    id: "voltageAlertLow",\n    name: "voltageAlertLow",\n    value: formValues.voltageAlertLow,\n    onChange: handleChange\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "frequencyAlertHigh"\n  }, "Frequency High Alert (Hz)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {\n    type: "number",\n    id: "frequencyAlertHigh",\n    name: "frequencyAlertHigh",\n    value: formValues.frequencyAlertHigh,\n    onChange: handleChange\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(SettingItem, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("label", {\n    htmlFor: "frequencyAlertLow"\n  }, "Frequency Low Alert (Hz)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {\n    type: "number",\n    id: "frequencyAlertLow",\n    name: "frequencyAlertLow",\n    value: formValues.frequencyAlertLow,\n    onChange: handleChange\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ModalFooter, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ModalButton, {\n    type: "button",\n    className: "secondary",\n    onClick: handleReset\n  }, "Reset to Defaults"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ModalButton, {\n    type: "submit",\n    className: "primary"\n  }, "Save Settings"))));\n};\nvar SettingsForm = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.form(_templateObject || (_templateObject = _taggedTemplateLiteral(["\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2rem;\\n"])));\nvar SettingsSection = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = _taggedTemplateLiteral(["\\n  margin-bottom: 2rem;\\n  \\n  h3 {\\n    font-size: 1.125rem;\\n    font-weight: var(--font-weight-semibold);\\n    color: var(--text-color);\\n    margin-bottom: 1.25rem;\\n    padding-bottom: 0.75rem;\\n    border-bottom: 1px solid var(--border-color);\\n    letter-spacing: -0.01em;\\n  }\\n"])));\nvar SettingItem = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = _taggedTemplateLiteral(["\\n  margin-bottom: 1.25rem;\\n  \\n  label {\\n    display: block;\\n    font-size: 0.9375rem;\\n    font-weight: var(--font-weight-medium);\\n    color: var(--text-color);\\n    margin-bottom: 0.5rem;\\n  }\\n  \\n  input[type=\\"number\\"],\\n  input[type=\\"text\\"],\\n  select {\\n    width: 100%;\\n    padding: 0.75rem 1rem;\\n    border: 1px solid var(--border-color);\\n    border-radius: var(--border-radius);\\n    font-size: 0.9375rem;\\n    color: var(--text-color);\\n    background-color: var(--card-color);\\n    transition: all var(--transition-speed) ease;\\n    box-shadow: var(--shadow-sm);\\n    \\n    &:focus {\\n      border-color: var(--primary-color);\\n      box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.15);\\n      outline: none;\\n    }\\n  }\\n  \\n  input[type=\\"range\\"] {\\n    width: 100%;\\n    height: 0.5rem;\\n    -webkit-appearance: none;\\n    background: var(--hover-color);\\n    border-radius: 0.25rem;\\n    outline: none;\\n    margin: 0.75rem 0;\\n    \\n    &::-webkit-slider-thumb {\\n      -webkit-appearance: none;\\n      width: 1.25rem;\\n      height: 1.25rem;\\n      border-radius: 50%;\\n      background: var(--primary-color);\\n      cursor: pointer;\\n      box-shadow: var(--shadow-sm);\\n      border: 2px solid white;\\n    }\\n  }\\n  \\n  &.checkbox {\\n    display: flex;\\n    align-items: center;\\n    gap: 0.75rem;\\n    \\n    input {\\n      width: 1.125rem;\\n      height: 1.125rem;\\n      accent-color: var(--primary-color);\\n    }\\n    \\n    label {\\n      margin-bottom: 0;\\n      cursor: pointer;\\n    }\\n  }\\n"])));\nvar ModalFooter = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = _taggedTemplateLiteral(["\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1rem;\\n"])));\nvar ModalButton = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.button(_templateObject5 || (_templateObject5 = _taggedTemplateLiteral(["\\n  padding: 0.625rem 1.25rem;\\n  border: 1px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  cursor: pointer;\\n  font-weight: var(--font-weight-medium);\\n  font-size: 0.875rem;\\n  transition: all var(--transition-speed) ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  \\n  &.primary {\\n    background: var(--gradient-primary);\\n    color: white;\\n    border: none;\\n    \\n    &:hover {\\n      box-shadow: var(--shadow-md);\\n      transform: translateY(-1px);\\n    }\\n  }\\n  \\n  &.secondary {\\n    background-color: var(--card-color);\\n    color: var(--text-color);\\n    \\n    &:hover {\\n      background-color: var(--hover-color);\\n      border-color: var(--primary-light);\\n    }\\n  }\\n"])));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsModal);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///428\n')}}]);