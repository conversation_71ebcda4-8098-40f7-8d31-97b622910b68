/**
 * Helper functions for AI analysis of electrical system data
 */

/**
 * Calculate basic statistics for an array of values
 * @param {Array} values - Array of numeric values
 * @returns {Object} Statistical measures
 */
function calculateStatistics(values) {
    if (!values || values.length === 0) {
        return {
            min: 0,
            max: 0,
            mean: 0,
            median: 0,
            stdDev: 0,
            range: 0
        };
    }
    
    // Sort values for percentile calculations
    const sortedValues = [...values].sort((a, b) => a - b);
    
    // Calculate basic statistics
    const min = Math.min(...values);
    const max = Math.max(...values);
    const sum = values.reduce((acc, val) => acc + val, 0);
    const mean = sum / values.length;
    
    // Calculate median
    const mid = Math.floor(sortedValues.length / 2);
    const median = sortedValues.length % 2 === 0 
        ? (sortedValues[mid - 1] + sortedValues[mid]) / 2 
        : sortedValues[mid];
    
    // Calculate standard deviation
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    const variance = squaredDiffs.reduce((acc, val) => acc + val, 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    // Calculate range
    const range = max - min;
    
    // Calculate percentiles
    const p95 = sortedValues[Math.floor(sortedValues.length * 0.95)];
    const p5 = sortedValues[Math.floor(sortedValues.length * 0.05)];
    
    return {
        min,
        max,
        mean,
        median,
        stdDev,
        range,
        p5,
        p95
    };
}

/**
 * Detect high voltage conditions
 * @param {Object} voltageData - Object with phase voltage arrays
 * @param {Object} stats - Statistics for each phase
 * @returns {Object} High voltage analysis
 */
function detectHighVoltage(voltageData, stats) {
    const threshold = analysisConfig.voltage.highThreshold;
    
    // Check if any phase exceeds the high voltage threshold
    const phase1Exceeded = stats.phase1.max > threshold;
    const phase2Exceeded = stats.phase2.max > threshold;
    const phase3Exceeded = stats.phase3.max > threshold;
    
    // Calculate percentage of time above threshold for each phase
    const phase1Percentage = voltageData.phase1.filter(v => v > threshold).length / voltageData.phase1.length * 100;
    const phase2Percentage = voltageData.phase2.filter(v => v > threshold).length / voltageData.phase2.length * 100;
    const phase3Percentage = voltageData.phase3.filter(v => v > threshold).length / voltageData.phase3.length * 100;
    
    // Determine if high voltage is a significant issue
    const detected = phase1Percentage > 5 || phase2Percentage > 5 || phase3Percentage > 5;
    
    return {
        detected,
        threshold,
        phases: {
            phase1: {
                exceeded: phase1Exceeded,
                percentage: phase1Percentage.toFixed(2),
                maxValue: stats.phase1.max
            },
            phase2: {
                exceeded: phase2Exceeded,
                percentage: phase2Percentage.toFixed(2),
                maxValue: stats.phase2.max
            },
            phase3: {
                exceeded: phase3Exceeded,
                percentage: phase3Percentage.toFixed(2),
                maxValue: stats.phase3.max
            }
        },
        severity: detected ? (
            Math.max(phase1Percentage, phase2Percentage, phase3Percentage) > 20 ? 'high' : 'medium'
        ) : 'low'
    };
}

/**
 * Detect low voltage conditions
 * @param {Object} voltageData - Object with phase voltage arrays
 * @param {Object} stats - Statistics for each phase
 * @returns {Object} Low voltage analysis
 */
function detectLowVoltage(voltageData, stats) {
    const threshold = analysisConfig.voltage.lowThreshold;
    
    // Check if any phase falls below the low voltage threshold
    const phase1Below = stats.phase1.min < threshold;
    const phase2Below = stats.phase2.min < threshold;
    const phase3Below = stats.phase3.min < threshold;
    
    // Calculate percentage of time below threshold for each phase
    const phase1Percentage = voltageData.phase1.filter(v => v < threshold).length / voltageData.phase1.length * 100;
    const phase2Percentage = voltageData.phase2.filter(v => v < threshold).length / voltageData.phase2.length * 100;
    const phase3Percentage = voltageData.phase3.filter(v => v < threshold).length / voltageData.phase3.length * 100;
    
    // Determine if low voltage is a significant issue
    const detected = phase1Percentage > 5 || phase2Percentage > 5 || phase3Percentage > 5;
    
    return {
        detected,
        threshold,
        phases: {
            phase1: {
                below: phase1Below,
                percentage: phase1Percentage.toFixed(2),
                minValue: stats.phase1.min
            },
            phase2: {
                below: phase2Below,
                percentage: phase2Percentage.toFixed(2),
                minValue: stats.phase2.min
            },
            phase3: {
                below: phase3Below,
                percentage: phase3Percentage.toFixed(2),
                minValue: stats.phase3.min
            }
        },
        severity: detected ? (
            Math.max(phase1Percentage, phase2Percentage, phase3Percentage) > 20 ? 'high' : 'medium'
        ) : 'low'
    };
}

/**
 * Detect voltage imbalance between phases
 * @param {Object} voltageData - Object with phase voltage arrays
 * @param {Object} stats - Statistics for each phase
 * @returns {Object} Voltage imbalance analysis
 */
function detectVoltageImbalance(voltageData, stats) {
    const threshold = analysisConfig.voltage.imbalanceThreshold;
    
    // Calculate imbalance for each data point
    const imbalances = [];
    for (let i = 0; i < voltageData.phase1.length; i++) {
        const values = [voltageData.phase1[i], voltageData.phase2[i], voltageData.phase3[i]];
        const avg = values.reduce((sum, val) => sum + val, 0) / 3;
        const maxDev = Math.max(...values.map(v => Math.abs(v - avg)));
        const imbalancePercent = (maxDev / avg) * 100;
        imbalances.push(imbalancePercent);
    }
    
    // Calculate statistics for imbalance
    const imbalanceStats = calculateStatistics(imbalances);
    
    // Determine if imbalance is a significant issue
    const detected = imbalanceStats.mean > threshold;
    
    return {
        detected,
        threshold,
        statistics: {
            mean: imbalanceStats.mean.toFixed(2),
            max: imbalanceStats.max.toFixed(2),
            percentageAboveThreshold: (imbalances.filter(i => i > threshold).length / imbalances.length * 100).toFixed(2)
        },
        severity: detected ? (
            imbalanceStats.mean > threshold * 2 ? 'high' : 'medium'
        ) : 'low'
    };
}

/**
 * Detect voltage fluctuations
 * @param {Object} voltageData - Object with phase voltage arrays
 * @returns {Object} Voltage fluctuation analysis
 */
function detectVoltageFluctuations(voltageData) {
    const threshold = analysisConfig.voltage.fluctuationThreshold;
    
    // Calculate rate of change for each phase
    const calculateRateOfChange = (values) => {
        const changes = [];
        for (let i = 1; i < values.length; i++) {
            const change = Math.abs(values[i] - values[i-1]);
            const percentChange = (change / values[i-1]) * 100;
            changes.push(percentChange);
        }
        return changes;
    };
    
    const phase1Changes = calculateRateOfChange(voltageData.phase1);
    const phase2Changes = calculateRateOfChange(voltageData.phase2);
    const phase3Changes = calculateRateOfChange(voltageData.phase3);
    
    // Calculate statistics for rate of change
    const phase1Stats = calculateStatistics(phase1Changes);
    const phase2Stats = calculateStatistics(phase2Changes);
    const phase3Stats = calculateStatistics(phase3Changes);
    
    // Determine if fluctuations are a significant issue
    const phase1Fluctuating = phase1Stats.p95 > threshold;
    const phase2Fluctuating = phase2Stats.p95 > threshold;
    const phase3Fluctuating = phase3Stats.p95 > threshold;
    
    const detected = phase1Fluctuating || phase2Fluctuating || phase3Fluctuating;
    
    return {
        detected,
        threshold,
        phases: {
            phase1: {
                fluctuating: phase1Fluctuating,
                maxChange: phase1Stats.max.toFixed(2),
                p95Change: phase1Stats.p95.toFixed(2)
            },
            phase2: {
                fluctuating: phase2Fluctuating,
                maxChange: phase2Stats.max.toFixed(2),
                p95Change: phase2Stats.p95.toFixed(2)
            },
            phase3: {
                fluctuating: phase3Fluctuating,
                maxChange: phase3Stats.max.toFixed(2),
                p95Change: phase3Stats.p95.toFixed(2)
            }
        },
        severity: detected ? (
            Math.max(phase1Stats.p95, phase2Stats.p95, phase3Stats.p95) > threshold * 2 ? 'high' : 'medium'
        ) : 'low'
    };
}

/**
 * Detect current imbalance between phases
 * @param {Object} currentData - Object with phase current arrays
 * @param {Object} stats - Statistics for each phase
 * @returns {Object} Current imbalance analysis
 */
function detectCurrentImbalance(currentData, stats) {
    const threshold = analysisConfig.current.imbalanceThreshold;
    
    // Calculate imbalance for each data point
    const imbalances = [];
    for (let i = 0; i < currentData.phase1.length; i++) {
        const values = [currentData.phase1[i], currentData.phase2[i], currentData.phase3[i]];
        const avg = values.reduce((sum, val) => sum + val, 0) / 3;
        const maxDev = Math.max(...values.map(v => Math.abs(v - avg)));
        const imbalancePercent = (maxDev / avg) * 100;
        imbalances.push(imbalancePercent);
    }
    
    // Calculate statistics for imbalance
    const imbalanceStats = calculateStatistics(imbalances);
    
    // Determine if imbalance is a significant issue
    const detected = imbalanceStats.mean > threshold;
    
    return {
        detected,
        threshold,
        statistics: {
            mean: imbalanceStats.mean.toFixed(2),
            max: imbalanceStats.max.toFixed(2),
            percentageAboveThreshold: (imbalances.filter(i => i > threshold).length / imbalances.length * 100).toFixed(2)
        },
        severity: detected ? (
            imbalanceStats.mean > threshold * 1.5 ? 'high' : 'medium'
        ) : 'low'
    };
}

// Export helper functions to global scope
window.calculateStatistics = calculateStatistics;
window.detectHighVoltage = detectHighVoltage;
window.detectLowVoltage = detectLowVoltage;
window.detectVoltageImbalance = detectVoltageImbalance;
window.detectVoltageFluctuations = detectVoltageFluctuations;
window.detectCurrentImbalance = detectCurrentImbalance;
