/**
 * Print styles for the dashboard
 * These styles are only applied when printing
 */

@media print {
  /* General page setup */
  @page {
    size: A4 landscape;
    margin: 0.5cm;
  }
  
  html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: white !important;
    font-size: 10pt;
    line-height: 1.3;
  }
  
  /* Hide non-printable elements */
  .no-print,
  .sidebar-container,
  button:not(.print-button),
  .notification-container,
  .status-indicator,
  .modal-overlay,
  .action-buttons,
  .control-panel,
  .time-controls,
  .refresh-controls,
  .sidebar-collapsed,
  .sidebar-footer {
    display: none !important;
  }
  
  /* Layout adjustments */
  .app-container {
    display: block !important;
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
  }
  
  .main-content {
    margin-left: 0 !important;
    padding: 0 !important;
    max-width: 100% !important;
    overflow: visible !important;
  }
  
  /* Dashboard grid */
  .dashboard-grid {
    display: block !important;
    height: auto !important;
    overflow: visible !important;
  }
  
  .dashboard-grid > div {
    position: relative !important;
    transform: none !important;
    page-break-inside: avoid;
    margin-bottom: 1cm;
  }
  
  /* Widget styling */
  .widget-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    page-break-inside: avoid;
    break-inside: avoid;
  }
  
  .widget-header {
    background-color: #f5f5f5 !important;
    color: #333 !important;
    border-bottom: 1px solid #ddd !important;
  }
  
  /* Chart styling */
  .chart-container {
    height: 5cm !important;
    page-break-inside: avoid;
    break-inside: avoid;
  }
  
  /* Summary cards */
  .summary-cards {
    display: flex !important;
    flex-wrap: wrap !important;
    page-break-inside: avoid;
    break-inside: avoid;
  }
  
  .summary-card {
    flex: 0 0 25% !important;
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    page-break-inside: avoid;
    break-inside: avoid;
  }
  
  /* Typography */
  h1, h2, h3, h4, h5, h6 {
    color: #000 !important;
    page-break-after: avoid;
  }
  
  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }
  
  /* Links */
  a {
    color: #000 !important;
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
    font-weight: normal;
  }
  
  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: "";
  }
  
  /* Tables */
  table {
    border-collapse: collapse !important;
    width: 100% !important;
    page-break-inside: avoid;
  }
  
  th, td {
    border: 1px solid #ddd !important;
    padding: 0.2cm !important;
  }
  
  th {
    background-color: #f5f5f5 !important;
  }
  
  /* Add print header and footer */
  .print-header {
    display: block !important;
    text-align: center;
    margin-bottom: 0.5cm;
    border-bottom: 1pt solid #ddd;
    padding-bottom: 0.5cm;
  }
  
  .print-header h1 {
    font-size: 18pt;
    margin: 0;
  }
  
  .print-header p {
    font-size: 10pt;
    color: #666;
    margin: 0.2cm 0 0;
  }
  
  .print-footer {
    display: block !important;
    text-align: center;
    margin-top: 0.5cm;
    border-top: 1pt solid #ddd;
    padding-top: 0.5cm;
    font-size: 8pt;
    color: #666;
  }
  
  /* Print-specific elements */
  .print-only {
    display: block !important;
  }
  
  /* Force background colors to print */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  /* Chart colors */
  .chart-container canvas {
    max-width: 100% !important;
    height: auto !important;
  }
  
  /* Add page breaks where needed */
  .page-break {
    page-break-before: always;
  }
  
  /* Print timestamp */
  .print-timestamp {
    display: block !important;
    text-align: right;
    font-size: 8pt;
    color: #666;
    margin-top: 0.2cm;
  }
}
