{"env": {"browser": true, "es2021": true, "node": true, "jest": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "react-hooks"], "rules": {"react/prop-types": "off", "react/react-in-jsx-scope": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "no-console": ["warn", {"allow": ["warn", "error"]}]}, "settings": {"react": {"version": "detect"}}}