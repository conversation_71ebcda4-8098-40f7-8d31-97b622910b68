/* Streamlined Top Bar Styling */
.streamlined-topbar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-color: transparent;
    padding: 12px 0;
    margin-bottom: 20px;
}

.dark-theme .streamlined-topbar {
    background-color: transparent;
}

.topbar-title {
    display: flex;
    flex-direction: column;
}

.topbar-title h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0056b3;
    margin: 0;
    line-height: 1.2;
}

.dark-theme .topbar-title h1 {
    color: #3b82f6;
}

.topbar-title .subtitle {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 400;
}

.dark-theme .topbar-title .subtitle {
    color: #94a3b8;
}

.topbar-status {
    display: flex;
    align-items: center;
    gap: 16px;
}

.status-box {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
}

.dark-theme .status-box {
    background-color: #0f172a;
    border: 1px solid #334155;
}

.status-box.system {
    background-color: #ecfdf5;
    border-color: #d1fae5;
}

.dark-theme .status-box.system {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
}

.status-box .icon {
    color: #64748b;
    font-size: 1.25rem;
}

.dark-theme .status-box .icon {
    color: #94a3b8;
}

.status-box.system .icon {
    color: #10b981;
}

.status-box .text {
    font-size: 0.875rem;
    font-weight: 500;
    color: #334155;
}

.dark-theme .status-box .text {
    color: #e2e8f0;
}

.status-box .label {
    font-size: 0.75rem;
    color: #64748b;
    display: block;
}

.dark-theme .status-box .label {
    color: #94a3b8;
}

.topbar-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.time-controls {
    display: flex;
    align-items: center;
    background-color: rgba(241, 245, 249, 0.1);
    border-radius: 8px;
    padding: 4px;
    border: 1px solid rgba(226, 232, 240, 0.1);
}

.dark-theme .time-controls {
    background-color: rgba(15, 23, 42, 0.5);
    border-color: rgba(51, 65, 85, 0.5);
}

.time-display {
    padding: 6px 12px;
    font-weight: 500;
    color: #e2e8f0;
    background-color: rgba(15, 23, 42, 0.7);
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    margin: 0 4px;
    min-width: 80px;
    text-align: center;
}

.dark-theme .time-display {
    background-color: rgba(15, 23, 42, 0.7);
    color: #e2e8f0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.nav-button {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: #5ECCE9;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-button:hover {
    background-color: rgba(94, 204, 233, 0.15);
}

.dark-theme .nav-button {
    color: #5ECCE9;
}

.dark-theme .nav-button:hover {
    background-color: rgba(94, 204, 233, 0.15);
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.action-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background-color: rgba(15, 23, 42, 0.5);
    color: #e2e8f0;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.dark-theme .action-button {
    background-color: rgba(15, 23, 42, 0.5);
    color: #e2e8f0;
}

.action-button:hover {
    background-color: rgba(30, 41, 59, 0.8);
}

.dark-theme .action-button:hover {
    background-color: rgba(30, 41, 59, 0.8);
}

.action-button.primary {
    background-color: rgba(94, 204, 233, 0.2);
    color: #5ECCE9;
    border: 1px solid rgba(94, 204, 233, 0.3);
}

.action-button.primary:hover {
    background-color: rgba(94, 204, 233, 0.3);
}

.dark-theme .action-button.primary {
    background-color: rgba(94, 204, 233, 0.2);
    color: #5ECCE9;
    border: 1px solid rgba(94, 204, 233, 0.3);
}

.dark-theme .action-button.primary:hover {
    background-color: rgba(94, 204, 233, 0.3);
}

.action-button.secondary {
    background-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.action-button.secondary:hover {
    background-color: rgba(16, 185, 129, 0.3);
}

.dark-theme .action-button.secondary {
    background-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.dark-theme .action-button.secondary:hover {
    background-color: rgba(16, 185, 129, 0.3);
}

.action-button .icon {
    font-size: 1.25rem;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .action-button span:not(.icon) {
        display: none;
    }

    .action-button {
        padding: 8px;
    }

    .status-box .label {
        display: none;
    }
}

@media (max-width: 768px) {
    .streamlined-topbar {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .topbar-controls {
        width: 100%;
        justify-content: space-between;
    }

    .topbar-status {
        width: 100%;
        justify-content: space-between;
    }
}
