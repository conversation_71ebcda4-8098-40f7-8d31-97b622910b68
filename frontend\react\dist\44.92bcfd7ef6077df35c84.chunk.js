"use strict";(self.webpackChunkpower_monitor_pro_dashboard=self.webpackChunkpower_monitor_pro_dashboard||[]).push([[44],{44:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  "default": () => (/* binding */ modals_ExportModal)\n});\n\n// EXTERNAL MODULE: ./node_modules/react/index.js\nvar react = __webpack_require__(540);\nvar react_default = /*#__PURE__*/__webpack_require__.n(react);\n// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 1 modules\nvar styled_components_browser_esm = __webpack_require__(523);\n// EXTERNAL MODULE: ./src/components/modals/Modal.js\nvar Modal = __webpack_require__(515);\n// EXTERNAL MODULE: ./node_modules/axios/lib/axios.js + 48 modules\nvar axios = __webpack_require__(83);\n;// ./src/utils/exportUtils.js\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i["return"]) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a \'" + o + "\' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }\n/**\n * Utility functions for data export\n */\n\n/**\n * Convert data to CSV format\n * @param {Array} data - Array of data objects\n * @param {Array} headers - Array of header objects with key and label properties\n * @param {string} filename - Filename for the CSV file\n * @returns {string} - CSV string\n */\nvar convertToCSV = function convertToCSV(data, headers) {\n  if (!data || !data.length) return \'\';\n\n  // Create header row\n  var headerRow = headers.map(function (header) {\n    return "\\"".concat(header.label, "\\"");\n  }).join(\',\');\n\n  // Create data rows\n  var dataRows = data.map(function (item) {\n    return headers.map(function (header) {\n      var value = item[header.key];\n\n      // Handle different value types\n      if (value === null || value === undefined) {\n        return \'""\';\n      } else if (typeof value === \'string\') {\n        // Escape quotes in strings\n        return "\\"".concat(value.replace(/"/g, \'""\'), "\\"");\n      } else if (value instanceof Date) {\n        return "\\"".concat(value.toISOString(), "\\"");\n      } else {\n        return "\\"".concat(value, "\\"");\n      }\n    }).join(\',\');\n  }).join(\'\\n\');\n\n  // Combine header and data rows\n  return "".concat(headerRow, "\\n").concat(dataRows);\n};\n\n/**\n * Download data as CSV file\n * @param {Array} data - Array of data objects\n * @param {Array} headers - Array of header objects with key and label properties\n * @param {string} filename - Filename for the CSV file\n */\nvar downloadCSV = function downloadCSV(data, headers) {\n  var filename = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \'export.csv\';\n  // Convert data to CSV\n  var csv = convertToCSV(data, headers);\n\n  // Create a Blob with the CSV data\n  var blob = new Blob([csv], {\n    type: \'text/csv;charset=utf-8;\'\n  });\n\n  // Create a download link\n  var link = document.createElement(\'a\');\n\n  // Set link properties\n  link.href = URL.createObjectURL(blob);\n  link.download = filename;\n  link.style.display = \'none\';\n\n  // Add link to document\n  document.body.appendChild(link);\n\n  // Click the link to download the file\n  link.click();\n\n  // Clean up\n  document.body.removeChild(link);\n  URL.revokeObjectURL(link.href);\n};\n\n/**\n * Format timestamp for CSV export\n * @param {string} timestamp - Timestamp string\n * @returns {string} - Formatted timestamp\n */\nvar formatTimestampForExport = function formatTimestampForExport(timestamp) {\n  if (!timestamp) return \'\';\n  try {\n    var date = new Date(timestamp);\n    return date.toISOString();\n  } catch (error) {\n    console.error(\'Error formatting timestamp:\', error);\n    return timestamp;\n  }\n};\n\n/**\n * Format number for CSV export\n * @param {number} value - Number value\n * @param {number} decimals - Number of decimal places\n * @returns {string} - Formatted number\n */\nvar formatNumberForExport = function formatNumberForExport(value) {\n  var decimals = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  if (value === null || value === undefined) return \'\';\n  try {\n    return Number(value).toFixed(decimals);\n  } catch (error) {\n    console.error(\'Error formatting number:\', error);\n    return value;\n  }\n};\n\n/**\n * Generate CSV filename with timestamp\n * @param {string} prefix - Filename prefix\n * @param {Date} startDate - Start date for the data\n * @param {Date} endDate - End date for the data\n * @returns {string} - Filename\n */\nvar generateCSVFilename = function generateCSVFilename() {\n  var prefix = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \'power_data\';\n  var startDate = arguments.length > 1 ? arguments[1] : undefined;\n  var endDate = arguments.length > 2 ? arguments[2] : undefined;\n  var formatDate = function formatDate(date) {\n    if (!date) return \'\';\n    try {\n      return date instanceof Date ? date.toISOString().split(\'T\')[0] : new Date(date).toISOString().split(\'T\')[0];\n    } catch (error) {\n      console.error(\'Error formatting date for filename:\', error);\n      return \'\';\n    }\n  };\n  var startStr = startDate ? "_from_".concat(formatDate(startDate)) : \'\';\n  var endStr = endDate ? "_to_".concat(formatDate(endDate)) : \'\';\n  var timestamp = new Date().toISOString().replace(/[:.]/g, \'-\').split(\'T\')[0];\n  return "".concat(prefix).concat(startStr).concat(endStr, "_").concat(timestamp, ".csv");\n};\n\n/**\n * Fetch all pages of data for export\n * @param {Function} fetchFunction - Function to fetch a page of data\n * @param {number} pageSize - Number of items per page\n * @param {Function} onProgress - Callback for progress updates\n * @returns {Promise<Array>} - All data\n */\nvar fetchAllPagesForExport = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(fetchFunction) {\n    var pageSize,\n      onProgress,\n      allData,\n      page,\n      hasMore,\n      result,\n      _args = arguments,\n      _t;\n    return _regenerator().w(function (_context) {\n      while (1) switch (_context.n) {\n        case 0:\n          pageSize = _args.length > 1 && _args[1] !== undefined ? _args[1] : 1000;\n          onProgress = _args.length > 2 && _args[2] !== undefined ? _args[2] : null;\n          allData = [];\n          page = 1;\n          hasMore = true;\n        case 1:\n          if (!hasMore) {\n            _context.n = 6;\n            break;\n          }\n          _context.p = 2;\n          _context.n = 3;\n          return fetchFunction(page, pageSize);\n        case 3:\n          result = _context.v;\n          // Check if we got data\n          if (result && Array.isArray(result.data) && result.data.length > 0) {\n            // Add data to the collection\n            allData = [].concat(_toConsumableArray(allData), _toConsumableArray(result.data));\n\n            // Update progress\n            if (onProgress) {\n              onProgress({\n                page: page,\n                totalItems: allData.length,\n                hasMore: result.data.length === pageSize\n              });\n            }\n\n            // Check if we have more data\n            hasMore = result.data.length === pageSize;\n            page++;\n          } else {\n            // No more data\n            hasMore = false;\n          }\n          _context.n = 5;\n          break;\n        case 4:\n          _context.p = 4;\n          _t = _context.v;\n          console.error(\'Error fetching data for export:\', _t);\n          throw _t;\n        case 5:\n          _context.n = 1;\n          break;\n        case 6:\n          return _context.a(2, allData);\n      }\n    }, _callee, null, [[2, 4]]);\n  }));\n  return function fetchAllPagesForExport(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\n;// ./src/components/modals/ExportModal.js\nfunction _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }\nvar _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10;\nfunction _taggedTemplateLiteral(e, t) { return t || (t = e.slice(0)), Object.freeze(Object.defineProperties(e, { raw: { value: Object.freeze(t) } })); }\nfunction ExportModal_regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return ExportModal_regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i["return"]) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a \'" + o + "\' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (ExportModal_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, ExportModal_regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, ExportModal_regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), ExportModal_regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", ExportModal_regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), ExportModal_regeneratorDefine2(u), ExportModal_regeneratorDefine2(u, o, "Generator"), ExportModal_regeneratorDefine2(u, n, function () { return this; }), ExportModal_regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (ExportModal_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction ExportModal_regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } ExportModal_regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { ExportModal_regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, ExportModal_regeneratorDefine2(e, r, n, t); }\nfunction ExportModal_asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction ExportModal_asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { ExportModal_asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { ExportModal_asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }\nfunction _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || ExportModal_unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }\nfunction ExportModal_unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return ExportModal_arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? ExportModal_arrayLikeToArray(r, a) : void 0; } }\nfunction ExportModal_arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\n\n\n\n\n\nvar ExportModal = function ExportModal(_ref) {\n  var isOpen = _ref.isOpen,\n    onClose = _ref.onClose,\n    addNotification = _ref.addNotification;\n  var _useState = (0,react.useState)(\'csv\'),\n    _useState2 = _slicedToArray(_useState, 2),\n    exportType = _useState2[0],\n    setExportType = _useState2[1];\n  var _useState3 = (0,react.useState)(\'visible\'),\n    _useState4 = _slicedToArray(_useState3, 2),\n    timeRange = _useState4[0],\n    setTimeRange = _useState4[1];\n  var _useState5 = (0,react.useState)(true),\n    _useState6 = _slicedToArray(_useState5, 2),\n    includeAllParameters = _useState6[0],\n    setIncludeAllParameters = _useState6[1];\n  var _useState7 = (0,react.useState)({\n      startDate: \'\',\n      endDate: \'\'\n    }),\n    _useState8 = _slicedToArray(_useState7, 2),\n    customDateRange = _useState8[0],\n    setCustomDateRange = _useState8[1];\n  var _useState9 = (0,react.useState)(false),\n    _useState0 = _slicedToArray(_useState9, 2),\n    isExporting = _useState0[0],\n    setIsExporting = _useState0[1];\n  var _useState1 = (0,react.useState)({\n      current: 0,\n      total: 0,\n      percentage: 0\n    }),\n    _useState10 = _slicedToArray(_useState1, 2),\n    exportProgress = _useState10[0],\n    setExportProgress = _useState10[1];\n\n  // Reference to cancel export\n  var cancelExportRef = (0,react.useRef)(false);\n\n  // Handle export type change\n  var handleExportTypeChange = function handleExportTypeChange(e) {\n    setExportType(e.target.value);\n  };\n\n  // Handle time range change\n  var handleTimeRangeChange = function handleTimeRangeChange(e) {\n    setTimeRange(e.target.value);\n  };\n\n  // Handle include all parameters change\n  var handleIncludeAllParametersChange = function handleIncludeAllParametersChange(e) {\n    setIncludeAllParameters(e.target.checked);\n  };\n\n  // Handle custom date range change\n  var handleCustomDateRangeChange = function handleCustomDateRangeChange(e) {\n    var _e$target = e.target,\n      name = _e$target.name,\n      value = _e$target.value;\n    setCustomDateRange(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, _defineProperty({}, name, value));\n    });\n  };\n\n  // Handle export\n  var handleExport = /*#__PURE__*/function () {\n    var _ref2 = ExportModal_asyncToGenerator(/*#__PURE__*/ExportModal_regenerator().m(function _callee2() {\n      var startDate, endDate, startDateStr, endDateStr, fetchPage, onProgress, pageSize, allData, _t, _t2, _t3;\n      return ExportModal_regenerator().w(function (_context2) {\n        while (1) switch (_context2.n) {\n          case 0:\n            // Reset cancel flag\n            cancelExportRef.current = false;\n\n            // Show loading notification and set exporting state\n            addNotification(\'Preparing data for export...\', \'info\');\n            setIsExporting(true);\n            setExportProgress({\n              current: 0,\n              total: 0,\n              percentage: 0\n            });\n            _context2.p = 1;\n            _t = timeRange;\n            _context2.n = _t === \'visible\' ? 2 : _t === \'hour\' ? 3 : _t === \'day\' ? 4 : _t === \'week\' ? 5 : _t === \'custom\' ? 6 : 7;\n            break;\n          case 2:\n            // Use currently visible data range (fallback to last hour)\n            endDate = new Date();\n            startDate = new Date(endDate.getTime() - 60 * 60 * 1000); // 1 hour ago\n            return _context2.a(3, 8);\n          case 3:\n            endDate = new Date();\n            startDate = new Date(endDate.getTime() - 60 * 60 * 1000); // 1 hour ago\n            return _context2.a(3, 8);\n          case 4:\n            endDate = new Date();\n            startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago\n            return _context2.a(3, 8);\n          case 5:\n            endDate = new Date();\n            startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days ago\n            return _context2.a(3, 8);\n          case 6:\n            startDate = new Date(customDateRange.startDate);\n            endDate = new Date(customDateRange.endDate);\n            return _context2.a(3, 8);\n          case 7:\n            endDate = new Date();\n            startDate = new Date(endDate.getTime() - 60 * 60 * 1000);\n          case 8:\n            // Format dates for API\n            startDateStr = startDate.toISOString();\n            endDateStr = endDate.toISOString(); // Define fetch function for pagination\n            fetchPage = /*#__PURE__*/function () {\n              var _ref3 = ExportModal_asyncToGenerator(/*#__PURE__*/ExportModal_regenerator().m(function _callee(page, pageSize) {\n                var response;\n                return ExportModal_regenerator().w(function (_context) {\n                  while (1) switch (_context.n) {\n                    case 0:\n                      if (!cancelExportRef.current) {\n                        _context.n = 1;\n                        break;\n                      }\n                      throw new Error(\'Export cancelled\');\n                    case 1:\n                      // Update progress\n                      setExportProgress(function (prev) {\n                        return _objectSpread(_objectSpread({}, prev), {}, {\n                          current: (page - 1) * pageSize\n                        });\n                      });\n\n                      // Fetch data for this page\n                      _context.n = 2;\n                      return axios/* default */.A.get("../backend/get_historical_data.php?start=".concat(startDateStr, "&end=").concat(endDateStr, "&page=").concat(page, "&limit=").concat(pageSize));\n                    case 2:\n                      response = _context.v;\n                      return _context.a(2, {\n                        data: response.data,\n                        page: page,\n                        pageSize: pageSize\n                      });\n                  }\n                }, _callee);\n              }));\n              return function fetchPage(_x, _x2) {\n                return _ref3.apply(this, arguments);\n              };\n            }(); // Progress callback\n            onProgress = function onProgress(progress) {\n              // Calculate percentage\n              var percentage = progress.totalItems > 0 ? Math.round(progress.totalItems / (progress.totalItems + (progress.hasMore ? pageSize : 0)) * 100) : 0;\n\n              // Update progress state\n              setExportProgress({\n                current: progress.totalItems,\n                total: progress.totalItems + (progress.hasMore ? pageSize : 0),\n                percentage: percentage\n              });\n            }; // Fetch all pages of data\n            pageSize = 1000; // Fetch 1000 records at a time\n            _context2.n = 9;\n            return fetchAllPagesForExport(function (page, limit) {\n              return fetchPage(page, limit);\n            }, pageSize, onProgress);\n          case 9:\n            allData = _context2.v;\n            if (!cancelExportRef.current) {\n              _context2.n = 10;\n              break;\n            }\n            throw new Error(\'Export cancelled\');\n          case 10:\n            if (!(!allData || allData.length === 0)) {\n              _context2.n = 11;\n              break;\n            }\n            addNotification(\'No data found for the selected time range\', \'warning\');\n            setIsExporting(false);\n            return _context2.a(2);\n          case 11:\n            // Update progress to 100%\n            setExportProgress({\n              current: allData.length,\n              total: allData.length,\n              percentage: 100\n            });\n\n            // Process data based on export type\n            _t2 = exportType;\n            _context2.n = _t2 === \'csv\' ? 12 : _t2 === \'json\' ? 14 : _t2 === \'pdf\' ? 16 : 17;\n            break;\n          case 12:\n            _context2.n = 13;\n            return exportAsCSV(allData, includeAllParameters, startDate, endDate);\n          case 13:\n            return _context2.a(3, 18);\n          case 14:\n            _context2.n = 15;\n            return exportAsJSON(allData, includeAllParameters, startDate, endDate);\n          case 15:\n            return _context2.a(3, 18);\n          case 16:\n            addNotification(\'PDF export is not yet implemented\', \'warning\');\n            return _context2.a(3, 18);\n          case 17:\n            _context2.n = 18;\n            return exportAsCSV(allData, includeAllParameters, startDate, endDate);\n          case 18:\n            // Reset exporting state\n            setIsExporting(false);\n\n            // Close modal\n            onClose();\n            _context2.n = 20;\n            break;\n          case 19:\n            _context2.p = 19;\n            _t3 = _context2.v;\n            console.error(\'Error exporting data:\', _t3);\n\n            // Show error notification only if not cancelled\n            if (_t3.message !== \'Export cancelled\') {\n              addNotification(\'Failed to export data: \' + _t3.message, \'error\');\n            }\n\n            // Reset exporting state\n            setIsExporting(false);\n          case 20:\n            return _context2.a(2);\n        }\n      }, _callee2, null, [[1, 19]]);\n    }));\n    return function handleExport() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n\n  // Cancel export\n  var handleCancelExport = function handleCancelExport() {\n    cancelExportRef.current = true;\n    addNotification(\'Export cancelled\', \'info\');\n  };\n\n  // Export as CSV\n  var exportAsCSV = /*#__PURE__*/function () {\n    var _ref4 = ExportModal_asyncToGenerator(/*#__PURE__*/ExportModal_regenerator().m(function _callee3(data, includeAllParameters, startDate, endDate) {\n      var headers, formattedData, filename;\n      return ExportModal_regenerator().w(function (_context3) {\n        while (1) switch (_context3.n) {\n          case 0:\n            if (!(!data || !Array.isArray(data))) {\n              _context3.n = 1;\n              break;\n            }\n            addNotification(\'No data to export\', \'warning\');\n            return _context3.a(2);\n          case 1:\n            // Define headers based on includeAllParameters\n            headers = [];\n            if (includeAllParameters) {\n              headers = [{\n                key: \'timestamp\',\n                label: \'Timestamp\'\n              }, {\n                key: \'voltage_1\',\n                label: \'Voltage Phase 1 (V)\'\n              }, {\n                key: \'voltage_2\',\n                label: \'Voltage Phase 2 (V)\'\n              }, {\n                key: \'voltage_3\',\n                label: \'Voltage Phase 3 (V)\'\n              }, {\n                key: \'current_1\',\n                label: \'Current Phase 1 (A)\'\n              }, {\n                key: \'current_2\',\n                label: \'Current Phase 2 (A)\'\n              }, {\n                key: \'current_3\',\n                label: \'Current Phase 3 (A)\'\n              }, {\n                key: \'pf_1\',\n                label: \'Power Factor Phase 1\'\n              }, {\n                key: \'pf_2\',\n                label: \'Power Factor Phase 2\'\n              }, {\n                key: \'pf_3\',\n                label: \'Power Factor Phase 3\'\n              }, {\n                key: \'kva_1\',\n                label: \'KVA Phase 1\'\n              }, {\n                key: \'kva_2\',\n                label: \'KVA Phase 2\'\n              }, {\n                key: \'kva_3\',\n                label: \'KVA Phase 3\'\n              }, {\n                key: \'total_kw\',\n                label: \'Total KW\'\n              }, {\n                key: \'total_kva\',\n                label: \'Total KVA\'\n              }, {\n                key: \'total_kvar\',\n                label: \'Total KVAR\'\n              }, {\n                key: \'frequency\',\n                label: \'Frequency (Hz)\'\n              }];\n            } else {\n              // Only include essential parameters\n              headers = [{\n                key: \'timestamp\',\n                label: \'Timestamp\'\n              }, {\n                key: \'voltage_avg\',\n                label: \'Average Voltage (V)\'\n              }, {\n                key: \'current_total\',\n                label: \'Total Current (A)\'\n              }, {\n                key: \'pf_avg\',\n                label: \'Average Power Factor\'\n              }, {\n                key: \'total_kw\',\n                label: \'Total KW\'\n              }, {\n                key: \'total_kva\',\n                label: \'Total KVA\'\n              }, {\n                key: \'frequency\',\n                label: \'Frequency (Hz)\'\n              }];\n\n              // Calculate derived values\n              data = data.map(function (item) {\n                var voltageAvg = ((parseFloat(item.voltage_1) + parseFloat(item.voltage_2) + parseFloat(item.voltage_3)) / 3).toFixed(3);\n                var currentTotal = (parseFloat(item.current_1) + parseFloat(item.current_2) + parseFloat(item.current_3)).toFixed(3);\n                var pfAvg = ((parseFloat(item.pf_1) + parseFloat(item.pf_2) + parseFloat(item.pf_3)) / 3).toFixed(3);\n                return _objectSpread(_objectSpread({}, item), {}, {\n                  voltage_avg: voltageAvg,\n                  current_total: currentTotal,\n                  pf_avg: pfAvg\n                });\n              });\n            }\n\n            // Format data for CSV\n            formattedData = data.map(function (item) {\n              var formattedItem = _objectSpread({}, item);\n\n              // Format timestamp\n              formattedItem.timestamp = formatTimestampForExport(item.timestamp);\n\n              // Format numeric values with 3 decimal places\n              headers.forEach(function (header) {\n                if (header.key !== \'timestamp\' && formattedItem[header.key] !== undefined) {\n                  formattedItem[header.key] = formatNumberForExport(formattedItem[header.key], 3);\n                }\n              });\n              return formattedItem;\n            }); // Generate filename\n            filename = generateCSVFilename(\'power_data\', startDate, endDate); // Download CSV\n            downloadCSV(formattedData, headers, filename);\n\n            // Show success notification\n            addNotification("Data exported successfully as CSV (".concat(data.length, " records)"), \'success\');\n          case 2:\n            return _context3.a(2);\n        }\n      }, _callee3);\n    }));\n    return function exportAsCSV(_x3, _x4, _x5, _x6) {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n\n  // Export as JSON\n  var exportAsJSON = /*#__PURE__*/function () {\n    var _ref5 = ExportModal_asyncToGenerator(/*#__PURE__*/ExportModal_regenerator().m(function _callee4(data, includeAllParameters, startDate, endDate) {\n      var exportData, jsonExport, jsonContent, filename, blob, url, link;\n      return ExportModal_regenerator().w(function (_context4) {\n        while (1) switch (_context4.n) {\n          case 0:\n            if (!(!data || !Array.isArray(data))) {\n              _context4.n = 1;\n              break;\n            }\n            addNotification(\'No data to export\', \'warning\');\n            return _context4.a(2);\n          case 1:\n            if (includeAllParameters) {\n              // Export all data as is\n              exportData = data;\n            } else {\n              // Only include essential parameters with calculated values\n              exportData = data.map(function (item) {\n                var voltageAvg = ((parseFloat(item.voltage_1) + parseFloat(item.voltage_2) + parseFloat(item.voltage_3)) / 3).toFixed(3);\n                var currentTotal = (parseFloat(item.current_1) + parseFloat(item.current_2) + parseFloat(item.current_3)).toFixed(3);\n                var pfAvg = ((parseFloat(item.pf_1) + parseFloat(item.pf_2) + parseFloat(item.pf_3)) / 3).toFixed(3);\n                return {\n                  timestamp: formatTimestampForExport(item.timestamp),\n                  voltage_avg: parseFloat(voltageAvg),\n                  current_total: parseFloat(currentTotal),\n                  pf_avg: parseFloat(pfAvg),\n                  total_kw: parseFloat(formatNumberForExport(item.total_kw, 3)),\n                  total_kva: parseFloat(formatNumberForExport(item.total_kva, 3)),\n                  frequency: parseFloat(formatNumberForExport(item.frequency, 3))\n                };\n              });\n            }\n\n            // Add metadata\n            jsonExport = {\n              metadata: {\n                exportDate: new Date().toISOString(),\n                startDate: startDate.toISOString(),\n                endDate: endDate.toISOString(),\n                recordCount: data.length,\n                exportType: includeAllParameters ? \'full\' : \'summary\'\n              },\n              data: exportData\n            }; // Create JSON content\n            jsonContent = JSON.stringify(jsonExport, null, 2); // Generate filename\n            filename = "power_data_".concat(startDate.toISOString().split(\'T\')[0], "_to_").concat(endDate.toISOString().split(\'T\')[0], ".json"); // Create download link\n            blob = new Blob([jsonContent], {\n              type: \'application/json;charset=utf-8;\'\n            });\n            url = URL.createObjectURL(blob);\n            link = document.createElement(\'a\');\n            link.setAttribute(\'href\', url);\n            link.setAttribute(\'download\', filename);\n            link.style.visibility = \'hidden\';\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n\n            // Show success notification\n            addNotification("Data exported successfully as JSON (".concat(data.length, " records)"), \'success\');\n          case 2:\n            return _context4.a(2);\n        }\n      }, _callee4);\n    }));\n    return function exportAsJSON(_x7, _x8, _x9, _x0) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  return /*#__PURE__*/react_default().createElement(Modal/* default */.A, {\n    isOpen: isOpen,\n    onClose: onClose,\n    title: "Export Data"\n  }, /*#__PURE__*/react_default().createElement(ExportOptions, null, /*#__PURE__*/react_default().createElement(ExportOption, null, /*#__PURE__*/react_default().createElement("input", {\n    type: "radio",\n    name: "exportType",\n    id: "exportCSV",\n    value: "csv",\n    checked: exportType === \'csv\',\n    onChange: handleExportTypeChange\n  }), /*#__PURE__*/react_default().createElement("label", {\n    htmlFor: "exportCSV"\n  }, /*#__PURE__*/react_default().createElement("span", {\n    className: "material-icons-round"\n  }, "description"), /*#__PURE__*/react_default().createElement("div", null, /*#__PURE__*/react_default().createElement("h4", null, "CSV File"), /*#__PURE__*/react_default().createElement("p", null, "Export data as comma-separated values")))), /*#__PURE__*/react_default().createElement(ExportOption, null, /*#__PURE__*/react_default().createElement("input", {\n    type: "radio",\n    name: "exportType",\n    id: "exportJSON",\n    value: "json",\n    checked: exportType === \'json\',\n    onChange: handleExportTypeChange\n  }), /*#__PURE__*/react_default().createElement("label", {\n    htmlFor: "exportJSON"\n  }, /*#__PURE__*/react_default().createElement("span", {\n    className: "material-icons-round"\n  }, "data_object"), /*#__PURE__*/react_default().createElement("div", null, /*#__PURE__*/react_default().createElement("h4", null, "JSON File"), /*#__PURE__*/react_default().createElement("p", null, "Export data in JSON format")))), /*#__PURE__*/react_default().createElement(ExportOption, null, /*#__PURE__*/react_default().createElement("input", {\n    type: "radio",\n    name: "exportType",\n    id: "exportPDF",\n    value: "pdf",\n    checked: exportType === \'pdf\',\n    onChange: handleExportTypeChange\n  }), /*#__PURE__*/react_default().createElement("label", {\n    htmlFor: "exportPDF"\n  }, /*#__PURE__*/react_default().createElement("span", {\n    className: "material-icons-round"\n  }, "picture_as_pdf"), /*#__PURE__*/react_default().createElement("div", null, /*#__PURE__*/react_default().createElement("h4", null, "PDF Report"), /*#__PURE__*/react_default().createElement("p", null, "Export data as a PDF report"))))), /*#__PURE__*/react_default().createElement(ExportSettings, null, /*#__PURE__*/react_default().createElement("h3", null, "Export Settings"), /*#__PURE__*/react_default().createElement(SettingItem, null, /*#__PURE__*/react_default().createElement("label", {\n    htmlFor: "exportTimeRange"\n  }, "Time Range"), /*#__PURE__*/react_default().createElement("select", {\n    id: "exportTimeRange",\n    value: timeRange,\n    onChange: handleTimeRangeChange\n  }, /*#__PURE__*/react_default().createElement("option", {\n    value: "visible"\n  }, "Currently Visible Data"), /*#__PURE__*/react_default().createElement("option", {\n    value: "hour"\n  }, "Last Hour"), /*#__PURE__*/react_default().createElement("option", {\n    value: "day"\n  }, "Last 24 Hours"), /*#__PURE__*/react_default().createElement("option", {\n    value: "week"\n  }, "Last Week"), /*#__PURE__*/react_default().createElement("option", {\n    value: "custom"\n  }, "Custom Range"))), timeRange === \'custom\' && /*#__PURE__*/react_default().createElement(DateRange, null, /*#__PURE__*/react_default().createElement(SettingItem, null, /*#__PURE__*/react_default().createElement("label", {\n    htmlFor: "startDate"\n  }, "Start Date"), /*#__PURE__*/react_default().createElement("input", {\n    type: "datetime-local",\n    id: "startDate",\n    name: "startDate",\n    value: customDateRange.startDate,\n    onChange: handleCustomDateRangeChange\n  })), /*#__PURE__*/react_default().createElement(SettingItem, null, /*#__PURE__*/react_default().createElement("label", {\n    htmlFor: "endDate"\n  }, "End Date"), /*#__PURE__*/react_default().createElement("input", {\n    type: "datetime-local",\n    id: "endDate",\n    name: "endDate",\n    value: customDateRange.endDate,\n    onChange: handleCustomDateRangeChange\n  }))), /*#__PURE__*/react_default().createElement(SettingItem, {\n    className: "checkbox"\n  }, /*#__PURE__*/react_default().createElement("input", {\n    type: "checkbox",\n    id: "includeAllParameters",\n    checked: includeAllParameters,\n    onChange: handleIncludeAllParametersChange\n  }), /*#__PURE__*/react_default().createElement("label", {\n    htmlFor: "includeAllParameters"\n  }, "Include All Parameters"))), isExporting ? /*#__PURE__*/react_default().createElement(ExportProgress, null, /*#__PURE__*/react_default().createElement(ProgressTitle, null, "Exporting Data..."), /*#__PURE__*/react_default().createElement(ProgressBar, null, /*#__PURE__*/react_default().createElement(ProgressFill, {\n    style: {\n      width: "".concat(exportProgress.percentage, "%")\n    }\n  })), /*#__PURE__*/react_default().createElement(ProgressDetails, null, exportProgress.current, " / ", exportProgress.total || \'?\', " records (", exportProgress.percentage, "%)"), /*#__PURE__*/react_default().createElement(ModalButton, {\n    className: "secondary",\n    onClick: handleCancelExport\n  }, "Cancel Export")) : /*#__PURE__*/react_default().createElement(ModalFooter, null, /*#__PURE__*/react_default().createElement(ModalButton, {\n    className: "secondary",\n    onClick: onClose\n  }, "Cancel"), /*#__PURE__*/react_default().createElement(ModalButton, {\n    className: "primary",\n    onClick: handleExport\n  }, "Export Data")));\n};\nvar ExportOptions = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = _taggedTemplateLiteral(["\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n\\n  @media (max-width: 576px) {\\n    grid-template-columns: 1fr;\\n  }\\n"])));\nvar ExportOption = styled_components_browser_esm/* default */.Ay.div(_templateObject2 || (_templateObject2 = _taggedTemplateLiteral(["\\n  position: relative;\\n\\n  input[type=\\"radio\\"] {\\n    position: absolute;\\n    opacity: 0;\\n    width: 0;\\n    height: 0;\\n  }\\n\\n  label {\\n    display: flex;\\n    align-items: center;\\n    gap: 1rem;\\n    padding: 1.25rem;\\n    border: 1px solid var(--border-color);\\n    border-radius: var(--border-radius);\\n    cursor: pointer;\\n    transition: all var(--transition-speed) ease;\\n    box-shadow: var(--shadow-sm);\\n    height: 100%;\\n\\n    &:hover {\\n      border-color: var(--primary-light);\\n      background-color: var(--hover-color);\\n      transform: translateY(-2px);\\n      box-shadow: var(--shadow-md);\\n    }\\n\\n    span.material-icons-round {\\n      font-size: 1.5rem;\\n      color: var(--primary-color);\\n      width: 2.5rem;\\n      height: 2.5rem;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      border-radius: 50%;\\n      background-color: rgba(0, 86, 179, 0.1);\\n    }\\n\\n    h4 {\\n      font-size: 0.9375rem;\\n      font-weight: var(--font-weight-semibold);\\n      color: var(--text-color);\\n      margin-bottom: 0.25rem;\\n    }\\n\\n    p {\\n      font-size: 0.8125rem;\\n      color: var(--text-secondary);\\n    }\\n  }\\n\\n  input[type=\\"radio\\"]:checked + label {\\n    border-color: var(--primary-color);\\n    background-color: rgba(0, 86, 179, 0.05);\\n    box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.15);\\n  }\\n"])));\nvar ExportSettings = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = _taggedTemplateLiteral(["\\n  margin-bottom: 1.5rem;\\n\\n  h3 {\\n    font-size: 1.125rem;\\n    font-weight: var(--font-weight-semibold);\\n    color: var(--text-color);\\n    margin-bottom: 1.25rem;\\n    padding-bottom: 0.75rem;\\n    border-bottom: 1px solid var(--border-color);\\n    letter-spacing: -0.01em;\\n  }\\n"])));\nvar SettingItem = styled_components_browser_esm/* default */.Ay.div(_templateObject4 || (_templateObject4 = _taggedTemplateLiteral(["\\n  margin-bottom: 1.25rem;\\n\\n  label {\\n    display: block;\\n    font-size: 0.9375rem;\\n    font-weight: var(--font-weight-medium);\\n    color: var(--text-color);\\n    margin-bottom: 0.5rem;\\n  }\\n\\n  input[type=\\"datetime-local\\"],\\n  select {\\n    width: 100%;\\n    padding: 0.75rem 1rem;\\n    border: 1px solid var(--border-color);\\n    border-radius: var(--border-radius);\\n    font-size: 0.9375rem;\\n    color: var(--text-color);\\n    background-color: var(--card-color);\\n    transition: all var(--transition-speed) ease;\\n    box-shadow: var(--shadow-sm);\\n\\n    &:focus {\\n      border-color: var(--primary-color);\\n      box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.15);\\n      outline: none;\\n    }\\n  }\\n\\n  &.checkbox {\\n    display: flex;\\n    align-items: center;\\n    gap: 0.75rem;\\n\\n    input {\\n      width: 1.125rem;\\n      height: 1.125rem;\\n      accent-color: var(--primary-color);\\n    }\\n\\n    label {\\n      margin-bottom: 0;\\n      cursor: pointer;\\n    }\\n  }\\n"])));\nvar DateRange = styled_components_browser_esm/* default */.Ay.div(_templateObject5 || (_templateObject5 = _taggedTemplateLiteral(["\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1rem;\\n\\n  @media (max-width: 576px) {\\n    grid-template-columns: 1fr;\\n  }\\n"])));\nvar ModalFooter = styled_components_browser_esm/* default */.Ay.div(_templateObject6 || (_templateObject6 = _taggedTemplateLiteral(["\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1rem;\\n"])));\nvar ModalButton = styled_components_browser_esm/* default */.Ay.button(_templateObject7 || (_templateObject7 = _taggedTemplateLiteral(["\\n  padding: 0.625rem 1.25rem;\\n  border: 1px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  cursor: pointer;\\n  font-weight: var(--font-weight-medium);\\n  font-size: 0.875rem;\\n  transition: all var(--transition-speed) ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n\\n  &.primary {\\n    background: var(--gradient-primary);\\n    color: white;\\n    border: none;\\n\\n    &:hover {\\n      box-shadow: var(--shadow-md);\\n      transform: translateY(-1px);\\n    }\\n  }\\n\\n  &.secondary {\\n    background-color: var(--card-color);\\n    color: var(--text-color);\\n\\n    &:hover {\\n      background-color: var(--hover-color);\\n      border-color: var(--primary-light);\\n    }\\n  }\\n"])));\nvar ExportProgress = styled_components_browser_esm/* default */.Ay.div(_templateObject8 || (_templateObject8 = _taggedTemplateLiteral(["\\n  margin-top: 1.5rem;\\n  padding: 1.25rem;\\n  background-color: var(--card-color);\\n  border-radius: var(--border-radius);\\n  border: 1px solid var(--border-color);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n"])));\nvar ProgressTitle = styled_components_browser_esm/* default */.Ay.h4(_templateObject9 || (_templateObject9 = _taggedTemplateLiteral(["\\n  font-size: 1rem;\\n  font-weight: var(--font-weight-semibold);\\n  color: var(--text-color);\\n  margin-bottom: 1rem;\\n"])));\nvar ProgressBar = styled_components_browser_esm/* default */.Ay.div(_templateObject0 || (_templateObject0 = _taggedTemplateLiteral(["\\n  width: 100%;\\n  height: 0.5rem;\\n  background-color: var(--hover-color);\\n  border-radius: 9999px;\\n  overflow: hidden;\\n  margin-bottom: 0.5rem;\\n"])));\nvar ProgressFill = styled_components_browser_esm/* default */.Ay.div(_templateObject1 || (_templateObject1 = _taggedTemplateLiteral(["\\n  height: 100%;\\n  background-color: var(--primary-color);\\n  border-radius: 9999px;\\n  transition: width 0.3s ease;\\n"])));\nvar ProgressDetails = styled_components_browser_esm/* default */.Ay.div(_templateObject10 || (_templateObject10 = _taggedTemplateLiteral(["\\n  font-size: 0.875rem;\\n  color: var(--text-secondary);\\n  margin-bottom: 1.25rem;\\n"])));\n/* harmony default export */ const modals_ExportModal = (ExportModal);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///44\n')}}]);