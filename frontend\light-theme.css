/**
 * Light Theme for Online Data Logger System
 * Professional light mode styling
 */

:root {
    /* Light theme colors */
    --light-bg: #f8fafc;
    --light-card-bg: #ffffff;
    --light-text: #1e293b;
    --light-text-secondary: #64748b;
    --light-border: #e2e8f0;
    --light-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --light-hover: #f1f5f9;
    
    /* Accent colors */
    --accent-primary: #0ea5e9;
    --accent-primary-hover: #0284c7;
    --accent-secondary: #0f766e;
    --accent-secondary-hover: #0e7490;
    --accent-success: #10b981;
    --accent-warning: #f59e0b;
    --accent-danger: #ef4444;
    --accent-info: #3b82f6;
    
    /* Status colors */
    --status-online: #10b981;
    --status-warning: #f59e0b;
    --status-danger: #ef4444;
    --status-offline: #94a3b8;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--light-bg);
    color: var(--light-text);
    margin: 0;
    padding: 0;
    line-height: 1.5;
}

/* Dashboard container */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--light-card-bg);
    border-right: 1px solid var(--light-border);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 100;
    box-shadow: var(--light-shadow);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--light-border);
    display: flex;
    justify-content: center;
    align-items: center;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--light-text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
    background-color: var(--light-hover);
    color: var(--accent-primary);
}

.sidebar-nav li.active a {
    background-color: rgba(14, 165, 233, 0.1);
    color: var(--accent-primary);
    border-left-color: var(--accent-primary);
}

.sidebar-nav .material-icons-round {
    margin-right: 12px;
    font-size: 20px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--light-border);
}

.system-status {
    display: flex;
    align-items: center;
    margin: 15px 0;
    color: var(--light-text-secondary);
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.online {
    background-color: var(--status-online);
}

.status-indicator.warning {
    background-color: var(--status-warning);
}

.status-indicator.danger {
    background-color: var(--status-danger);
}

.status-indicator.offline {
    background-color: var(--status-offline);
}

.logout-button {
    display: flex;
    align-items: center;
    padding: 10px;
    color: var(--light-text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.logout-button:hover {
    background-color: var(--light-hover);
    color: var(--accent-danger);
}

.logout-button .material-icons-round {
    margin-right: 8px;
    font-size: 18px;
}

/* Main content */
.main-content {
    flex: 1;
    padding: 20px;
    margin-left: 250px;
    width: calc(100% - 250px);
}

/* Status header */
.status-header {
    background-color: var(--light-card-bg);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--light-shadow);
}

.status-title {
    display: flex;
    align-items: center;
}

.header-logo-container {
    margin-right: 15px;
}

.header-logo {
    height: 40px;
}

.subtitle {
    font-size: 1.25rem;
    color: var(--light-text-secondary);
    font-weight: 400;
}

.status-boxes {
    display: flex;
    gap: 15px;
}

.status-box {
    background-color: var(--light-hover);
    border-radius: 8px;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    min-width: 180px;
}

.status-icon {
    background-color: rgba(14, 165, 233, 0.1);
    color: var(--accent-primary);
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.status-icon .material-icons-round {
    font-size: 24px;
}

.status-text {
    flex: 1;
}

.status-label {
    font-size: 0.75rem;
    color: var(--light-text-secondary);
    margin-bottom: 2px;
}

.status-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--light-text);
}

.status-box.system .status-icon {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--accent-success);
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .sidebar {
        width: 70px;
        overflow: hidden;
    }
    
    .sidebar-header {
        padding: 15px 10px;
    }
    
    .sidebar-nav a span:not(.material-icons-round) {
        display: none;
    }
    
    .sidebar-nav .material-icons-round {
        margin-right: 0;
    }
    
    .sidebar-footer {
        padding: 15px 10px;
    }
    
    .system-status span:not(.status-indicator),
    .logout-button span:not(.material-icons-round) {
        display: none;
    }
    
    .main-content {
        margin-left: 70px;
        width: calc(100% - 70px);
    }
}

@media (max-width: 768px) {
    .status-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .status-boxes {
        margin-top: 15px;
        width: 100%;
        flex-wrap: wrap;
    }
    
    .status-box {
        flex: 1;
        min-width: 140px;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 15px;
    }

    .status-boxes {
        flex-direction: column;
    }

    .status-box {
        width: 100%;
    }
}

/* Common Components */
.button {
    display: inline-flex;
    align-items: center;
    padding: 10px 16px;
    border: 1px solid var(--light-border);
    border-radius: 6px;
    background-color: var(--light-card-bg);
    color: var(--light-text);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 6px;
}

.button:hover {
    background-color: var(--light-hover);
    border-color: var(--accent-primary);
}

.button-primary {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.button-primary:hover {
    background-color: var(--accent-primary-hover);
    border-color: var(--accent-primary-hover);
}

.button-secondary {
    background-color: var(--light-hover);
    color: var(--light-text-secondary);
}

.button-secondary:hover {
    background-color: var(--light-border);
    color: var(--light-text);
}

.button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.button:disabled:hover {
    background-color: var(--light-card-bg);
    border-color: var(--light-border);
}

/* Form Controls */
.control-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

.control-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--light-text);
    margin-bottom: 5px;
}

.control-group input,
.control-group select {
    padding: 8px 12px;
    border: 1px solid var(--light-border);
    border-radius: 6px;
    background-color: var(--light-card-bg);
    color: var(--light-text);
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.control-group input:focus,
.control-group select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--light-card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--light-shadow);
}

table th {
    background-color: var(--light-hover);
    color: var(--light-text);
    font-weight: 600;
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--light-border);
}

table td {
    padding: 12px;
    border-bottom: 1px solid var(--light-border);
    color: var(--light-text);
}

table tr:hover {
    background-color: var(--light-hover);
}

/* Message Container */
#messageContainer {
    margin-bottom: 20px;
}

.message {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.message.success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--accent-success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.message.error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--accent-danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.message.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--accent-warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.message.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--accent-info);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--light-border);
}

.section-header h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    color: var(--light-text);
    font-size: 1.125rem;
    font-weight: 600;
}

.section-actions {
    display: flex;
    gap: 10px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 15px 0;
}

.page-info {
    color: var(--light-text-secondary);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    gap: 10px;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--light-card-bg);
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-content.report-preview {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--light-border);
}

.modal-header h3 {
    margin: 0;
    color: var(--light-text);
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--light-text-secondary);
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--light-hover);
    color: var(--light-text);
}

.modal-body {
    padding: 20px;
}

.detail-row {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--light-hover);
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-row strong {
    color: var(--light-text);
    font-weight: 600;
}

.severity-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.severity-badge.critical {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--accent-danger);
}

.severity-badge.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--accent-warning);
}

.severity-badge.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--accent-info);
}

/* Preview Content */
.preview-content h4 {
    color: var(--light-text);
    margin-bottom: 15px;
    font-size: 1.125rem;
}

.preview-content h5 {
    color: var(--light-text);
    margin: 20px 0 10px;
    font-size: 1rem;
}

.preview-chart {
    margin: 20px 0;
    padding: 15px;
    background-color: var(--light-hover);
    border-radius: 8px;
}

.preview-summary table {
    width: 100%;
    margin: 0;
    box-shadow: none;
}

.preview-summary table td {
    padding: 8px 12px;
    border-bottom: 1px solid var(--light-border);
}

.preview-summary table td:first-child {
    font-weight: 500;
    color: var(--light-text);
}

.preview-summary table td:last-child {
    text-align: right;
    color: var(--light-text-secondary);
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--light-text-secondary);
    font-style: italic;
}

.loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--light-border);
    border-top: 2px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--light-text-secondary);
}

.empty-state .material-icons-round {
    font-size: 64px;
    margin-bottom: 15px;
    opacity: 0.5;
    color: var(--light-text-secondary);
}

.empty-state h3 {
    margin: 0 0 10px;
    color: var(--light-text);
    font-size: 1.125rem;
}

.empty-state p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--light-text-secondary);
}
