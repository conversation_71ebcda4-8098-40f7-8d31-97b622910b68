<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zero Values Test - Online Data Logger</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.warning { background: #ffc107; color: #212529; }
        .button.danger { background: #dc3545; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; }
        .test-card h4 { margin: 0 0 10px; color: #495057; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Zero Values Dashboard Test</h1>
        <p>This tool tests if the dashboard can properly display zero values from your ESP32.</p>
        
        <div class="test-grid">
            <div class="test-card">
                <h4>📊 Test Zero Values</h4>
                <button class="button" onclick="sendZeroValues()">Send All Zeros</button>
                <div id="zeroStatus" class="result info">Ready to test</div>
            </div>
            
            <div class="test-card">
                <h4>📈 Test Normal Values</h4>
                <button class="button success" onclick="sendNormalValues()">Send Normal Data</button>
                <div id="normalStatus" class="result info">Ready to test</div>
            </div>
            
            <div class="test-card">
                <h4>🔄 Test Mixed Values</h4>
                <button class="button warning" onclick="sendMixedValues()">Send Mixed Data</button>
                <div id="mixedStatus" class="result info">Ready to test</div>
            </div>
            
            <div class="test-card">
                <h4>⚡ Auto Test</h4>
                <button class="button" onclick="startAutoTest()" id="autoTestBtn">Start Auto Test</button>
                <div id="autoStatus" class="result info">Ready to start</div>
            </div>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>📋 Test Results</h3>
            <div id="testResults"></div>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>🔗 Quick Actions</h3>
            <a href="frontend/advanced_dashboard.php" target="_blank" class="button">Open Dashboard</a>
            <a href="backend/get_latest_data.php" target="_blank" class="button">Check API</a>
            <a href="debug_data.php" target="_blank" class="button">Debug Tool</a>
            <button class="button" onclick="clearDatabase()">Clear Database</button>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>📊 Expected Dashboard Behavior</h3>
            <ul>
                <li><strong>Zero Values:</strong> Charts should show flat lines at zero, summary cards show "0.000"</li>
                <li><strong>Normal Values:</strong> Charts should show realistic electrical data curves</li>
                <li><strong>Mixed Values:</strong> Some parameters zero, others normal - should display both</li>
                <li><strong>Console Logs:</strong> Check browser console (F12) for detailed debugging info</li>
            </ul>
        </div>
    </div>

    <script>
        let autoTestInterval = null;
        let testCount = 0;
        
        // Test data sets
        const zeroData = {
            voltage_1: 0.0, voltage_2: 0.0, voltage_3: 0.0,
            current_1: 0.0, current_2: 0.0, current_3: 0.0,
            pf_1: 0.0, pf_2: 0.0, pf_3: 0.0,
            kva_1: 0.0, kva_2: 0.0, kva_3: 0.0,
            total_kva: 0.0, total_kw: 0.0, total_kvar: 0.0,
            frequency: 0.0
        };
        
        const normalData = {
            voltage_1: 230.5, voltage_2: 231.2, voltage_3: 229.8,
            current_1: 5.1, current_2: 5.2, current_3: 5.0,
            pf_1: 0.92, pf_2: 0.93, pf_3: 0.91,
            kva_1: 1150.5, kva_2: 1151.2, kva_3: 1149.8,
            total_kva: 3450.5, total_kw: 3277.8, total_kvar: 1076.5,
            frequency: 50.1
        };
        
        const mixedData = {
            voltage_1: 0.0, voltage_2: 230.0, voltage_3: 0.0,
            current_1: 5.0, current_2: 0.0, current_3: 5.0,
            pf_1: 0.0, pf_2: 0.92, pf_3: 0.0,
            kva_1: 0.0, kva_2: 1150.0, kva_3: 0.0,
            total_kva: 1150.0, total_kw: 1058.0, total_kvar: 0.0,
            frequency: 50.0
        };
        
        async function sendTestData(data, testName, statusElementId) {
            const statusElement = document.getElementById(statusElementId);
            const resultsDiv = document.getElementById('testResults');
            
            statusElement.innerHTML = `<span class="status-indicator status-pending"></span>Sending ${testName}...`;
            
            try {
                const response = await fetch('backend/receive_data.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.text();
                
                if (response.ok) {
                    statusElement.innerHTML = `<span class="status-indicator status-success"></span>${testName} sent successfully!`;
                    resultsDiv.innerHTML += `
                        <div class="result success">
                            ✅ ${testName} - ${new Date().toLocaleTimeString()}
                            <details>
                                <summary>Data sent:</summary>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                statusElement.innerHTML = `<span class="status-indicator status-error"></span>Failed to send ${testName}`;
                resultsDiv.innerHTML += `
                    <div class="result error">
                        ❌ ${testName} failed - ${error.message}
                    </div>
                `;
            }
        }
        
        function sendZeroValues() {
            sendTestData(zeroData, 'Zero Values', 'zeroStatus');
        }
        
        function sendNormalValues() {
            sendTestData(normalData, 'Normal Values', 'normalStatus');
        }
        
        function sendMixedValues() {
            sendTestData(mixedData, 'Mixed Values', 'mixedStatus');
        }
        
        function startAutoTest() {
            const btn = document.getElementById('autoTestBtn');
            const statusElement = document.getElementById('autoStatus');
            
            if (autoTestInterval) {
                // Stop auto test
                clearInterval(autoTestInterval);
                autoTestInterval = null;
                btn.textContent = 'Start Auto Test';
                btn.className = 'button';
                statusElement.innerHTML = '<span class="status-indicator status-success"></span>Auto test stopped';
                return;
            }
            
            // Start auto test
            btn.textContent = 'Stop Auto Test';
            btn.className = 'button danger';
            testCount = 0;
            
            autoTestInterval = setInterval(() => {
                testCount++;
                const testTypes = [
                    { data: zeroData, name: 'Zero Values' },
                    { data: normalData, name: 'Normal Values' },
                    { data: mixedData, name: 'Mixed Values' }
                ];
                
                const testType = testTypes[testCount % testTypes.length];
                sendTestData(testType.data, `Auto Test ${testCount} (${testType.name})`, 'autoStatus');
                
                statusElement.innerHTML = `<span class="status-indicator status-pending"></span>Auto test running... (${testCount} sent)`;
                
            }, 3000); // Send every 3 seconds
        }
        
        async function clearDatabase() {
            if (!confirm('Are you sure you want to clear all data from the database?')) {
                return;
            }
            
            try {
                const response = await fetch('backend/clear_data.php', { method: 'POST' });
                const result = await response.text();
                
                document.getElementById('testResults').innerHTML += `
                    <div class="result info">
                        🗑️ Database cleared - ${new Date().toLocaleTimeString()}
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('testResults').innerHTML += `
                    <div class="result error">
                        ❌ Failed to clear database - ${error.message}
                    </div>
                `;
            }
        }
        
        // Auto-refresh results
        setInterval(() => {
            const results = document.getElementById('testResults');
            const children = results.children;
            if (children.length > 10) {
                // Keep only last 10 results
                for (let i = 0; i < children.length - 10; i++) {
                    results.removeChild(children[0]);
                }
            }
        }, 5000);
    </script>
</body>
</html>
