/**
 * AI Analysis for Electrical System Graphs
 * This module provides AI-powered analysis of electrical parameters
 * to identify patterns, anomalies, and optimization opportunities
 */

// Configuration for analysis thresholds and parameters
const analysisConfig = {
    // Voltage analysis parameters
    voltage: {
        nominal: 415, // Nominal voltage for 3-phase system
        highThreshold: 440, // High voltage threshold
        lowThreshold: 390, // Low voltage threshold
        imbalanceThreshold: 5, // % threshold for phase imbalance
        fluctuationThreshold: 3, // % threshold for rapid fluctuations
    },
    
    // Current analysis parameters
    current: {
        imbalanceThreshold: 10, // % threshold for phase imbalance
        overloadThreshold: 90, // % of rated capacity
        harmonicThreshold: 5, // % THD threshold
    },
    
    // Power factor analysis parameters
    powerFactor: {
        optimalMin: 0.95, // Minimum optimal power factor
        correctionNeededThreshold: 0.85, // Threshold below which correction is recommended
        leadingThreshold: 1.0, // Threshold for leading power factor
    },
    
    // Frequency analysis parameters
    frequency: {
        nominal: 50, // Nominal frequency (Hz)
        deviationThreshold: 0.5, // Hz deviation threshold
    },
    
    // Time windows for different analyses
    timeWindows: {
        short: 5 * 60, // 5 minutes in seconds
        medium: 60 * 60, // 1 hour in seconds
        long: 24 * 60 * 60, // 24 hours in seconds
    }
};

/**
 * Main function to analyze electrical system data
 * @param {Array} data - Array of data points with electrical parameters
 * @returns {Object} Analysis results with insights and recommendations
 */
function analyzeElectricalSystem(data) {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            status: 'error',
            message: 'No data available for analysis'
        };
    }
    
    // Perform various analyses
    const voltageAnalysis = analyzeVoltage(data);
    const currentAnalysis = analyzeCurrent(data);
    const powerFactorAnalysis = analyzePowerFactor(data);
    const frequencyAnalysis = analyzeFrequency(data);
    const loadPatternAnalysis = analyzeLoadPatterns(data);
    const efficiencyAnalysis = analyzeSystemEfficiency(data);
    
    // Generate overall insights and recommendations
    const insights = generateInsights(
        voltageAnalysis, 
        currentAnalysis, 
        powerFactorAnalysis, 
        frequencyAnalysis,
        loadPatternAnalysis,
        efficiencyAnalysis
    );
    
    return {
        status: 'success',
        timestamp: new Date().toISOString(),
        dataPoints: data.length,
        timeRange: {
            start: data[data.length - 1].timestamp,
            end: data[0].timestamp
        },
        analyses: {
            voltage: voltageAnalysis,
            current: currentAnalysis,
            powerFactor: powerFactorAnalysis,
            frequency: frequencyAnalysis,
            loadPattern: loadPatternAnalysis,
            efficiency: efficiencyAnalysis
        },
        insights: insights
    };
}

/**
 * Analyze voltage data for issues and patterns
 * @param {Array} data - Array of data points
 * @returns {Object} Voltage analysis results
 */
function analyzeVoltage(data) {
    // Extract voltage data
    const voltageData = {
        phase1: data.map(d => parseFloat(d.voltage_1)),
        phase2: data.map(d => parseFloat(d.voltage_2)),
        phase3: data.map(d => parseFloat(d.voltage_3))
    };
    
    // Calculate statistics
    const stats = {
        phase1: calculateStatistics(voltageData.phase1),
        phase2: calculateStatistics(voltageData.phase2),
        phase3: calculateStatistics(voltageData.phase3)
    };
    
    // Check for voltage issues
    const highVoltageIssues = detectHighVoltage(voltageData, stats);
    const lowVoltageIssues = detectLowVoltage(voltageData, stats);
    const imbalanceIssues = detectVoltageImbalance(voltageData, stats);
    const fluctuationIssues = detectVoltageFluctuations(voltageData);
    
    // Generate recommendations
    const recommendations = [];
    
    if (highVoltageIssues.detected) {
        recommendations.push('Consider adjusting transformer tap settings to reduce voltage');
    }
    
    if (lowVoltageIssues.detected) {
        recommendations.push('Check for excessive load or adjust transformer tap settings to increase voltage');
    }
    
    if (imbalanceIssues.detected) {
        recommendations.push('Redistribute single-phase loads to balance the system');
    }
    
    if (fluctuationIssues.detected) {
        recommendations.push('Investigate sources of voltage fluctuations (e.g., large motor starts, welding equipment)');
    }
    
    return {
        statistics: stats,
        issues: {
            highVoltage: highVoltageIssues,
            lowVoltage: lowVoltageIssues,
            imbalance: imbalanceIssues,
            fluctuations: fluctuationIssues
        },
        recommendations: recommendations
    };
}

/**
 * Analyze current data for issues and patterns
 * @param {Array} data - Array of data points
 * @returns {Object} Current analysis results
 */
function analyzeCurrent(data) {
    // Extract current data
    const currentData = {
        phase1: data.map(d => parseFloat(d.current_1)),
        phase2: data.map(d => parseFloat(d.current_2)),
        phase3: data.map(d => parseFloat(d.current_3))
    };
    
    // Calculate statistics
    const stats = {
        phase1: calculateStatistics(currentData.phase1),
        phase2: calculateStatistics(currentData.phase2),
        phase3: calculateStatistics(currentData.phase3)
    };
    
    // Check for current issues
    const imbalanceIssues = detectCurrentImbalance(currentData, stats);
    const overloadIssues = detectCurrentOverload(currentData, stats);
    const harmonicIssues = estimateHarmonicIssues(currentData);
    
    // Generate recommendations
    const recommendations = [];
    
    if (imbalanceIssues.detected) {
        recommendations.push('Redistribute loads to balance current across phases');
    }
    
    if (overloadIssues.detected) {
        recommendations.push('Reduce load on overloaded phases or upgrade system capacity');
    }
    
    if (harmonicIssues.detected) {
        recommendations.push('Consider installing harmonic filters or upgrading to harmonic-resistant equipment');
    }
    
    return {
        statistics: stats,
        issues: {
            imbalance: imbalanceIssues,
            overload: overloadIssues,
            harmonics: harmonicIssues
        },
        recommendations: recommendations
    };
}

/**
 * Analyze power factor data for issues and optimization opportunities
 * @param {Array} data - Array of data points
 * @returns {Object} Power factor analysis results
 */
function analyzePowerFactor(data) {
    // Extract power factor data
    const pfData = {
        phase1: data.map(d => parseFloat(d.pf_1)),
        phase2: data.map(d => parseFloat(d.pf_2)),
        phase3: data.map(d => parseFloat(d.pf_3))
    };
    
    // Calculate statistics
    const stats = {
        phase1: calculateStatistics(pfData.phase1),
        phase2: calculateStatistics(pfData.phase2),
        phase3: calculateStatistics(pfData.phase3)
    };
    
    // Calculate average power factor
    const avgPF = (stats.phase1.mean + stats.phase2.mean + stats.phase3.mean) / 3;
    
    // Check for power factor issues
    const lowPFIssues = detectLowPowerFactor(pfData, stats);
    const leadingPFIssues = detectLeadingPowerFactor(pfData, stats);
    
    // Calculate potential savings from power factor correction
    const potentialSavings = estimatePFCorrectionSavings(avgPF, data);
    
    // Generate recommendations
    const recommendations = [];
    
    if (lowPFIssues.detected) {
        recommendations.push(`Install power factor correction capacitors to improve PF to at least ${analysisConfig.powerFactor.optimalMin}`);
    }
    
    if (leadingPFIssues.detected) {
        recommendations.push('Reduce capacitive compensation as leading power factor detected');
    }
    
    return {
        statistics: stats,
        averagePF: avgPF,
        issues: {
            lowPowerFactor: lowPFIssues,
            leadingPowerFactor: leadingPFIssues
        },
        potentialSavings: potentialSavings,
        recommendations: recommendations
    };
}

/**
 * Analyze frequency data for stability and issues
 * @param {Array} data - Array of data points
 * @returns {Object} Frequency analysis results
 */
function analyzeFrequency(data) {
    // Extract frequency data
    const frequencyData = data.map(d => parseFloat(d.frequency));
    
    // Calculate statistics
    const stats = calculateStatistics(frequencyData);
    
    // Check for frequency issues
    const deviationIssues = detectFrequencyDeviation(frequencyData, stats);
    const stabilityIssues = assessFrequencyStability(frequencyData);
    
    // Generate recommendations
    const recommendations = [];
    
    if (deviationIssues.detected) {
        recommendations.push('Investigate power supply quality or consider frequency stabilization equipment');
    }
    
    if (stabilityIssues.detected) {
        recommendations.push('Monitor generator performance or grid connection quality');
    }
    
    return {
        statistics: stats,
        issues: {
            deviation: deviationIssues,
            stability: stabilityIssues
        },
        recommendations: recommendations
    };
}

/**
 * Analyze load patterns to identify optimization opportunities
 * @param {Array} data - Array of data points
 * @returns {Object} Load pattern analysis results
 */
function analyzeLoadPatterns(data) {
    // Extract timestamps and power data
    const timestamps = data.map(d => new Date(d.timestamp));
    const powerData = data.map(d => parseFloat(d.total_kw));
    
    // Identify peak and off-peak periods
    const peakPeriods = identifyPeakPeriods(timestamps, powerData);
    const loadFactor = calculateLoadFactor(powerData);
    
    // Generate recommendations
    const recommendations = [];
    
    if (loadFactor < 0.7) {
        recommendations.push('Consider load shifting to improve load factor and reduce peak demand charges');
    }
    
    if (peakPeriods.length > 0) {
        recommendations.push('Implement peak shaving strategies during identified peak periods');
    }
    
    return {
        loadFactor: loadFactor,
        peakPeriods: peakPeriods,
        recommendations: recommendations
    };
}

/**
 * Analyze system efficiency and energy usage
 * @param {Array} data - Array of data points
 * @returns {Object} Efficiency analysis results
 */
function analyzeSystemEfficiency(data) {
    // Calculate apparent power, real power, and reactive power
    const apparentPower = data.map(d => parseFloat(d.total_kva));
    const realPower = data.map(d => parseFloat(d.total_kw));
    const reactivePower = data.map(d => parseFloat(d.total_kvar));
    
    // Calculate system efficiency metrics
    const powerQuality = assessPowerQuality(realPower, apparentPower, reactivePower);
    const energyEfficiency = estimateEnergyEfficiency(data);
    
    // Generate recommendations
    const recommendations = [];
    
    if (powerQuality.score < 0.8) {
        recommendations.push('Improve power quality with filters and power conditioning equipment');
    }
    
    if (energyEfficiency.score < 0.7) {
        recommendations.push('Implement energy efficiency measures to reduce losses');
    }
    
    return {
        powerQuality: powerQuality,
        energyEfficiency: energyEfficiency,
        recommendations: recommendations
    };
}

// Export the main analysis function
window.analyzeElectricalSystem = analyzeElectricalSystem;
