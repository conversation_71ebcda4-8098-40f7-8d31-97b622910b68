import React from 'react';
import styled from 'styled-components';

const StatusIndicator = ({ message, type }) => {
  if (!message) return null;
  
  return (
    <StatusIndicatorContainer className={type}>
      {message}
    </StatusIndicatorContainer>
  );
};

const StatusIndicatorContainer = styled.div`
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--card-color);
  color: var(--text-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-xl);
  z-index: 1000;
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  max-width: 350px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border: 1px solid var(--border-color);
  transform: translateY(0);
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0.25rem;
  }
  
  &.error {
    background-color: rgba(230, 57, 70, 0.1);
    color: var(--accent-color);
    border-color: rgba(230, 57, 70, 0.3);
    
    &::before {
      background: var(--gradient-accent);
    }
  }
  
  &.warning {
    background-color: rgba(249, 168, 38, 0.1);
    color: var(--warning-color);
    border-color: rgba(249, 168, 38, 0.3);
    
    &::before {
      background: var(--gradient-warning);
    }
  }
  
  &.success {
    background-color: rgba(0, 135, 90, 0.1);
    color: var(--success-color);
    border-color: rgba(0, 135, 90, 0.3);
    
    &::before {
      background: var(--gradient-success);
    }
  }
`;

export default StatusIndicator;
