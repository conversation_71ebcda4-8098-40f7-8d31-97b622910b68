// Generated by CoffeeScript 1.12.7
var object, self,
  hasProp = {}.hasOwnProperty;

object = require('utila').object;

module.exports = self = {
  sanitize: function(val) {
    return self._toChildren(val);
  },
  _toChildren: function(val) {
    var ref;
    if (object.isBareObject(val)) {
      return self._objectToChildren(val);
    } else if (Array.isArray(val)) {
      return self._arrayToChildren(val);
    } else if (val === null || typeof val === 'undefined') {
      return [];
    } else if ((ref = typeof val) === 'string' || ref === 'number') {
      return [String(val)];
    } else {
      throw Error("not a valid child node: `" + val);
    }
  },
  _objectToChildren: function(o) {
    var a, cur, key, val;
    a = [];
    for (key in o) {
      if (!hasProp.call(o, key)) continue;
      val = o[key];
      cur = {};
      cur[key] = self.sanitize(val);
      a.push(cur);
    }
    return a;
  },
  _arrayToChildren: function(a) {
    var i, len, ret, v;
    ret = [];
    for (i = 0, len = a.length; i < len; i++) {
      v = a[i];
      ret.push(self._toNode(v));
    }
    return ret;
  },
  _toNode: function(o) {
    var key, keys, obj, ref;
    if ((ref = typeof o) === 'string' || ref === 'number') {
      return String(o);
    } else if (object.isBareObject(o)) {
      keys = Object.keys(o);
      if (keys.length !== 1) {
        throw Error("a node must only have one key as tag name");
      }
      key = keys[0];
      obj = {};
      obj[key] = self._toChildren(o[key]);
      return obj;
    } else {
      throw Error("not a valid node: `" + o + "`");
    }
  }
};
