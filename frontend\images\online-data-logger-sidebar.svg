<?xml version="1.0" encoding="UTF-8"?>
<svg width="240" height="80" viewBox="0 0 240 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="240" height="80" rx="4" fill="#007A8C" />
  
  <!-- Grid <PERSON> -->
  <g opacity="0.2">
    <path d="M0 0 L240 0" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M0 10 L240 10" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M0 20 L240 20" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M0 30 L240 30" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M0 40 L240 40" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M0 50 L240 50" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M0 60 L240 60" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M0 70 L240 70" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M0 80 L240 80" stroke="#FFFFFF" stroke-width="0.5" />
    
    <path d="M0 0 L0 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M20 0 L20 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M40 0 L40 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M60 0 L60 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M80 0 L80 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M100 0 L100 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M120 0 L120 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M140 0 L140 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M160 0 L160 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M180 0 L180 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M200 0 L200 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M220 0 L220 80" stroke="#FFFFFF" stroke-width="0.5" />
    <path d="M240 0 L240 80" stroke="#FFFFFF" stroke-width="0.5" />
  </g>
  
  <!-- Hexagon with Circuit Design -->
  <g transform="translate(40, 40) scale(0.4)">
    <!-- Hexagon Outline -->
    <path d="M0,-80 L70,-40 L70,40 L0,80 L-70,40 L-70,-40 Z" stroke="#5ECCE9" stroke-width="4" fill="none" />
    
    <!-- Circuit Nodes and Connections -->
    <circle cx="0" cy="0" r="15" fill="#5ECCE9" />
    <circle cx="30" cy="-30" r="10" fill="#5ECCE9" />
    <circle cx="-30" cy="-20" r="8" fill="#5ECCE9" />
    <circle cx="-40" cy="20" r="7" fill="#5ECCE9" />
    <circle cx="40" cy="30" r="9" fill="#5ECCE9" />
    <circle cx="20" cy="40" r="6" fill="#5ECCE9" />
    <circle cx="-20" cy="30" r="8" fill="#5ECCE9" />
    <circle cx="50" cy="-10" r="5" fill="#5ECCE9" />
    <circle cx="-50" cy="-10" r="4" fill="#5ECCE9" />
    <circle cx="10" cy="-50" r="6" fill="#5ECCE9" />
    <circle cx="-10" cy="-40" r="5" fill="#5ECCE9" />
    <circle cx="35" cy="10" r="4" fill="#5ECCE9" />
    
    <!-- Circuit Lines -->
    <path d="M0,0 L30,-30" stroke="#5ECCE9" stroke-width="2" />
    <path d="M0,0 L-30,-20" stroke="#5ECCE9" stroke-width="2" />
    <path d="M0,0 L-40,20" stroke="#5ECCE9" stroke-width="2" />
    <path d="M0,0 L40,30" stroke="#5ECCE9" stroke-width="2" />
    <path d="M0,0 L20,40" stroke="#5ECCE9" stroke-width="2" />
    <path d="M0,0 L-20,30" stroke="#5ECCE9" stroke-width="2" />
    <path d="M30,-30 L50,-10" stroke="#5ECCE9" stroke-width="2" />
    <path d="M-30,-20 L-50,-10" stroke="#5ECCE9" stroke-width="2" />
    <path d="M-30,-20 L-10,-40" stroke="#5ECCE9" stroke-width="2" />
    <path d="M30,-30 L10,-50" stroke="#5ECCE9" stroke-width="2" />
    <path d="M40,30 L35,10" stroke="#5ECCE9" stroke-width="2" />
    <path d="M20,40 L35,10" stroke="#5ECCE9" stroke-width="2" />
  </g>
  
  <!-- Text: ONLINE DATA LOGGER -->
  <g transform="translate(140, 40)">
    <text x="0" y="-10" font-family="Arial, sans-serif" font-size="16" font-weight="800" fill="#FFFFFF" text-anchor="middle">ONLINE</text>
    <text x="0" y="10" font-family="Arial, sans-serif" font-size="16" font-weight="800" fill="#FFFFFF" text-anchor="middle">DATA LOGGER</text>
  </g>
</svg>
