import React from 'react';
import styled from 'styled-components';

/**
 * Fallback component to display when data fails to load
 */
const DataLoadingFallback = ({ 
  message = 'Unable to load data', 
  onRetry,
  isLoading = false,
  lastSuccessTime = null
}) => {
  return (
    <FallbackContainer>
      {isLoading ? (
        <>
          <LoadingIcon className="material-icons-round">hourglass_top</LoadingIcon>
          <FallbackTitle>Loading data...</FallbackTitle>
          <FallbackMessage>Please wait while we fetch the latest information.</FallbackMessage>
        </>
      ) : (
        <>
          <ErrorIcon className="material-icons-round">cloud_off</ErrorIcon>
          <FallbackTitle>Data Unavailable</FallbackTitle>
          <FallbackMessage>{message}</FallbackMessage>
          
          {lastSuccessTime && (
            <LastSuccessMessage>
              Last successful data update: {lastSuccessTime}
            </LastSuccessMessage>
          )}
          
          {onRetry && (
            <RetryButton onClick={onRetry}>
              <span className="material-icons-round">refresh</span>
              Retry
            </RetryButton>
          )}
        </>
      )}
    </FallbackContainer>
  );
};

const FallbackContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  text-align: center;
  min-height: 200px;
  height: 100%;
`;

const ErrorIcon = styled.span`
  font-size: 3rem;
  color: var(--warning-color);
  margin-bottom: 1rem;
`;

const LoadingIcon = styled.span`
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
  animation: rotate 1.5s infinite linear;
  
  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const FallbackTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin-bottom: 0.75rem;
`;

const FallbackMessage = styled.p`
  font-size: 0.9375rem;
  color: var(--text-secondary);
  margin-bottom: 1.25rem;
  max-width: 400px;
`;

const LastSuccessMessage = styled.p`
  font-size: 0.8125rem;
  color: var(--text-muted);
  margin-bottom: 1.25rem;
  font-style: italic;
`;

const RetryButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  span {
    font-size: 1.125rem;
  }
`;

export default DataLoadingFallback;
