<?php
/**
 * Simple CSS/JS Helper Functions (Vite Alternative)
 * Fallback to direct CSS and JS files for light mode styling
 */

/**
 * Get Vite client script for development (disabled for simplicity)
 */
function viteClientScript() {
    return '';
}

/**
 * Get CSS tag for a given entry
 */
function viteCssTag($entry) {
    // Map entry names to actual CSS files
    $cssMap = [
        'history' => 'history.css',
        'stability' => 'stability.css',
        'alerts' => 'alerts.css',
        'reports' => 'reports.css',
        'dashboard' => 'light-theme.css'
    ];

    $cssFile = isset($cssMap[$entry]) ? $cssMap[$entry] : $entry . '.css';

    if (file_exists(__DIR__ . '/' . $cssFile)) {
        return '<link rel="stylesheet" href="' . $cssFile . '?v=' . filemtime(__DIR__ . '/' . $cssFile) . '">';
    }

    // Always include base light theme
    if (file_exists(__DIR__ . '/light-theme.css')) {
        return '<link rel="stylesheet" href="light-theme.css?v=' . filemtime(__DIR__ . '/light-theme.css') . '">';
    }

    return '';
}

/**
 * Get script tag for a given entry
 */
function viteScriptTag($entry) {
    // Map entry names to actual JS files
    $jsMap = [
        'history' => 'history.js',
        'stability' => 'stability.js',
        'alerts' => 'alerts.js',
        'reports' => 'reports.js',
        'dashboard' => 'advanced-dashboard.js'
    ];

    $jsFile = isset($jsMap[$entry]) ? $jsMap[$entry] : $entry . '.js';

    if (file_exists(__DIR__ . '/' . $jsFile)) {
        return '<script src="' . $jsFile . '?v=' . filemtime(__DIR__ . '/' . $jsFile) . '"></script>';
    }

    return '';
}
?>
