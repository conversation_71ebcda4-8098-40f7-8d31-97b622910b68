<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Data Logger System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .card:hover {
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .card-description {
            color: #64748b;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #1d4ed8;
        }
        
        .status-section {
            background: #f1f5f9;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .status-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #1e293b;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .status-label {
            font-size: 0.9rem;
            color: #64748b;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-weight: 600;
            color: #1e293b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">⚡ Online Data Logger</div>
            <div class="subtitle">Real-time Electrical Monitoring System</div>
        </div>
        
        <div class="grid">
            <div class="card" onclick="window.location.href='frontend/basic_dashboard.php'">
                <div class="card-title">📊 Basic Dashboard</div>
                <div class="card-description">Simple, clean interface for monitoring electrical parameters</div>
                <a href="frontend/basic_dashboard.php" class="btn">Open Dashboard</a>
            </div>
            
            <div class="card" onclick="window.location.href='frontend/advanced_dashboard.php'">
                <div class="card-title">📈 Advanced Dashboard</div>
                <div class="card-description">Real-time charts and comprehensive data visualization</div>
                <a href="frontend/advanced_dashboard.php" class="btn">Open Dashboard</a>
            </div>
            
            <div class="card" onclick="window.location.href='frontend/react_dashboard.php'">
                <div class="card-title">⚛️ React Dashboard</div>
                <div class="card-description">Modern React-based interface with advanced features</div>
                <a href="frontend/react_dashboard.php" class="btn">Open Dashboard</a>
            </div>
        </div>
        
        <div class="status-section">
            <div class="status-title">🔧 System Tools & Diagnostics</div>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Database Check</div>
                    <div class="status-value"><a href="check_data.php" style="color: #2563eb;">View Data</a></div>
                </div>
                <div class="status-item">
                    <div class="status-label">API Test</div>
                    <div class="status-value"><a href="backend/get_latest_data.php" style="color: #2563eb;">Test API</a></div>
                </div>
                <div class="status-item">
                    <div class="status-label">Connection Test</div>
                    <div class="status-value"><a href="backend/test_connection.php" style="color: #2563eb;">Test ESP32</a></div>
                </div>
                <div class="status-item">
                    <div class="status-label">Setup Database</div>
                    <div class="status-value"><a href="setup.php" style="color: #2563eb;">Setup DB</a></div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #64748b; font-size: 0.9rem;">
            <p>ESP32 Server: <strong>http://192.168.244.65/online%20data%20logger/backend/receive_data.php</strong></p>
            <p style="margin-top: 5px;">Last updated: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
</body>
</html>
