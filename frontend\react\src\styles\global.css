/* Global CSS Variables */
:root {
  /* Professional color palette - Enhanced & Refined */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #60a5fa;
  --secondary-color: #10b981;
  --accent-color: #f43f5e;
  --warning-color: #f59e0b;
  --success-color: #059669;
  --info-color: #0ea5e9;

  /* Text and background colors */
  --text-color: #0f172a;
  --text-secondary: #475569;
  --text-muted: #94a3b8;
  --background-color: #f8fafc;
  --card-color: #ffffff;
  --border-color: #e2e8f0;
  --hover-color: #f1f5f9;

  /* Shadows with improved depth perception and subtle coloring */
  --shadow-sm: 0 1px 3px rgba(15, 23, 42, 0.05), 0 1px 2px rgba(15, 23, 42, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(15, 23, 42, 0.08), 0 2px 4px -1px rgba(15, 23, 42, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(15, 23, 42, 0.08), 0 4px 6px -2px rgba(15, 23, 42, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(15, 23, 42, 0.1), 0 10px 10px -5px rgba(15, 23, 42, 0.04);
  --shadow-colored: 0 4px 14px 0 rgba(37, 99, 235, 0.1);
  --shadow-colored-lg: 0 10px 25px 0 rgba(37, 99, 235, 0.15);

  /* Typography */
  --font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Layout */
  --border-radius-sm: 4px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --transition-speed: 0.2s;
  --content-width: 1440px;

  /* Gradients - Enhanced with modern angles and subtle transitions */
  --gradient-primary: linear-gradient(145deg, var(--primary-color), var(--primary-dark));
  --gradient-secondary: linear-gradient(145deg, var(--secondary-color), #059669);
  --gradient-accent: linear-gradient(145deg, var(--accent-color), #e11d48);
  --gradient-success: linear-gradient(145deg, var(--success-color), #047857);
  --gradient-warning: linear-gradient(145deg, var(--warning-color), #d97706);
  --gradient-glass: linear-gradient(120deg, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
  --gradient-glass-dark: linear-gradient(120deg, rgba(15, 23, 42, 0.7), rgba(15, 23, 42, 0.3));

  /* Dark theme colors - Enhanced for better contrast and readability */
  --dark-background: #0f172a;
  --dark-card: #1e293b;
  --dark-border: #334155;
  --dark-text: #f8fafc;
  --dark-text-secondary: #cbd5e1;
  --dark-text-muted: #94a3b8;
  --dark-hover: #273549;
  --dark-accent: #3b82f6;
  --dark-accent-hover: #60a5fa;

  /* Chart colors and styling */
  --chart-line-width: 1.5px;
  --chart-point-radius: 0;
  --chart-grid-color: rgba(203, 213, 225, 0.4);
  --chart-tooltip-bg: rgba(255, 255, 255, 0.95);
  --chart-tooltip-border: rgba(226, 232, 240, 0.8);
  --chart-tooltip-shadow: 0 4px 6px rgba(15, 23, 42, 0.1);

  /* Modern UI elements */
  --glass-bg: rgba(255, 255, 255, 0.7);
  --glass-border: rgba(255, 255, 255, 0.5);
  --glass-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  --backdrop-blur: 8px;

  /* Animation durations */
  --animation-slow: 0.5s;
  --animation-medium: 0.3s;
  --animation-fast: 0.15s;
}

/* Reset styles */
*, *::before, *::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: var(--font-family);
}

html {
  font-size: 16px;
  height: 100%;
  scroll-behavior: smooth;
}

body {
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
  font-size: 0.875rem;
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.01em;
  transition: all var(--transition-speed) ease;
  overflow-x: hidden;
  min-height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Dark theme */
body.dark-theme {
  --background-color: var(--dark-background);
  --card-color: var(--dark-card);
  --border-color: var(--dark-border);
  --text-color: var(--dark-text);
  --text-secondary: var(--dark-text-secondary);
  --text-muted: var(--dark-text-muted);
  --hover-color: var(--dark-hover);
  --primary-color: var(--dark-accent);
  --primary-light: var(--dark-accent-hover);
  --chart-grid-color: rgba(51, 65, 85, 0.5);
  --chart-tooltip-bg: rgba(30, 41, 59, 0.95);
  --chart-tooltip-border: rgba(51, 65, 85, 0.8);

  /* Glass effect for dark mode */
  --glass-bg: rgba(30, 41, 59, 0.7);
  --glass-border: rgba(51, 65, 85, 0.5);

  /* Adjust shadows for dark mode */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  --shadow-colored: 0 4px 14px 0 rgba(59, 130, 246, 0.2);
  --shadow-colored-lg: 0 10px 25px 0 rgba(59, 130, 246, 0.25);
}

/* Modern UI Elements */
.glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(var(--backdrop-blur));
  -webkit-backdrop-filter: blur(var(--backdrop-blur));
  box-shadow: var(--glass-shadow);
}

.pill {
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.pill.primary {
  background-color: rgba(37, 99, 235, 0.1);
  color: var(--primary-color);
}

.pill.success {
  background-color: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
}

.pill.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.pill.danger {
  background-color: rgba(244, 63, 94, 0.1);
  color: var(--accent-color);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
  }
}

.animate-fade-in {
  animation: fadeIn var(--animation-medium) ease forwards;
}

.animate-slide-up {
  animation: slideInUp var(--animation-medium) ease forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Print-only elements (hidden by default) */
.print-only {
  display: none;
}

/* Import print styles */
@import url('./print.css');
