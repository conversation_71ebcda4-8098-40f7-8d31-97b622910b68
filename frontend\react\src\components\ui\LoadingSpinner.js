import React from 'react';
import styled, { keyframes } from 'styled-components';

const LoadingSpinner = ({ size = 'medium', fullScreen = false, message = 'Loading...' }) => {
  return (
    <SpinnerContainer fullScreen={fullScreen}>
      <Spinner size={size} />
      {message && <SpinnerMessage>{message}</SpinnerMessage>}
    </SpinnerContainer>
  );
};

const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const SpinnerContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  
  ${props => props.fullScreen && `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 23, 42, 0.7);
    backdrop-filter: blur(4px);
    z-index: 9999;
  `}
`;

const Spinner = styled.div`
  width: ${props => {
    switch (props.size) {
      case 'small': return '1.5rem';
      case 'large': return '3rem';
      default: return '2.25rem';
    }
  }};
  
  height: ${props => {
    switch (props.size) {
      case 'small': return '1.5rem';
      case 'large': return '3rem';
      default: return '2.25rem';
    }
  }};
  
  border: ${props => {
    switch (props.size) {
      case 'small': return '2px';
      case 'large': return '4px';
      default: return '3px';
    }
  }} solid rgba(var(--primary-color), 0.2);
  
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
`;

const SpinnerMessage = styled.div`
  margin-top: 1rem;
  color: var(--text-color);
  font-size: 0.9375rem;
  font-weight: var(--font-weight-medium);
`;

export default LoadingSpinner;
