import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import CustomizeModal from '../CustomizeModal';
import { DashboardProvider } from '../../../contexts/DashboardContext';

// Mock the Modal component
jest.mock('../Modal', () => {
  return ({ children, isOpen, title }) => (
    isOpen ? (
      <div data-testid="modal">
        <h2>{title}</h2>
        {children}
      </div>
    ) : null
  );
});

// Mock notification function
const mockAddNotification = jest.fn();

describe('CustomizeModal', () => {
  beforeEach(() => {
    mockAddNotification.mockClear();
  });
  
  it('renders when isOpen is true', () => {
    render(
      <DashboardProvider>
        <CustomizeModal 
          isOpen={true} 
          onClose={() => {}} 
          addNotification={mockAddNotification} 
        />
      </DashboardProvider>
    );
    
    // Modal should be rendered
    expect(screen.getByTestId('modal')).toBeInTheDocument();
    
    // Title should be correct
    expect(screen.getByText('Customize Dashboard')).toBeInTheDocument();
  });
  
  it('does not render when isOpen is false', () => {
    render(
      <DashboardProvider>
        <CustomizeModal 
          isOpen={false} 
          onClose={() => {}} 
          addNotification={mockAddNotification} 
        />
      </DashboardProvider>
    );
    
    // Modal should not be rendered
    expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
  });
  
  it('toggles edit mode when button is clicked', () => {
    render(
      <DashboardProvider>
        <CustomizeModal 
          isOpen={true} 
          onClose={() => {}} 
          addNotification={mockAddNotification} 
        />
      </DashboardProvider>
    );
    
    // Find the edit mode button (it should say "Edit Layout" initially)
    const editModeButton = screen.getByText('Edit Layout').closest('button');
    
    // Click the button
    fireEvent.click(editModeButton);
    
    // Notification should be called
    expect(mockAddNotification).toHaveBeenCalledWith(
      expect.stringContaining('Dashboard edit mode'), 
      'info'
    );
    
    // Button text should change to "Lock Layout"
    expect(screen.getByText('Lock Layout')).toBeInTheDocument();
  });
  
  it('filters widgets by category', () => {
    render(
      <DashboardProvider>
        <CustomizeModal 
          isOpen={true} 
          onClose={() => {}} 
          addNotification={mockAddNotification} 
        />
      </DashboardProvider>
    );
    
    // All widgets should be visible initially
    expect(screen.getAllByText(/Widget/i).length).toBeGreaterThan(1);
    
    // Find and click a category button (e.g., "Power")
    const powerCategoryButton = screen.getByText('Power');
    fireEvent.click(powerCategoryButton);
    
    // Only power widgets should be visible
    const visibleWidgets = screen.getAllByText(/Widget/i);
    visibleWidgets.forEach(widget => {
      const widgetCard = widget.closest('[class*="WidgetCard"]');
      expect(widgetCard).toBeInTheDocument();
    });
  });
});
