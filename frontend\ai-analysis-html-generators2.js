/**
 * Additional HTML generators for AI analysis detailed results
 */

/**
 * Generate HTML for load pattern analysis
 * @param {Object} analysis - Load pattern analysis results
 * @returns {string} HTML content
 */
function generateLoadPatternAnalysisHTML(analysis) {
    if (!analysis) return '<p>No load pattern analysis data available.</p>';
    
    let html = `
        <div class="analysis-stat-grid">
            <div class="analysis-stat">
                <div class="analysis-stat-label">Load Factor</div>
                <div class="analysis-stat-value">${(analysis.loadFactor * 100).toFixed(1)}%</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Peak Periods</div>
                <div class="analysis-stat-value">${analysis.peakPeriods.length}</div>
            </div>
        </div>
    `;
    
    // Add peak periods if any
    if (analysis.peakPeriods && analysis.peakPeriods.length > 0) {
        html += '<h5>Identified Peak Periods</h5><ul class="analysis-detail-list">';
        
        analysis.peakPeriods.forEach((period, index) => {
            html += `
                <li class="analysis-detail-item">
                    <span class="analysis-detail-label">Peak ${index + 1}</span>
                    <span class="analysis-detail-value">${period.startTime} - ${period.endTime} (${period.duration.toFixed(0)} min)</span>
                </li>
            `;
        });
        
        html += '</ul>';
    } else {
        html += '<p>No significant peak periods identified in the analyzed time range.</p>';
    }
    
    // Add load factor interpretation
    html += '<h5>Load Factor Interpretation</h5>';
    
    if (analysis.loadFactor >= 0.9) {
        html += '<p>Excellent load factor. The electrical system is being utilized very efficiently with minimal peaks.</p>';
    } else if (analysis.loadFactor >= 0.8) {
        html += '<p>Good load factor. The electrical system is being utilized efficiently with moderate peaks.</p>';
    } else if (analysis.loadFactor >= 0.7) {
        html += '<p>Fair load factor. There is room for improvement in load distribution.</p>';
    } else if (analysis.loadFactor >= 0.6) {
        html += '<p>Poor load factor. Significant peaks in demand are causing inefficient system utilization.</p>';
    } else {
        html += '<p>Very poor load factor. Extreme peaks in demand are causing highly inefficient system utilization.</p>';
    }
    
    // Add recommendations if any
    if (analysis.recommendations && analysis.recommendations.length > 0) {
        html += '<h5>Recommendations</h5><ul>';
        analysis.recommendations.forEach(recommendation => {
            html += `<li>${recommendation}</li>`;
        });
        html += '</ul>';
    }
    
    return html;
}

/**
 * Generate HTML for efficiency analysis
 * @param {Object} analysis - Efficiency analysis results
 * @returns {string} HTML content
 */
function generateEfficiencyAnalysisHTML(analysis) {
    if (!analysis) return '<p>No efficiency analysis data available.</p>';
    
    let html = `
        <div class="analysis-stat-grid">
            <div class="analysis-stat">
                <div class="analysis-stat-label">Efficiency Score</div>
                <div class="analysis-stat-value">${analysis.powerQuality.score}</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Power Quality Rating</div>
                <div class="analysis-stat-value">${analysis.powerQuality.rating}</div>
            </div>
            <div class="analysis-stat">
                <div class="analysis-stat-label">Estimated Losses</div>
                <div class="analysis-stat-value">${analysis.energyEfficiency.estimatedLosses}</div>
            </div>
        </div>
        
        <h5>Power Quality Details</h5>
        <ul class="analysis-detail-list">
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Power Factor</span>
                <span class="analysis-detail-value">${analysis.powerQuality.powerFactor}</span>
            </li>
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Distortion Power Factor</span>
                <span class="analysis-detail-value">${analysis.powerQuality.distortionPowerFactor}</span>
            </li>
        </ul>
        
        <h5>Energy Efficiency Details</h5>
        <ul class="analysis-detail-list">
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Efficiency Score</span>
                <span class="analysis-detail-value">${analysis.energyEfficiency.score}</span>
            </li>
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Rating</span>
                <span class="analysis-detail-value">${analysis.energyEfficiency.rating}</span>
            </li>
            <li class="analysis-detail-item">
                <span class="analysis-detail-label">Improvement Potential</span>
                <span class="analysis-detail-value">${analysis.energyEfficiency.improvementPotential}</span>
            </li>
        </ul>
    `;
    
    // Add power quality issues if any
    if (analysis.powerQuality.issues && analysis.powerQuality.issues.length > 0) {
        html += '<h5>Power Quality Issues</h5><ul>';
        analysis.powerQuality.issues.forEach(issue => {
            html += `<li>${issue}</li>`;
        });
        html += '</ul>';
    }
    
    // Add recommendations if any
    if (analysis.recommendations && analysis.recommendations.length > 0) {
        html += '<h5>Recommendations</h5><ul>';
        analysis.recommendations.forEach(recommendation => {
            html += `<li>${recommendation}</li>`;
        });
        html += '</ul>';
    }
    
    return html;
}

// Export HTML generator functions to global scope
window.generateLoadPatternAnalysisHTML = generateLoadPatternAnalysisHTML;
window.generateEfficiencyAnalysisHTML = generateEfficiencyAnalysisHTML;
