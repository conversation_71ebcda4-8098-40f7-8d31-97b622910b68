import React, { Component } from 'react';
import styled from 'styled-components';

/**
 * Error Boundary component to catch JavaScript errors anywhere in the child component tree
 */
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
    
    // You can also log the error to a reporting service like Sentry
    // logErrorToService(error, errorInfo);
    
    // Attempt to save any unsaved work or state
    this.saveStateIfPossible();
  }
  
  saveStateIfPossible() {
    try {
      // Try to save any important state to localStorage
      const currentState = this.props.getStateToSave ? this.props.getStateToSave() : null;
      if (currentState) {
        localStorage.setItem('errorRecoveryState', JSON.stringify(currentState));
      }
    } catch (e) {
      console.error('Failed to save state during error:', e);
    }
  }
  
  handleRetry = () => {
    // Reset the error state
    this.setState({ 
      hasError: false,
      error: null,
      errorInfo: null
    });
    
    // Call the onRetry prop if provided
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  }
  
  handleReload = () => {
    // Reload the page
    window.location.reload();
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <ErrorContainer>
          <ErrorIcon className="material-icons-round">error_outline</ErrorIcon>
          <ErrorTitle>Something went wrong</ErrorTitle>
          <ErrorMessage>
            {this.props.message || 'An unexpected error occurred in this component.'}
          </ErrorMessage>
          
          {this.props.showDetails && this.state.error && (
            <ErrorDetails>
              <ErrorDetailsTitle>Error Details:</ErrorDetailsTitle>
              <ErrorDetailsText>{this.state.error.toString()}</ErrorDetailsText>
              {this.state.errorInfo && (
                <ErrorStack>
                  {this.state.errorInfo.componentStack}
                </ErrorStack>
              )}
            </ErrorDetails>
          )}
          
          <ErrorActions>
            <RetryButton onClick={this.handleRetry}>
              <span className="material-icons-round">refresh</span>
              Retry
            </RetryButton>
            <ReloadButton onClick={this.handleReload}>
              <span className="material-icons-round">restart_alt</span>
              Reload Page
            </ReloadButton>
          </ErrorActions>
          
          {this.props.fallback && (
            <FallbackContainer>
              {this.props.fallback}
            </FallbackContainer>
          )}
        </ErrorContainer>
      );
    }

    // If there's no error, render children normally
    return this.props.children;
  }
}

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  margin: 1rem;
  background-color: var(--card-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  text-align: center;
  min-height: 300px;
`;

const ErrorIcon = styled.span`
  font-size: 4rem;
  color: var(--warning-color);
  margin-bottom: 1rem;
`;

const ErrorTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: 1rem;
`;

const ErrorMessage = styled.p`
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  max-width: 600px;
`;

const ErrorDetails = styled.div`
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  width: 100%;
  max-width: 800px;
  text-align: left;
  overflow: auto;
`;

const ErrorDetailsTitle = styled.h3`
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin-bottom: 0.5rem;
`;

const ErrorDetailsText = styled.pre`
  font-family: monospace;
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  white-space: pre-wrap;
  word-break: break-word;
`;

const ErrorStack = styled.pre`
  font-family: monospace;
  font-size: 0.75rem;
  color: var(--text-muted);
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow: auto;
`;

const ErrorActions = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  span {
    font-size: 1.25rem;
  }
`;

const RetryButton = styled(Button)`
  background-color: var(--primary-color);
  color: white;
  border: none;
  
  &:hover {
    background-color: var(--primary-dark);
  }
`;

const ReloadButton = styled(Button)`
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  
  &:hover {
    background-color: var(--hover-color);
  }
`;

const FallbackContainer = styled.div`
  width: 100%;
  max-width: 800px;
  margin-top: 1rem;
`;

export default ErrorBoundary;
