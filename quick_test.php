<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Dashboard Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .button { background: #007bff; color: white; border: none; padding: 15px 30px; margin: 10px; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .result { margin: 15px 0; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Quick Dashboard Test</h1>
        <p>This will send test data and open the dashboard to check if it's working.</p>
        
        <button class="button" onclick="sendTestDataAndOpenDashboard()">Send Test Data & Open Dashboard</button>
        <button class="button success" onclick="sendZeroData()">Send Zero Values</button>
        <button class="button" onclick="checkAPI()">Check API</button>
        
        <div id="results"></div>
        
        <h3>📋 What to Check:</h3>
        <ol>
            <li><strong>Console Errors:</strong> Press F12 in dashboard and check for red errors</li>
            <li><strong>Last Updated:</strong> Should show current time instead of "Loading..."</li>
            <li><strong>Charts:</strong> Should show data points (even if zero)</li>
            <li><strong>Summary Cards:</strong> Should show values instead of "--"</li>
        </ol>
    </div>

    <script>
        async function sendTestDataAndOpenDashboard() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Sending test data...</div>';
            
            const testData = {
                voltage_1: 230.5, voltage_2: 231.2, voltage_3: 229.8,
                current_1: 5.1, current_2: 5.2, current_3: 5.0,
                pf_1: 0.92, pf_2: 0.93, pf_3: 0.91,
                kva_1: 1150.5, kva_2: 1151.2, kva_3: 1149.8,
                total_kva: 3450.5, total_kw: 3277.8, total_kvar: 1076.5,
                frequency: 50.1
            };
            
            try {
                const response = await fetch('backend/receive_data.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ Test data sent successfully!</div>
                        <div class="info">Opening dashboard in new tab...</div>
                    `;
                    
                    // Open dashboard in new tab
                    window.open('frontend/advanced_dashboard.php', '_blank');
                    
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Failed to send test data: ${error.message}</div>
                `;
            }
        }
        
        async function sendZeroData() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Sending zero values...</div>';
            
            const zeroData = {
                voltage_1: 0.0, voltage_2: 0.0, voltage_3: 0.0,
                current_1: 0.0, current_2: 0.0, current_3: 0.0,
                pf_1: 0.0, pf_2: 0.0, pf_3: 0.0,
                kva_1: 0.0, kva_2: 0.0, kva_3: 0.0,
                total_kva: 0.0, total_kw: 0.0, total_kvar: 0.0,
                frequency: 0.0
            };
            
            try {
                const response = await fetch('backend/receive_data.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(zeroData)
                });
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ Zero values sent successfully!</div>
                        <div class="info">Dashboard should now show zeros instead of "Loading..."</div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Failed to send zero data: ${error.message}</div>
                `;
            }
        }
        
        async function checkAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Checking API...</div>';
            
            try {
                const response = await fetch('backend/get_latest_data.php');
                const data = await response.text();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ API is working!</div>
                        <details>
                            <summary>API Response:</summary>
                            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">${data}</pre>
                        </details>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ API check failed: ${error.message}</div>
                `;
            }
        }
    </script>
</body>
</html>
