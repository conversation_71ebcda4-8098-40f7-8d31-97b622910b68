<?php
// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3-Phase Data Simulator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #2c3e50;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #1a73e8;
            margin-top: 0;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            background-color: #1a73e8;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(26, 115, 232, 0.2);
            margin-top: 10px;
        }

        button:hover {
            background-color: #1557b0;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(26, 115, 232, 0.3);
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            max-height: 300px;
            overflow-y: auto;
        }

        .status-line {
            margin-bottom: 5px;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .success {
            color: #28a745;
        }

        .error {
            color: #dc3545;
        }

        .info {
            color: #1a73e8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>3-Phase Data Simulator</h1>
        <p>Use this tool to generate simulated data for the 3-Phase Data Logger System</p>

        <div class="form-group">
            <label for="duration">Simulation Duration (seconds)</label>
            <input type="number" id="duration" value="60" min="1" max="3600">
        </div>

        <div class="form-group">
            <label for="interval">Interval Between Data Points (seconds)</label>
            <input type="number" id="interval" value="3" min="1" max="60">
        </div>

        <div class="form-group">
            <label for="serverUrl">Server URL</label>
            <input type="text" id="serverUrl" value="backend/receive_data.php">
        </div>

        <button id="startBtn">Start Simulation</button>
        <button id="stopBtn" style="background-color: #dc3545; display: none;">Stop Simulation</button>

        <div class="status" id="status">
            <div class="status-line info">Ready to start simulation...</div>
        </div>
    </div>

    <script>
        // DOM elements
        const durationInput = document.getElementById('duration');
        const intervalInput = document.getElementById('interval');
        const serverUrlInput = document.getElementById('serverUrl');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const statusDiv = document.getElementById('status');

        // Base values for simulation
        const baseVoltage = 400;
        const baseCurrent = 50;
        const basePF = 0.9;
        const baseKVA = 20;
        const baseFrequency = 50;

        // Simulation variables
        let simulationRunning = false;
        let simulationInterval;
        let count = 0;
        let startTime;

        // Add a status message
        function addStatus(message, type = 'info') {
            const statusLine = document.createElement('div');
            statusLine.className = `status-line ${type}`;
            statusLine.textContent = message;
            statusDiv.appendChild(statusLine);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }

        // Generate a data point
        function generateDataPoint() {
            const angle = count * 0.1; // For creating sine wave variations

            // Create simulated data with some randomness and sine wave patterns
            const data = {
                voltage_1: baseVoltage + Math.sin(angle) * 10 + (Math.random() * 10 - 5) / 10,
                voltage_2: baseVoltage + Math.sin(angle + 2.09) * 10 + (Math.random() * 10 - 5) / 10, // 120° phase shift
                voltage_3: baseVoltage + Math.sin(angle + 4.19) * 10 + (Math.random() * 10 - 5) / 10, // 240° phase shift

                current_1: baseCurrent + Math.sin(angle) * 5 + (Math.random() * 20 - 10) / 10,
                current_2: baseCurrent + Math.sin(angle + 2.09) * 5 + (Math.random() * 20 - 10) / 10,
                current_3: baseCurrent + Math.sin(angle + 4.19) * 5 + (Math.random() * 20 - 10) / 10,

                pf_1: basePF + Math.sin(angle) * 0.05 + (Math.random() * 10 - 5) / 100,
                pf_2: basePF + Math.sin(angle + 2.09) * 0.05 + (Math.random() * 10 - 5) / 100,
                pf_3: basePF + Math.sin(angle + 4.19) * 0.05 + (Math.random() * 10 - 5) / 100,

                kva_1: baseKVA + Math.sin(angle) * 2 + (Math.random() * 10 - 5) / 10,
                kva_2: baseKVA + Math.sin(angle + 2.09) * 2 + (Math.random() * 10 - 5) / 10,
                kva_3: baseKVA + Math.sin(angle + 4.19) * 2 + (Math.random() * 10 - 5) / 10,
            };

            // Calculate total values
            const totalKVA = data.kva_1 + data.kva_2 + data.kva_3;
            const totalKW = totalKVA * ((data.pf_1 + data.pf_2 + data.pf_3) / 3);
            const totalKVAR = Math.sqrt(Math.pow(totalKVA, 2) - Math.pow(totalKW, 2));

            // Add total values to data
            data.total_kva = totalKVA;
            data.total_kw = totalKW;
            data.total_kvar = totalKVAR;

            // Add frequency with small variations
            data.frequency = baseFrequency + Math.sin(angle) * 0.1 + (Math.random() * 10 - 5) / 100;

            return data;
        }

        // Send a data point to the server
        async function sendDataPoint() {
            if (!simulationRunning) return;

            const data = generateDataPoint();
            count++;

            addStatus(`Sending data point ${count}...`);

            try {
                const response = await fetch(serverUrlInput.value, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.status === 'success') {
                    addStatus(`Data point ${count} sent successfully: ${result.message}`, 'success');
                } else {
                    addStatus(`Error sending data point ${count}: ${result.message}`, 'error');
                }
            } catch (error) {
                addStatus(`Error sending data point ${count}: ${error.message}`, 'error');
            }

            // Check if simulation should end
            const elapsedTime = (Date.now() - startTime) / 1000;
            const duration = parseInt(durationInput.value);

            if (elapsedTime >= duration) {
                stopSimulation();
                addStatus(`Simulation completed. Generated ${count} data points.`, 'success');
            }
        }

        // Start the simulation
        function startSimulation() {
            // Reset simulation state
            count = 0;
            startTime = Date.now();
            simulationRunning = true;

            // Update UI
            startBtn.style.display = 'none';
            stopBtn.style.display = 'inline-block';
            durationInput.disabled = true;
            intervalInput.disabled = true;
            serverUrlInput.disabled = true;

            // Clear status
            statusDiv.innerHTML = '';

            // Log simulation start
            const duration = parseInt(durationInput.value);
            const interval = parseInt(intervalInput.value);
            addStatus(`Starting simulation for ${duration} seconds with ${interval} second intervals`);

            // Send first data point immediately
            sendDataPoint();

            // Set up interval for subsequent data points
            simulationInterval = setInterval(sendDataPoint, interval * 1000);
        }

        // Stop the simulation
        function stopSimulation() {
            simulationRunning = false;
            clearInterval(simulationInterval);

            // Update UI
            startBtn.style.display = 'inline-block';
            stopBtn.style.display = 'none';
            durationInput.disabled = false;
            intervalInput.disabled = false;
            serverUrlInput.disabled = false;

            addStatus('Simulation stopped', 'info');
        }

        // Event listeners
        startBtn.addEventListener('click', startSimulation);
        stopBtn.addEventListener('click', stopSimulation);
    </script>
</body>
</html>
