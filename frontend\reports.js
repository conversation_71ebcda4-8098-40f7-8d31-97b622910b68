/**
 * Reports Page JavaScript
 * Light mode optimized for Online Data Logger System
 */

// Global variables
let selectedReportType = 'daily';
let selectedParameters = [];
let selectedFormat = 'pdf';
let generatedReports = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Reports page...');
    
    setupEventListeners();
    setDefaultDates();
    loadGeneratedReports();
    initializeParameterSelection();
});

function setupEventListeners() {
    // Report type cards
    document.querySelectorAll('.report-type-card').forEach(card => {
        card.addEventListener('click', function() {
            document.querySelectorAll('.report-type-card').forEach(c => c.classList.remove('active'));
            this.classList.add('active');
            selectedReportType = this.dataset.type;
        });
    });

    // Parameter checkboxes
    document.querySelectorAll('.parameter-checkbox input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedParameters);
    });

    // Format selection
    document.querySelectorAll('.format-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.format-option').forEach(o => o.classList.remove('active'));
            this.classList.add('active');
            selectedFormat = this.dataset.format;
        });
    });

    // Generate report button
    const generateBtn = document.getElementById('generateReportBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', generateReport);
    }

    // Preview report button
    const previewBtn = document.getElementById('previewReportBtn');
    if (previewBtn) {
        previewBtn.addEventListener('click', previewReport);
    }
}

function setDefaultDates() {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    if (startDate) {
        startDate.value = formatDateTimeLocal(yesterday);
    }
    
    if (endDate) {
        endDate.value = formatDateTimeLocal(now);
    }
}

function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

function initializeParameterSelection() {
    // Select all parameters by default
    const checkboxes = document.querySelectorAll('.parameter-checkbox input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedParameters();
}

function updateSelectedParameters() {
    selectedParameters = [];
    document.querySelectorAll('.parameter-checkbox input[type="checkbox"]:checked').forEach(checkbox => {
        selectedParameters.push(checkbox.value);
    });
}

async function generateReport() {
    const startDate = document.getElementById('startDate')?.value;
    const endDate = document.getElementById('endDate')?.value;
    
    if (!startDate || !endDate) {
        showMessage('Please select start and end dates', 'warning');
        return;
    }
    
    if (selectedParameters.length === 0) {
        showMessage('Please select at least one parameter', 'warning');
        return;
    }
    
    showProgress(true);
    showMessage('Generating report...', 'info');
    
    try {
        const reportData = {
            type: selectedReportType,
            start_date: startDate,
            end_date: endDate,
            parameters: selectedParameters,
            format: selectedFormat
        };
        
        const response = await fetch('../backend/generate_report.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(reportData)
        });
        
        if (response.ok) {
            const result = await response.json();
            
            if (result.error) {
                throw new Error(result.error);
            }
            
            // Add to generated reports list
            const newReport = {
                id: Date.now(),
                name: `${selectedReportType}_report_${new Date().toISOString().split('T')[0]}.${selectedFormat}`,
                type: selectedReportType,
                format: selectedFormat,
                generated: new Date().toISOString(),
                size: '2.3 MB',
                url: result.download_url
            };
            
            generatedReports.unshift(newReport);
            updateGeneratedReportsList();
            
            showMessage('Report generated successfully', 'success');
            
            // Auto-download if requested
            if (result.download_url) {
                downloadReport(result.download_url, newReport.name);
            }
            
        } else {
            throw new Error('Failed to generate report');
        }
        
    } catch (error) {
        console.error('Report generation error:', error);
        showMessage('Report generation failed: ' + error.message, 'error');
        
        // Generate sample report for demonstration
        generateSampleReport();
    } finally {
        showProgress(false);
    }
}

function generateSampleReport() {
    // Create a sample report entry
    const sampleReport = {
        id: Date.now(),
        name: `${selectedReportType}_report_${new Date().toISOString().split('T')[0]}.${selectedFormat}`,
        type: selectedReportType,
        format: selectedFormat,
        generated: new Date().toISOString(),
        size: '2.3 MB',
        url: '#'
    };
    
    generatedReports.unshift(sampleReport);
    updateGeneratedReportsList();
    
    showMessage('Sample report generated (backend not available)', 'warning');
}

async function previewReport() {
    showMessage('Generating preview...', 'info');
    
    // For demonstration, show a preview modal
    setTimeout(() => {
        showReportPreview();
        showMessage('Preview ready', 'success');
    }, 1000);
}

function showReportPreview() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content report-preview">
            <div class="modal-header">
                <h3>Report Preview</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="preview-content">
                    <h4>Electrical System Report</h4>
                    <p><strong>Report Type:</strong> ${selectedReportType.charAt(0).toUpperCase() + selectedReportType.slice(1)}</p>
                    <p><strong>Parameters:</strong> ${selectedParameters.join(', ')}</p>
                    <p><strong>Format:</strong> ${selectedFormat.toUpperCase()}</p>
                    
                    <div class="preview-chart">
                        <h5>Sample Data Visualization</h5>
                        <canvas id="previewChart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="preview-summary">
                        <h5>Summary Statistics</h5>
                        <table>
                            <tr><td>Average Voltage:</td><td>230.5 V</td></tr>
                            <tr><td>Peak Current:</td><td>15.2 A</td></tr>
                            <tr><td>Power Factor:</td><td>0.92</td></tr>
                            <tr><td>Frequency Range:</td><td>49.8 - 50.2 Hz</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Initialize preview chart
    const ctx = document.getElementById('previewChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['00:00', '06:00', '12:00', '18:00', '24:00'],
                datasets: [{
                    label: 'Voltage',
                    data: [230, 232, 229, 231, 230],
                    borderColor: '#2563eb',
                    backgroundColor: '#2563eb20',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });
    }
    
    // Close modal handlers
    modal.querySelector('.modal-close').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

function loadGeneratedReports() {
    // Load sample reports for demonstration
    generatedReports = [
        {
            id: 1,
            name: 'daily_report_2024-06-21.pdf',
            type: 'daily',
            format: 'pdf',
            generated: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            size: '1.8 MB',
            url: '#'
        },
        {
            id: 2,
            name: 'weekly_report_2024-06-15.xlsx',
            type: 'weekly',
            format: 'xlsx',
            generated: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            size: '3.2 MB',
            url: '#'
        }
    ];
    
    updateGeneratedReportsList();
}

function updateGeneratedReportsList() {
    const reportsList = document.querySelector('.reports-list');
    if (!reportsList) return;
    
    if (generatedReports.length === 0) {
        reportsList.innerHTML = `
            <div class="empty-state">
                <span class="material-icons-round">description</span>
                <h3>No Reports Generated</h3>
                <p>Generate your first report to see it here.</p>
            </div>
        `;
        return;
    }
    
    const reportItems = generatedReports.map(report => `
        <div class="report-item">
            <div class="report-file-icon">
                <span class="material-icons-round">${getFormatIcon(report.format)}</span>
            </div>
            
            <div class="report-info">
                <div class="report-name">${report.name}</div>
                <div class="report-details">
                    ${formatReportType(report.type)} • ${report.size} • ${formatTime(report.generated)}
                </div>
            </div>
            
            <div class="report-actions-list">
                <button class="report-action-btn download" onclick="downloadReport('${report.url}', '${report.name}')">
                    <span class="material-icons-round">download</span>
                </button>
                <button class="report-action-btn delete" onclick="deleteReport(${report.id})">
                    <span class="material-icons-round">delete</span>
                </button>
            </div>
        </div>
    `).join('');
    
    reportsList.innerHTML = reportItems;
}

function downloadReport(url, filename) {
    if (url === '#') {
        showMessage('Sample report - download not available', 'info');
        return;
    }
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    showMessage(`Downloading ${filename}`, 'success');
}

function deleteReport(reportId) {
    if (confirm('Are you sure you want to delete this report?')) {
        generatedReports = generatedReports.filter(report => report.id !== reportId);
        updateGeneratedReportsList();
        showMessage('Report deleted', 'success');
    }
}

function showProgress(show) {
    const progressContainer = document.querySelector('.progress-container');
    if (progressContainer) {
        if (show) {
            progressContainer.classList.add('active');
            animateProgress();
        } else {
            progressContainer.classList.remove('active');
        }
    }
}

function animateProgress() {
    const progressFill = document.querySelector('.progress-fill');
    if (!progressFill) return;
    
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
        }
        progressFill.style.width = progress + '%';
    }, 200);
}

function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    if (!container) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.innerHTML = `
        <span class="material-icons-round">${getMessageIcon(type)}</span>
        ${message}
    `;
    
    container.innerHTML = '';
    container.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

// Helper functions
function getFormatIcon(format) {
    const icons = {
        pdf: 'picture_as_pdf',
        xlsx: 'table_chart',
        csv: 'description',
        docx: 'description'
    };
    return icons[format] || 'description';
}

function formatReportType(type) {
    return type.charAt(0).toUpperCase() + type.slice(1) + ' Report';
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-IN') + ' ' + date.toLocaleTimeString('en-IN', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
}

function getMessageIcon(type) {
    switch (type) {
        case 'success': return 'check_circle';
        case 'error': return 'error';
        case 'warning': return 'warning';
        default: return 'info';
    }
}

// Make functions globally available
window.downloadReport = downloadReport;
window.deleteReport = deleteReport;
