import React, { useState } from 'react';
import styled from 'styled-components';

/**
 * PrintButton component for printing the dashboard
 */
const PrintButton = ({
  title = 'Dashboard Report',
  subtitle = 'Power Monitoring System',
  showOptions = true
}) => {
  const [showPrintOptions, setShowPrintOptions] = useState(false);
  const [printOptions, setPrintOptions] = useState({
    includeTimestamp: true,
    includeHeader: true,
    includeFooter: true,
    landscape: true,
    includeAllData: false
  });

  // Toggle print options panel
  const togglePrintOptions = () => {
    setShowPrintOptions(prev => !prev);
  };

  // Handle option change
  const handleOptionChange = (e) => {
    const { name, checked } = e.target;
    setPrintOptions(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle print action
  const handlePrint = () => {
    // Add print-specific elements to the DOM
    addPrintElements();

    // Print the page
    window.print();

    // Remove print-specific elements after printing
    setTimeout(removePrintElements, 1000);

    // Close options panel
    setShowPrintOptions(false);
  };

  // Add print-specific elements to the DOM
  const addPrintElements = () => {
    // Remove any existing print elements
    removePrintElements();

    // Create container for print elements
    const printContainer = document.createElement('div');
    printContainer.id = 'print-elements';
    printContainer.className = 'print-only';
    document.body.appendChild(printContainer);

    // Add header if enabled
    if (printOptions.includeHeader) {
      const header = document.createElement('div');
      header.className = 'print-header';

      const headerTitle = document.createElement('h1');
      headerTitle.textContent = title;
      header.appendChild(headerTitle);

      const headerSubtitle = document.createElement('p');
      headerSubtitle.textContent = subtitle;
      header.appendChild(headerSubtitle);

      printContainer.appendChild(header);
    }

    // Add timestamp if enabled
    if (printOptions.includeTimestamp) {
      const timestamp = document.createElement('div');
      timestamp.className = 'print-timestamp';

      const now = new Date();
      timestamp.textContent = `Generated on: ${now.toLocaleDateString()} at ${now.toLocaleTimeString()}`;

      printContainer.appendChild(timestamp);
    }

    // Add footer if enabled
    if (printOptions.includeFooter) {
      const footer = document.createElement('div');
      footer.className = 'print-footer';
      footer.textContent = `© ${new Date().getFullYear()} PowerMonitor Pro - Confidential`;

      printContainer.appendChild(footer);
    }

    // Set page orientation
    document.body.classList.toggle('print-landscape', printOptions.landscape);

    // Set data option
    document.body.classList.toggle('print-all-data', printOptions.includeAllData);
  };

  // Remove print-specific elements from the DOM
  const removePrintElements = () => {
    const printElements = document.getElementById('print-elements');
    if (printElements) {
      printElements.remove();
    }

    // Remove orientation class
    document.body.classList.remove('print-landscape');
    document.body.classList.remove('print-all-data');
  };

  return (
    <PrintButtonContainer>
      {showOptions ? (
        <>
          <PrintIconButton onClick={togglePrintOptions} title="Print Dashboard">
            <span className="material-icons-round">print</span>
          </PrintIconButton>

          {showPrintOptions && (
            <PrintOptionsPanel>
              <OptionsHeader>
                <h3>Print Options</h3>
                <CloseButton onClick={togglePrintOptions}>
                  <span className="material-icons-round">close</span>
                </CloseButton>
              </OptionsHeader>

              <OptionsList>
                <OptionItem>
                  <label>
                    <input
                      type="checkbox"
                      name="includeHeader"
                      checked={printOptions.includeHeader}
                      onChange={handleOptionChange}
                    />
                    Include Header
                  </label>
                </OptionItem>

                <OptionItem>
                  <label>
                    <input
                      type="checkbox"
                      name="includeFooter"
                      checked={printOptions.includeFooter}
                      onChange={handleOptionChange}
                    />
                    Include Footer
                  </label>
                </OptionItem>

                <OptionItem>
                  <label>
                    <input
                      type="checkbox"
                      name="includeTimestamp"
                      checked={printOptions.includeTimestamp}
                      onChange={handleOptionChange}
                    />
                    Include Timestamp
                  </label>
                </OptionItem>

                <OptionItem>
                  <label>
                    <input
                      type="checkbox"
                      name="landscape"
                      checked={printOptions.landscape}
                      onChange={handleOptionChange}
                    />
                    Landscape Orientation
                  </label>
                </OptionItem>

                <OptionItem>
                  <label>
                    <input
                      type="checkbox"
                      name="includeAllData"
                      checked={printOptions.includeAllData}
                      onChange={handleOptionChange}
                    />
                    Include All Data Points
                  </label>
                </OptionItem>
              </OptionsList>

              <PrintActionButton onClick={handlePrint}>
                <span className="material-icons-round">print</span>
                Print Dashboard
              </PrintActionButton>
            </PrintOptionsPanel>
          )}
        </>
      ) : (
        <SimplePrintButton onClick={handlePrint} className="print-button">
          <span className="material-icons-round">print</span>
          Print
        </SimplePrintButton>
      )}
    </PrintButtonContainer>
  );
};

const PrintButtonContainer = styled.div`
  position: relative;
`;

const PrintIconButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--card-color);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--animation-medium) ease;

  &:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  span {
    font-size: 1.25rem;
  }
`;

const PrintOptionsPanel = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  width: 250px;
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  z-index: 100;
  margin-top: 0.5rem;
  animation: fadeIn var(--animation-medium) ease forwards;

  &:before {
    content: '';
    position: absolute;
    top: -0.5rem;
    right: 1rem;
    width: 1rem;
    height: 1rem;
    background-color: var(--card-color);
    border-top: 1px solid var(--border-color);
    border-left: 1px solid var(--border-color);
    transform: rotate(45deg);
  }
`;

const OptionsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);

  h3 {
    font-size: 0.9375rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin: 0;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: var(--text-color);
  }

  span {
    font-size: 1.25rem;
  }
`;

const OptionsList = styled.div`
  padding: 0.75rem 1rem;
`;

const OptionItem = styled.div`
  margin-bottom: 0.75rem;

  label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-color);
    cursor: pointer;
  }

  input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-color);
  }
`;

const PrintActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-medium) ease;

  &:hover {
    background-color: var(--primary-dark);
  }

  span {
    font-size: 1.25rem;
  }
`;

const SimplePrintButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--card-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-medium) ease;

  &:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  span {
    font-size: 1.125rem;
  }
`;

export default PrintButton;
