import React, { Component } from 'react';

/**
 * Simple Error Boundary component to catch JavaScript errors
 */
class SimpleErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
  }
  
  handleRetry = () => {
    // Reset the error state
    this.setState({ 
      hasError: false,
      error: null,
      errorInfo: null
    });
    
    // Call the onRetry prop if provided
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <div style={{
          padding: '2rem',
          margin: '1rem',
          backgroundColor: '#fff',
          borderRadius: '0.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          textAlign: 'center',
          minHeight: '300px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            fontSize: '4rem',
            color: '#f59e0b',
            marginBottom: '1rem'
          }}>
            ⚠️
          </div>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: '1rem'
          }}>
            Something went wrong
          </h2>
          <p style={{
            fontSize: '1rem',
            color: '#4b5563',
            marginBottom: '1.5rem',
            maxWidth: '600px'
          }}>
            {this.props.message || 'An unexpected error occurred in this component.'}
          </p>
          
          {this.props.showDetails && this.state.error && (
            <div style={{
              backgroundColor: 'rgba(0, 0, 0, 0.05)',
              padding: '1rem',
              borderRadius: '0.5rem',
              marginBottom: '1.5rem',
              width: '100%',
              maxWidth: '800px',
              textAlign: 'left',
              overflow: 'auto'
            }}>
              <h3 style={{
                fontSize: '1rem',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '0.5rem'
              }}>
                Error Details:
              </h3>
              <pre style={{
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                color: '#4b5563',
                marginBottom: '0.5rem',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}>
                {this.state.error.toString()}
              </pre>
              {this.state.errorInfo && (
                <pre style={{
                  fontFamily: 'monospace',
                  fontSize: '0.75rem',
                  color: '#6b7280',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  maxHeight: '200px',
                  overflow: 'auto'
                }}>
                  {this.state.errorInfo.componentStack}
                </pre>
              )}
            </div>
          )}
          
          <div style={{
            display: 'flex',
            gap: '1rem',
            marginBottom: '1.5rem'
          }}>
            <button 
              onClick={this.handleRetry}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.75rem 1.25rem',
                borderRadius: '0.5rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                backgroundColor: '#2563eb',
                color: 'white',
                border: 'none'
              }}
            >
              🔄 Retry
            </button>
            <button 
              onClick={() => window.location.reload()}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.75rem 1.25rem',
                borderRadius: '0.5rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                backgroundColor: 'transparent',
                color: '#1f2937',
                border: '1px solid #d1d5db'
              }}
            >
              🔄 Reload Page
            </button>
          </div>
          
          {this.props.fallback && (
            <div style={{
              width: '100%',
              maxWidth: '800px',
              marginTop: '1rem'
            }}>
              {this.props.fallback}
            </div>
          )}
        </div>
      );
    }

    // If there's no error, render children normally
    return this.props.children;
  }
}

export default SimpleErrorBoundary;
