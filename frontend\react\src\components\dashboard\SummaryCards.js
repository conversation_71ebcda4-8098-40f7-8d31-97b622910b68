import React from 'react';
import styled from 'styled-components';

const SummaryCards = ({ data, decimalPlaces = 3 }) => {
  // Calculate average voltage
  const avgVoltage = data ?
    ((parseFloat(data.voltage_1) + parseFloat(data.voltage_2) + parseFloat(data.voltage_3)) / 3).toFixed(decimalPlaces) :
    '--';

  // Calculate total current
  const totalCurrent = data ?
    (parseFloat(data.current_1) + parseFloat(data.current_2) + parseFloat(data.current_3)).toFixed(decimalPlaces) :
    '--';

  // Total power
  const totalPower = data ?
    parseFloat(data.total_kw).toFixed(decimalPlaces) :
    '--';

  // Frequency
  const frequency = data ?
    parseFloat(data.frequency).toFixed(decimalPlaces) :
    '--';

  return (
    <SummaryCardsContainer>
      <SummaryCard className="voltage">
        <CardIcon>
          <span className="material-icons-round">bolt</span>
        </CardIcon>
        <CardContent>
          <h3>Average Voltage</h3>
          <CardValue>{avgVoltage} V</CardValue>
          <TrendIndicator className="up">
            <span className="material-icons-round">trending_up</span>
            <span className="trend-value">0.5%</span>
          </TrendIndicator>
        </CardContent>
      </SummaryCard>

      <SummaryCard className="current">
        <CardIcon>
          <span className="material-icons-round">electric_bolt</span>
        </CardIcon>
        <CardContent>
          <h3>Total Current</h3>
          <CardValue>{totalCurrent} A</CardValue>
          <TrendIndicator className="down">
            <span className="material-icons-round">trending_down</span>
            <span className="trend-value">1.2%</span>
          </TrendIndicator>
        </CardContent>
      </SummaryCard>

      <SummaryCard className="power">
        <CardIcon>
          <span className="material-icons-round">power</span>
        </CardIcon>
        <CardContent>
          <h3>Total Power</h3>
          <CardValue>{totalPower} kW</CardValue>
          <TrendIndicator className="up">
            <span className="material-icons-round">trending_up</span>
            <span className="trend-value">2.3%</span>
          </TrendIndicator>
        </CardContent>
      </SummaryCard>

      <SummaryCard className="frequency">
        <CardIcon>
          <span className="material-icons-round">speed</span>
        </CardIcon>
        <CardContent>
          <h3>Frequency</h3>
          <CardValue>{frequency} Hz</CardValue>
          <TrendIndicator className="stable">
            <span className="material-icons-round">trending_flat</span>
            <span className="trend-value">Stable</span>
          </TrendIndicator>
        </CardContent>
      </SummaryCard>
    </SummaryCardsContainer>
  );
};

const SummaryCardsContainer = styled.section`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -1rem;
    left: 0;
    width: 5rem;
    height: 0.25rem;
    background: var(--gradient-primary);
    border-radius: 9999px;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SummaryCard = styled.div`
  background-color: var(--card-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-colored);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  transition: all var(--animation-medium) cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  animation: slideInUp var(--animation-medium) ease forwards;
  animation-delay: calc(var(--animation-fast) * var(--index, 0));
  opacity: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0.25rem;
    height: 100%;
    opacity: 1;
    transition: all var(--animation-medium) ease;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0.25rem;
    opacity: 0;
    transition: opacity var(--animation-medium) ease;
  }

  &.voltage::before, &.voltage::after {
    background: var(--gradient-primary);
  }

  &.current::before, &.current::after {
    background: var(--gradient-accent);
  }

  &.power::before, &.power::after {
    background: var(--gradient-secondary);
  }

  &.frequency::before, &.frequency::after {
    background: var(--gradient-warning);
  }

  &:hover {
    box-shadow: var(--shadow-colored-lg);
    transform: translateY(-3px) scale(1.02);
  }

  &:hover::after {
    opacity: 1;
  }

  &:nth-child(1) {
    --index: 0;
  }

  &:nth-child(2) {
    --index: 1;
  }

  &:nth-child(3) {
    --index: 2;
  }

  &:nth-child(4) {
    --index: 3;
  }
`;

const CardIcon = styled.div`
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.25rem;
  position: relative;
  z-index: 1;
  transition: all var(--animation-medium) ease;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    opacity: 0.15;
    z-index: -1;
    transition: all var(--animation-medium) ease;
  }

  span {
    font-size: 1.75rem;
    transition: all var(--animation-medium) ease;
  }

  ${SummaryCard}:hover & {
    transform: scale(1.1) rotate(5deg);
  }

  ${SummaryCard}.voltage & {
    color: var(--primary-color);

    &::before {
      background: var(--primary-color);
    }
  }

  ${SummaryCard}.current & {
    color: var(--accent-color);

    &::before {
      background: var(--accent-color);
    }
  }

  ${SummaryCard}.power & {
    color: var(--secondary-color);

    &::before {
      background: var(--secondary-color);
    }
  }

  ${SummaryCard}.frequency & {
    color: var(--warning-color);

    &::before {
      background: var(--warning-color);
    }
  }
`;

const CardContent = styled.div`
  flex: 1;

  h3 {
    font-size: 0.875rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    display: inline-block;

    &::after {
      content: '';
      position: absolute;
      bottom: -0.25rem;
      left: 0;
      width: 2rem;
      height: 0.125rem;
      background: var(--border-color);
      border-radius: 9999px;
      transition: all var(--animation-medium) ease;
    }
  }

  ${SummaryCard}:hover h3::after {
    width: 3rem;
    background: var(--primary-light);
  }
`;

const CardValue = styled.p`
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: 0.5rem;
  letter-spacing: -0.02em;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  transition: all var(--animation-medium) ease;

  ${SummaryCard}.voltage & {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
  }

  ${SummaryCard}.current & {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    background-clip: text;
  }

  ${SummaryCard}.power & {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    background-clip: text;
  }

  ${SummaryCard}.frequency & {
    background: var(--gradient-warning);
    -webkit-background-clip: text;
    background-clip: text;
  }
`;

const TrendIndicator = styled.div`
  display: flex;
  align-items: center;
  font-size: 0.8125rem;
  font-weight: var(--font-weight-medium);
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  width: fit-content;
  transition: all var(--animation-medium) ease;
  box-shadow: var(--shadow-sm);

  &.up {
    color: var(--success-color);
    background-color: rgba(5, 150, 105, 0.1);
    border: 1px solid rgba(5, 150, 105, 0.2);
  }

  &.down {
    color: var(--accent-color);
    background-color: rgba(244, 63, 94, 0.1);
    border: 1px solid rgba(244, 63, 94, 0.2);
  }

  &.stable {
    color: var(--warning-color);
    background-color: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
  }

  ${SummaryCard}:hover & {
    transform: translateX(5px);
  }

  span.material-icons-round {
    font-size: 1rem;
    margin-right: 0.25rem;
  }
`;

export default SummaryCards;
