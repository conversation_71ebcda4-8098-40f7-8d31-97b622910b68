/**
 * UI components for AI analysis of electrical system data
 */

/**
 * Initialize the AI analysis UI
 */
function initializeAIAnalysisUI() {
    // Find the existing AI analysis button
    const aiButton = document.getElementById('aiAnalysisBtn');

    if (aiButton) {
        // Add event listener to the existing button
        aiButton.addEventListener('click', showAIAnalysisModal);
    }

    // Create AI analysis modal
    createAIAnalysisModal();
}

/**
 * Create the AI analysis modal
 */
function createAIAnalysisModal() {
    const modalHTML = `
        <div class="modal" id="aiAnalysisModal">
            <div class="modal-content ai-analysis-modal">
                <div class="modal-header">
                    <h2><span class="material-icons-round">psychology</span> AI System Analysis</h2>
                    <button class="close-modal">
                        <span class="material-icons-round">close</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="aiAnalysisLoading" class="ai-loading">
                        <div class="ai-loading-spinner"></div>
                        <p>Analyzing electrical system data...</p>
                    </div>

                    <div id="aiAnalysisContent" class="ai-content" style="display: none;">
                        <div class="ai-summary-section">
                            <h3>System Analysis Summary</h3>
                            <p id="aiSummaryText"></p>
                        </div>

                        <div class="ai-tabs">
                            <div class="ai-tab-header">
                                <button class="ai-tab-button active" data-tab="issues">Issues Found</button>
                                <button class="ai-tab-button" data-tab="recommendations">Recommendations</button>
                                <button class="ai-tab-button" data-tab="details">Detailed Analysis</button>
                            </div>

                            <div class="ai-tab-content active" id="issuesTab">
                                <div id="issuesList" class="issues-list"></div>
                            </div>

                            <div class="ai-tab-content" id="recommendationsTab">
                                <div id="recommendationsList" class="recommendations-list"></div>
                            </div>

                            <div class="ai-tab-content" id="detailsTab">
                                <div class="analysis-details">
                                    <div class="analysis-section" id="voltageAnalysis">
                                        <h4>Voltage Analysis</h4>
                                        <div class="analysis-content"></div>
                                    </div>

                                    <div class="analysis-section" id="currentAnalysis">
                                        <h4>Current Analysis</h4>
                                        <div class="analysis-content"></div>
                                    </div>

                                    <div class="analysis-section" id="powerFactorAnalysis">
                                        <h4>Power Factor Analysis</h4>
                                        <div class="analysis-content"></div>
                                    </div>

                                    <div class="analysis-section" id="frequencyAnalysis">
                                        <h4>Frequency Analysis</h4>
                                        <div class="analysis-content"></div>
                                    </div>

                                    <div class="analysis-section" id="loadPatternAnalysis">
                                        <h4>Load Pattern Analysis</h4>
                                        <div class="analysis-content"></div>
                                    </div>

                                    <div class="analysis-section" id="efficiencyAnalysis">
                                        <h4>Efficiency Analysis</h4>
                                        <div class="analysis-content"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-button secondary" id="exportAnalysisBtn">Export Analysis</button>
                    <button class="modal-button primary" id="runNewAnalysisBtn">Run New Analysis</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to the document
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;
    document.body.appendChild(modalContainer.firstElementChild);

    // Set up event listeners
    document.getElementById('aiAnalysisModal').querySelector('.close-modal').addEventListener('click', function() {
        document.getElementById('aiAnalysisModal').classList.remove('active');
    });

    // Tab switching
    document.querySelectorAll('.ai-tab-button').forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and content
            document.querySelectorAll('.ai-tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.ai-tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            this.classList.add('active');
            document.getElementById(this.dataset.tab + 'Tab').classList.add('active');
        });
    });

    // Run new analysis button
    document.getElementById('runNewAnalysisBtn').addEventListener('click', runAIAnalysis);

    // Export analysis button
    document.getElementById('exportAnalysisBtn').addEventListener('click', exportAIAnalysis);
}

/**
 * Show the AI analysis modal and run analysis
 */
function showAIAnalysisModal() {
    // Show the modal
    document.getElementById('aiAnalysisModal').classList.add('active');

    // Run the analysis
    runAIAnalysis();
}

/**
 * Run AI analysis on the current data
 */
function runAIAnalysis() {
    // Show loading state
    document.getElementById('aiAnalysisLoading').style.display = 'flex';
    document.getElementById('aiAnalysisContent').style.display = 'none';

    // Fetch historical data for analysis
    fetchHistoricalDataForAnalysis()
        .then(data => {
            // Run the analysis
            const analysisResults = analyzeElectricalSystem(data);

            // Display the results
            displayAnalysisResults(analysisResults);

            // Hide loading, show content
            document.getElementById('aiAnalysisLoading').style.display = 'none';
            document.getElementById('aiAnalysisContent').style.display = 'block';
        })
        .catch(error => {
            console.error('Error running AI analysis:', error);

            // Show error message
            document.getElementById('aiAnalysisLoading').innerHTML = `
                <span class="material-icons-round error-icon">error_outline</span>
                <p>Error analyzing data: ${error.message}</p>
                <button class="retry-button" onclick="runAIAnalysis()">Retry</button>
            `;
        });
}

/**
 * Fetch historical data for analysis
 * @returns {Promise<Array>} Promise resolving to array of data points
 */
async function fetchHistoricalDataForAnalysis() {
    // Calculate time range (last hour by default)
    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - (60 * 60 * 1000)); // 1 hour ago

    // Format dates for API
    const startTimeStr = startTime.toISOString();
    const endTimeStr = endTime.toISOString();

    try {
        // Fetch historical data
        const response = await fetch(`../backend/get_historical_data.php?start=${startTimeStr}&end=${endTimeStr}`);

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        if (data.error) {
            throw new Error(data.error);
        }

        if (!Array.isArray(data) || data.length === 0) {
            throw new Error('No data available for analysis');
        }

        return data;
    } catch (error) {
        console.error('Error fetching historical data:', error);
        throw error;
    }
}

/**
 * Display analysis results in the UI
 * @param {Object} results - Analysis results
 */
function displayAnalysisResults(results) {
    if (results.status !== 'success') {
        document.getElementById('aiAnalysisLoading').innerHTML = `
            <span class="material-icons-round error-icon">error_outline</span>
            <p>${results.message || 'Error analyzing data'}</p>
            <button class="retry-button" onclick="runAIAnalysis()">Retry</button>
        `;
        return;
    }

    // Display summary
    document.getElementById('aiSummaryText').textContent = results.insights.summary;

    // Display issues
    const issuesList = document.getElementById('issuesList');
    issuesList.innerHTML = '';

    if (results.insights.issues.length === 0) {
        issuesList.innerHTML = '<div class="no-issues">No issues detected in the electrical system.</div>';
    } else {
        results.insights.issues.forEach(issue => {
            const issueElement = document.createElement('div');
            issueElement.className = `issue-item ${issue.severity}`;
            issueElement.innerHTML = `
                <div class="issue-header">
                    <span class="issue-severity">${issue.severity}</span>
                    <span class="issue-parameter">${issue.parameter}</span>
                </div>
                <div class="issue-title">${issue.issue}</div>
                <div class="issue-details">${issue.details}</div>
            `;
            issuesList.appendChild(issueElement);
        });
    }

    // Display recommendations
    const recommendationsList = document.getElementById('recommendationsList');
    recommendationsList.innerHTML = '';

    if (results.insights.recommendations.length === 0) {
        recommendationsList.innerHTML = '<div class="no-recommendations">No recommendations available.</div>';
    } else {
        results.insights.recommendations.forEach((recommendation, index) => {
            const recommendationElement = document.createElement('div');
            recommendationElement.className = 'recommendation-item';
            recommendationElement.innerHTML = `
                <div class="recommendation-number">${index + 1}</div>
                <div class="recommendation-text">${recommendation}</div>
            `;
            recommendationsList.appendChild(recommendationElement);
        });
    }

    // Display detailed analysis
    displayDetailedAnalysis(results.analyses);
}

/**
 * Display detailed analysis in the UI
 * @param {Object} analyses - Detailed analysis results
 */
function displayDetailedAnalysis(analyses) {
    // Voltage analysis
    const voltageContent = document.querySelector('#voltageAnalysis .analysis-content');
    voltageContent.innerHTML = generateVoltageAnalysisHTML(analyses.voltage);

    // Current analysis
    const currentContent = document.querySelector('#currentAnalysis .analysis-content');
    currentContent.innerHTML = generateCurrentAnalysisHTML(analyses.current);

    // Power factor analysis
    const pfContent = document.querySelector('#powerFactorAnalysis .analysis-content');
    pfContent.innerHTML = generatePowerFactorAnalysisHTML(analyses.powerFactor);

    // Frequency analysis
    const frequencyContent = document.querySelector('#frequencyAnalysis .analysis-content');
    frequencyContent.innerHTML = generateFrequencyAnalysisHTML(analyses.frequency);

    // Load pattern analysis
    const loadPatternContent = document.querySelector('#loadPatternAnalysis .analysis-content');
    loadPatternContent.innerHTML = generateLoadPatternAnalysisHTML(analyses.loadPattern);

    // Efficiency analysis
    const efficiencyContent = document.querySelector('#efficiencyAnalysis .analysis-content');
    efficiencyContent.innerHTML = generateEfficiencyAnalysisHTML(analyses.efficiency);
}

/**
 * Export AI analysis results
 */
function exportAIAnalysis() {
    // Implementation will be added later
    alert('Export functionality will be implemented in a future update.');
}

// Initialize the AI analysis UI when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit to ensure the dashboard is fully loaded
    setTimeout(initializeAIAnalysisUI, 1000);
});
