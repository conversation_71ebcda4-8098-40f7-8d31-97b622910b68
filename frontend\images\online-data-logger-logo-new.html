<!DOCTYPE html>
<html>
<head>
    <title>Logo Converter</title>
    <script>
        window.onload = function() {
            const svg = document.getElementById('logo-svg');
            const canvas = document.getElementById('logo-canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            // Set canvas size
            canvas.width = 400;
            canvas.height = 300;
            
            // Convert SVG to data URL
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                // Draw image on canvas
                ctx.drawImage(img, 0, 0, 400, 300);
                
                // Convert to PNG
                const pngUrl = canvas.toDataURL('image/png');
                document.getElementById('download-link').href = pngUrl;
                document.getElementById('download-link').style.display = 'block';
                
                // Display the image
                const resultImg = document.getElementById('result-img');
                resultImg.src = pngUrl;
                resultImg.style.display = 'block';
            };
            
            img.src = url;
        };
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
        }
        #logo-svg {
            display: none;
        }
        #logo-canvas {
            display: none;
        }
        #result-img {
            max-width: 400px;
            border: 1px solid #ccc;
            margin: 20px auto;
            display: none;
        }
        #download-link {
            display: none;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #007A8C;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Online Data Logger Logo</h1>
    
    <svg id="logo-svg" width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
      <!-- Background -->
      <rect width="400" height="300" fill="#007A8C" />
      
      <!-- Grid Pattern -->
      <g opacity="0.2">
        <path d="M0 0 L400 0" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 20 L400 20" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 40 L400 40" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 60 L400 60" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 80 L400 80" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 100 L400 100" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 120 L400 120" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 140 L400 140" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 160 L400 160" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 180 L400 180" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 200 L400 200" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 220 L400 220" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 240 L400 240" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 260 L400 260" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 280 L400 280" stroke="#FFFFFF" stroke-width="1" />
        <path d="M0 300 L400 300" stroke="#FFFFFF" stroke-width="1" />
        
        <path d="M0 0 L0 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M20 0 L20 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M40 0 L40 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M60 0 L60 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M80 0 L80 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M100 0 L100 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M120 0 L120 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M140 0 L140 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M160 0 L160 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M180 0 L180 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M200 0 L200 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M220 0 L220 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M240 0 L240 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M260 0 L260 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M280 0 L280 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M300 0 L300 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M320 0 L320 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M340 0 L340 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M360 0 L360 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M380 0 L380 300" stroke="#FFFFFF" stroke-width="1" />
        <path d="M400 0 L400 300" stroke="#FFFFFF" stroke-width="1" />
      </g>
      
      <!-- Hexagon with Circuit Design -->
      <g transform="translate(200, 150) scale(0.9)">
        <!-- Hexagon Outline -->
        <path d="M0,-80 L70,-40 L70,40 L0,80 L-70,40 L-70,-40 Z" stroke="#5ECCE9" stroke-width="4" fill="none" />
        
        <!-- Circuit Nodes and Connections -->
        <circle cx="0" cy="0" r="15" fill="#5ECCE9" />
        <circle cx="30" cy="-30" r="10" fill="#5ECCE9" />
        <circle cx="-30" cy="-20" r="8" fill="#5ECCE9" />
        <circle cx="-40" cy="20" r="7" fill="#5ECCE9" />
        <circle cx="40" cy="30" r="9" fill="#5ECCE9" />
        <circle cx="20" cy="40" r="6" fill="#5ECCE9" />
        <circle cx="-20" cy="30" r="8" fill="#5ECCE9" />
        <circle cx="50" cy="-10" r="5" fill="#5ECCE9" />
        <circle cx="-50" cy="-10" r="4" fill="#5ECCE9" />
        <circle cx="10" cy="-50" r="6" fill="#5ECCE9" />
        <circle cx="-10" cy="-40" r="5" fill="#5ECCE9" />
        <circle cx="35" cy="10" r="4" fill="#5ECCE9" />
        
        <!-- Circuit Lines -->
        <path d="M0,0 L30,-30" stroke="#5ECCE9" stroke-width="2" />
        <path d="M0,0 L-30,-20" stroke="#5ECCE9" stroke-width="2" />
        <path d="M0,0 L-40,20" stroke="#5ECCE9" stroke-width="2" />
        <path d="M0,0 L40,30" stroke="#5ECCE9" stroke-width="2" />
        <path d="M0,0 L20,40" stroke="#5ECCE9" stroke-width="2" />
        <path d="M0,0 L-20,30" stroke="#5ECCE9" stroke-width="2" />
        <path d="M30,-30 L50,-10" stroke="#5ECCE9" stroke-width="2" />
        <path d="M-30,-20 L-50,-10" stroke="#5ECCE9" stroke-width="2" />
        <path d="M-30,-20 L-10,-40" stroke="#5ECCE9" stroke-width="2" />
        <path d="M30,-30 L10,-50" stroke="#5ECCE9" stroke-width="2" />
        <path d="M40,30 L35,10" stroke="#5ECCE9" stroke-width="2" />
        <path d="M20,40 L35,10" stroke="#5ECCE9" stroke-width="2" />
      </g>
      
      <!-- Text: ONLINE DATA LOGGER -->
      <g transform="translate(200, 240)">
        <text x="0" y="0" font-family="Arial, sans-serif" font-size="36" font-weight="800" fill="#FFFFFF" text-anchor="middle">ONLINE DATA LOGGER</text>
      </g>
    </svg>
    
    <canvas id="logo-canvas"></canvas>
    <img id="result-img" alt="Online Data Logger Logo" />
    <a id="download-link" href="#" download="online-data-logger-logo.png">Download PNG Logo</a>
    
    <p>Right-click on the image and select "Save Image As..." to download the logo.</p>
</body>
</html>
