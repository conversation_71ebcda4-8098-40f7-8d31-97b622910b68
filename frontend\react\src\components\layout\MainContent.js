import React, { useState, useEffect, useCallback, useRef } from 'react';
import styled from 'styled-components';
import TopBar from './TopBar';
import SummaryCards from '../dashboard/SummaryCards';
import DashboardGrid from '../dashboard/DashboardGrid';
import axios from 'axios';
import { useDashboard } from '../../contexts/DashboardContext';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import { debounce, aggregateData } from '../../utils/performanceUtils';
import { cacheApiResponse, clearApiCache } from '../../utils/cacheUtils';

const MainContent = ({
  settings,
  onExportClick,
  addNotification,
  updateStatus,
  toggleFullscreenWidget,
  isMobileView
}) => {
  const { dashboardConfig, editMode } = useDashboard();
  const { analyticsConfig, processData } = useAnalytics();
  const [latestData, setLatestData] = useState(null);
  const [allData, setAllData] = useState([]);
  const [isPaused, setIsPaused] = useState(false);
  const [timeDisplay, setTimeDisplay] = useState('Current');
  const [lastUpdatedTime, setLastUpdatedTime] = useState('Loading...');
  const [dataUpdateInterval, setDataUpdateInterval] = useState(null);

  // Reference to track if component is mounted
  const isMounted = useRef(true);

  // Set isMounted to false when component unmounts
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Fetch data from the backend with debounce and caching for performance
  const fetchData = useCallback(debounce(async () => {
    if (isPaused) return;

    try {
      // Use dashboard config for records count
      const recordsToFetch = dashboardConfig.timeWindow * 60; // 1 record per second

      // Define the fetch function
      const fetchFromApi = async () => {
        // Add cache-busting parameter
        const timestamp = new Date().getTime();
        const response = await axios.get(`../backend/get_latest_data.php?_=${timestamp}&records=${recordsToFetch}`);
        return response.data;
      };

      // Use caching for live data (short cache duration for live data - 10 seconds)
      const data = await cacheApiResponse(
        '../backend/get_latest_data.php',
        { records: recordsToFetch },
        fetchFromApi,
        10 * 1000 // 10 seconds cache for live data
      );

      // Check if component is still mounted before updating state
      if (!isMounted.current) return;

      // If we got an array of records, use the most recent one
      const latest = Array.isArray(data) ? data[0] : data;

      // Update timestamp display
      updateLastUpdatedTime(latest.timestamp);

      // Update latest data
      setLatestData(latest);

      // Update all data
      if (Array.isArray(data)) {
        // Process data in reverse order to add oldest points first
        const reversedData = data.reverse();

        // Optimize data for performance if needed
        const optimizedData = window.innerWidth < 1200 ?
          aggregateData(reversedData, 100) :
          reversedData;

        // Apply analytics processing if enabled
        const { processedData, analytics } = processData(optimizedData);

        setAllData(processedData);
      } else {
        setAllData([latest]);
      }

      // Update status (but don't show notification for regular updates)
      updateStatus('Data updated successfully', 'success');
    } catch (error) {
      console.error('Error fetching data:', error);
      updateStatus('Failed to fetch data', 'error');

      // If there's an error, clear the cache for this endpoint to force a fresh fetch next time
      clearApiCache('../backend/get_latest_data.php');
    }
  }, 100), [isPaused, dashboardConfig.timeWindow, processData]);

  // Update the last updated time display
  const updateLastUpdatedTime = (timestamp) => {
    if (!timestamp) return;

    // Parse the timestamp
    const date = new Date(timestamp);

    // Format the time in IST
    const formattedTime = date.toLocaleTimeString('en-IN', {
      timeZone: 'Asia/Kolkata',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      day: '2-digit',
      month: 'short'
    });

    // Update the display
    setLastUpdatedTime(formattedTime);
  };

  // Navigate to previous hour
  const navigateToPreviousHour = () => {
    // Pause all charts
    setIsPaused(true);

    // Update time display
    setTimeDisplay('Previous Hour');

    // Load historical data for the previous hour
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));
    const twoHoursAgo = new Date(now.getTime() - (2 * 60 * 60 * 1000));

    loadHistoricalData(twoHoursAgo, oneHourAgo);
  };

  // Navigate to next hour
  const navigateToNextHour = () => {
    // Get current time
    const now = new Date();

    // If we're already at current time, resume live updates
    if (timeDisplay === 'Current') {
      setIsPaused(false);
      fetchData();
      return;
    }

    // If we're looking at previous data, move forward
    if (timeDisplay === 'Previous Hour') {
      setTimeDisplay('Current');
      setIsPaused(false);
      fetchData();
    }
  };

  // Load historical data with caching
  const loadHistoricalData = async (startTime, endTime) => {
    try {
      // Show loading notification
      addNotification('Loading historical data...', 'info');

      // Format dates for API
      const startTimeStr = startTime.toISOString();
      const endTimeStr = endTime.toISOString();

      // Define the fetch function
      const fetchFromApi = async () => {
        const response = await axios.get(`../backend/get_historical_data.php?start=${startTimeStr}&end=${endTimeStr}`);
        return response.data;
      };

      // Use caching for historical data (longer cache duration - 5 minutes)
      const data = await cacheApiResponse(
        '../backend/get_historical_data.php',
        { start: startTimeStr, end: endTimeStr },
        fetchFromApi,
        5 * 60 * 1000 // 5 minutes cache for historical data
      );

      // Check if component is still mounted before updating state
      if (!isMounted.current) return;

      if (data.error) {
        throw new Error(data.error);
      }

      // Update all data
      if (Array.isArray(data)) {
        // Process data in reverse order to add oldest points first
        const reversedData = data.reverse();

        // Optimize data for performance if needed
        const optimizedData = window.innerWidth < 1200 || data.length > 1000 ?
          aggregateData(reversedData, 200) :
          reversedData;

        // Apply analytics processing if enabled
        const { processedData, analytics } = processData(optimizedData);

        setAllData(processedData);
        setLatestData(data[0]);
        updateLastUpdatedTime(data[0].timestamp);

        addNotification(`Historical data loaded successfully (${data.length} points)`, 'success');
      } else {
        addNotification('No historical data found for the selected time range', 'warning');
      }
    } catch (error) {
      console.error('Error loading historical data:', error);
      addNotification('Failed to load historical data: ' + error.message, 'error');

      // If there's an error, clear the cache for this endpoint to force a fresh fetch next time
      clearApiCache('../backend/get_historical_data.php');
    }
  };

  // Apply auto range to all charts
  const applyAutoRange = () => {
    // This will be handled by the chart components
    addNotification('Auto range applied to all charts', 'success');
  };

  // Start data updates
  useEffect(() => {
    // Fetch data immediately
    fetchData();

    // Set up interval for regular updates
    const interval = setInterval(fetchData, dashboardConfig.refreshRate * 1000);
    setDataUpdateInterval(interval);

    // Clean up on unmount
    return () => {
      clearInterval(interval);
    };
  }, [dashboardConfig.refreshRate, isPaused, fetchData]);

  // Update when analytics config changes
  useEffect(() => {
    if (!isPaused && allData.length > 0) {
      // Re-process existing data with new analytics settings
      const { processedData } = processData(allData);
      setAllData(processedData);
    }
  }, [analyticsConfig, isPaused, processData]);

  return (
    <MainContentContainer className={`main-content ${editMode ? 'edit-mode' : ''}`}>
      <TopBar
        lastUpdatedTime={lastUpdatedTime}
        timeDisplay={timeDisplay}
        onPrevHourClick={navigateToPreviousHour}
        onNextHourClick={navigateToNextHour}
        onAutoRangeClick={applyAutoRange}
        onExportClick={onExportClick}
        isMobileView={isMobileView}
      />

      <SummaryCards
        data={latestData}
        decimalPlaces={dashboardConfig.decimalPlaces}
      />

      <DashboardGrid
        data={allData}
        latestData={latestData}
        settings={{
          ...settings,
          ...dashboardConfig,
          analyticsConfig
        }}
        isPaused={isPaused}
        setIsPaused={setIsPaused}
        toggleFullscreenWidget={toggleFullscreenWidget}
        editMode={editMode}
        isMobileView={isMobileView}
      />
    </MainContentContainer>
  );
};

const MainContentContainer = styled.main`
  flex: 1;
  margin-left: 280px;
  padding: 1.5rem;
  transition: all var(--animation-medium) ease;
  max-width: calc(100% - 280px);
  position: relative;

  &.edit-mode {
    background-color: rgba(37, 99, 235, 0.05);

    &::before {
      content: 'Edit Mode Active';
      position: fixed;
      top: 1rem;
      right: 1rem;
      background-color: var(--primary-color);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-size: 0.875rem;
      font-weight: var(--font-weight-medium);
      z-index: 100;
      box-shadow: var(--shadow-md);
    }
  }

  .sidebar-collapsed + & {
    margin-left: 5rem;
    max-width: calc(100% - 5rem);
  }

  @media (max-width: 992px) {
    margin-left: 5rem;
    max-width: calc(100% - 5rem);
  }

  @media (max-width: 768px) {
    padding: 1rem;
    margin-left: 0;
    max-width: 100%;
  }
`;

export default MainContent;
