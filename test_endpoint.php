<?php
// Test script to verify the receive_data.php endpoint is accessible

$url = "http://localhost/online%20data%20logger/backend/receive_data.php";
$data = [
    "voltage_1" => 230.5,
    "voltage_2" => 231.2,
    "voltage_3" => 229.8,
    "current_1" => 5.1,
    "current_2" => 5.2,
    "current_3" => 5.0,
    "pf_1" => 0.92,
    "pf_2" => 0.93,
    "pf_3" => 0.91,
    "kva_1" => 1150.5,
    "kva_2" => 1151.2,
    "kva_3" => 1149.8,
    "total_kva" => 3450.5,
    "total_kw" => 3277.8,
    "total_kvar" => 1076.5,
    "frequency" => 50.1
];

$options = [
    'http' => [
        'header'  => "Content-type: application/json\r\n",
        'method'  => 'POST',
        'content' => json_encode($data)
    ]
];

$context  = stream_context_create($options);
$result = file_get_contents($url, false, $context);

echo "Test result:\n";
echo $result;
?>
