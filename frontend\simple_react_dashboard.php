<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>PowerMonitor Pro - React Dashboard</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">

    <!-- React and Chart.js from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.1/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0/dist/chartjs-adapter-luxon.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2563eb;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            color: #64748b;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 24px;
            color: white;
        }

        .voltage-icon { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
        .current-icon { background: linear-gradient(135deg, #10b981, #059669); }
        .power-icon { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .frequency-icon { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }

        .stat-title {
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 8px;
        }

        .stat-unit {
            font-size: 1rem;
            color: #6b7280;
            font-weight: 500;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 24px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .chart-title .material-icons-round {
            margin-right: 8px;
            color: #2563eb;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #6b7280;
            font-size: 1.1rem;
        }

        .error {
            color: #dc2626;
            text-align: center;
            padding: 20px;
        }

        .last-updated {
            text-align: center;
            color: #6b7280;
            font-size: 0.9rem;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 16px;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">Loading PowerMonitor Pro Dashboard...</div>
    </div>

    <script>
        const { useState, useEffect } = React;

        function Dashboard() {
            const [data, setData] = useState([]);
            const [latestData, setLatestData] = useState(null);
            const [loading, setLoading] = useState(true);
            const [error, setError] = useState(null);
            const [charts, setCharts] = useState({});

            // Fetch data from API
            const fetchData = async () => {
                try {
                    const response = await fetch('../backend/get_latest_data.php?records=20');
                    if (!response.ok) throw new Error('Failed to fetch data');
                    
                    const result = await response.json();
                    if (Array.isArray(result) && result.length > 0) {
                        setData(result);
                        setLatestData(result[0]);
                        setError(null);
                    } else {
                        throw new Error('No data available');
                    }
                } catch (err) {
                    setError(err.message);
                    console.error('Error fetching data:', err);
                } finally {
                    setLoading(false);
                }
            };

            // Initialize charts
            const initializeCharts = () => {
                if (!data.length) return;

                // Voltage Chart
                const voltageCtx = document.getElementById('voltageChart');
                if (voltageCtx && !charts.voltage) {
                    const voltageChart = new Chart(voltageCtx, {
                        type: 'line',
                        data: {
                            labels: data.map(d => new Date(d.timestamp).toLocaleTimeString()),
                            datasets: [
                                {
                                    label: 'Phase 1',
                                    data: data.map(d => parseFloat(d.voltage_1)),
                                    borderColor: '#3b82f6',
                                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                    tension: 0.4
                                },
                                {
                                    label: 'Phase 2',
                                    data: data.map(d => parseFloat(d.voltage_2)),
                                    borderColor: '#10b981',
                                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                    tension: 0.4
                                },
                                {
                                    label: 'Phase 3',
                                    data: data.map(d => parseFloat(d.voltage_3)),
                                    borderColor: '#f59e0b',
                                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                                    tension: 0.4
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'top',
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'Voltage (V)'
                                    }
                                }
                            }
                        }
                    });
                    setCharts(prev => ({ ...prev, voltage: voltageChart }));
                }

                // Current Chart
                const currentCtx = document.getElementById('currentChart');
                if (currentCtx && !charts.current) {
                    const currentChart = new Chart(currentCtx, {
                        type: 'line',
                        data: {
                            labels: data.map(d => new Date(d.timestamp).toLocaleTimeString()),
                            datasets: [
                                {
                                    label: 'Phase 1',
                                    data: data.map(d => parseFloat(d.current_1)),
                                    borderColor: '#8b5cf6',
                                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                                    tension: 0.4
                                },
                                {
                                    label: 'Phase 2',
                                    data: data.map(d => parseFloat(d.current_2)),
                                    borderColor: '#ec4899',
                                    backgroundColor: 'rgba(236, 72, 153, 0.1)',
                                    tension: 0.4
                                },
                                {
                                    label: 'Phase 3',
                                    data: data.map(d => parseFloat(d.current_3)),
                                    borderColor: '#06b6d4',
                                    backgroundColor: 'rgba(6, 182, 212, 0.1)',
                                    tension: 0.4
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'top',
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'Current (A)'
                                    }
                                }
                            }
                        }
                    });
                    setCharts(prev => ({ ...prev, current: currentChart }));
                }
            };

            useEffect(() => {
                fetchData();
                const interval = setInterval(fetchData, 5000); // Update every 5 seconds
                return () => clearInterval(interval);
            }, []);

            useEffect(() => {
                if (data.length > 0) {
                    setTimeout(initializeCharts, 100); // Small delay to ensure DOM is ready
                }
            }, [data]);

            if (loading) {
                return React.createElement('div', { className: 'loading' }, 'Loading dashboard data...');
            }

            if (error) {
                return React.createElement('div', { className: 'error' }, 
                    React.createElement('h2', null, 'Error Loading Data'),
                    React.createElement('p', null, error),
                    React.createElement('button', { 
                        onClick: () => { setLoading(true); fetchData(); },
                        style: { marginTop: '16px', padding: '8px 16px', borderRadius: '8px', border: 'none', background: '#2563eb', color: 'white', cursor: 'pointer' }
                    }, 'Retry')
                );
            }

            if (!latestData) {
                return React.createElement('div', { className: 'loading' }, 'No data available');
            }

            return React.createElement('div', { className: 'dashboard-container' },
                // Header
                React.createElement('div', { className: 'header' },
                    React.createElement('h1', null, '⚡ PowerMonitor Pro'),
                    React.createElement('p', null, 'Real-time Electrical Monitoring Dashboard')
                ),

                // Stats Grid
                React.createElement('div', { className: 'stats-grid' },
                    React.createElement('div', { className: 'stat-card' },
                        React.createElement('div', { className: 'stat-header' },
                            React.createElement('div', { className: 'stat-icon voltage-icon' }, '⚡'),
                            React.createElement('div', { className: 'stat-title' }, 'Voltage Phase 1')
                        ),
                        React.createElement('div', { className: 'stat-value' }, parseFloat(latestData.voltage_1).toFixed(1)),
                        React.createElement('div', { className: 'stat-unit' }, 'Volts')
                    ),
                    React.createElement('div', { className: 'stat-card' },
                        React.createElement('div', { className: 'stat-header' },
                            React.createElement('div', { className: 'stat-icon current-icon' }, '🔌'),
                            React.createElement('div', { className: 'stat-title' }, 'Current Phase 1')
                        ),
                        React.createElement('div', { className: 'stat-value' }, parseFloat(latestData.current_1).toFixed(2)),
                        React.createElement('div', { className: 'stat-unit' }, 'Amperes')
                    ),
                    React.createElement('div', { className: 'stat-card' },
                        React.createElement('div', { className: 'stat-header' },
                            React.createElement('div', { className: 'stat-icon frequency-icon' }, '📊'),
                            React.createElement('div', { className: 'stat-title' }, 'Frequency')
                        ),
                        React.createElement('div', { className: 'stat-value' }, parseFloat(latestData.frequency).toFixed(1)),
                        React.createElement('div', { className: 'stat-unit' }, 'Hz')
                    ),
                    React.createElement('div', { className: 'stat-card' },
                        React.createElement('div', { className: 'stat-header' },
                            React.createElement('div', { className: 'stat-icon power-icon' }, '⚡'),
                            React.createElement('div', { className: 'stat-title' }, 'Power Factor')
                        ),
                        React.createElement('div', { className: 'stat-value' }, parseFloat(latestData.pf_1).toFixed(3)),
                        React.createElement('div', { className: 'stat-unit' }, 'PF')
                    )
                ),

                // Charts Grid
                React.createElement('div', { className: 'charts-grid' },
                    React.createElement('div', { className: 'chart-card' },
                        React.createElement('div', { className: 'chart-title' },
                            React.createElement('span', { className: 'material-icons-round' }, 'bolt'),
                            'Voltage Trends'
                        ),
                        React.createElement('div', { className: 'chart-container' },
                            React.createElement('canvas', { id: 'voltageChart' })
                        )
                    ),
                    React.createElement('div', { className: 'chart-card' },
                        React.createElement('div', { className: 'chart-title' },
                            React.createElement('span', { className: 'material-icons-round' }, 'electrical_services'),
                            'Current Trends'
                        ),
                        React.createElement('div', { className: 'chart-container' },
                            React.createElement('canvas', { id: 'currentChart' })
                        )
                    )
                ),

                // Last Updated
                React.createElement('div', { className: 'last-updated' },
                    'Last updated: ', new Date(latestData.timestamp).toLocaleString()
                )
            );
        }

        // Render the dashboard
        const container = document.getElementById('root');
        const root = ReactDOM.createRoot(container);
        root.render(React.createElement(Dashboard));
    </script>
</body>
</html>
