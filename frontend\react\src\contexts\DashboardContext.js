import React, { createContext, useState, useEffect, useContext } from 'react';
import { defaultLayout, loadLayout, saveLayout, loadDashboardConfig, saveDashboardConfig } from '../utils/layoutUtils';

const DashboardContext = createContext();

export const useDashboard = () => useContext(DashboardContext);

export const DashboardProvider = ({ children }) => {
  const [layout, setLayout] = useState(defaultLayout);
  const [editMode, setEditMode] = useState(false);
  const [activeWidgets, setActiveWidgets] = useState([]);
  const [dashboardConfig, setDashboardConfig] = useState({
    name: 'Main Dashboard',
    refreshRate: 2,
    timeWindow: 1,
    showGridLines: true,
    lineThickness: 1.5,
    decimalPlaces: 3,
    chartTheme: 'professional'
  });
  
  // Load saved layout and config on mount
  useEffect(() => {
    const savedLayout = loadLayout('main');
    if (savedLayout) {
      setLayout(savedLayout);
    }
    
    const savedConfig = loadDashboardConfig('main');
    if (savedConfig) {
      setDashboardConfig(savedConfig);
    }
    
    // Extract active widgets from layout
    const widgetIds = new Set();
    Object.values(savedLayout || defaultLayout).forEach(breakpoint => {
      breakpoint.forEach(item => {
        if (item.i !== 'summaryCards') {
          widgetIds.add(item.i);
        }
      });
    });
    
    setActiveWidgets(Array.from(widgetIds));
  }, []);
  
  // Save layout when it changes
  useEffect(() => {
    if (layout !== defaultLayout) {
      saveLayout('main', layout);
    }
  }, [layout]);
  
  // Save config when it changes
  useEffect(() => {
    saveDashboardConfig('main', dashboardConfig);
  }, [dashboardConfig]);
  
  // Update layout
  const updateLayout = (newLayout) => {
    setLayout(newLayout);
  };
  
  // Toggle edit mode
  const toggleEditMode = () => {
    setEditMode(prev => !prev);
  };
  
  // Add widget to dashboard
  const addWidget = (widgetId, position = { x: 0, y: 0 }) => {
    if (activeWidgets.includes(widgetId)) return;
    
    setActiveWidgets(prev => [...prev, widgetId]);
    
    // Add widget to layout
    setLayout(prev => {
      const newLayout = { ...prev };
      
      // Add to each breakpoint
      Object.keys(newLayout).forEach(breakpoint => {
        const maxY = Math.max(...newLayout[breakpoint].map(item => item.y + item.h));
        
        let width, height;
        switch (breakpoint) {
          case 'lg':
            width = 6;
            height = 8;
            break;
          case 'md':
            width = 5;
            height = 7;
            break;
          case 'sm':
            width = 6;
            height = 6;
            break;
          case 'xs':
            width = 4;
            height = 6;
            break;
          default:
            width = 6;
            height = 8;
        }
        
        newLayout[breakpoint].push({
          i: widgetId,
          x: position.x || 0,
          y: maxY,
          w: width,
          h: height
        });
      });
      
      return newLayout;
    });
  };
  
  // Remove widget from dashboard
  const removeWidget = (widgetId) => {
    if (!activeWidgets.includes(widgetId)) return;
    
    setActiveWidgets(prev => prev.filter(id => id !== widgetId));
    
    // Remove from layout
    setLayout(prev => {
      const newLayout = { ...prev };
      
      // Remove from each breakpoint
      Object.keys(newLayout).forEach(breakpoint => {
        newLayout[breakpoint] = newLayout[breakpoint].filter(item => item.i !== widgetId);
      });
      
      return newLayout;
    });
  };
  
  // Update dashboard config
  const updateDashboardConfig = (newConfig) => {
    setDashboardConfig(prev => ({ ...prev, ...newConfig }));
  };
  
  // Reset dashboard to default
  const resetDashboard = () => {
    setLayout(defaultLayout);
    setActiveWidgets([
      'voltageWidget',
      'currentWidget',
      'pfWidget',
      'kvaWidget',
      'totalPowerWidget',
      'frequencyWidget'
    ]);
    setDashboardConfig({
      name: 'Main Dashboard',
      refreshRate: 2,
      timeWindow: 1,
      showGridLines: true,
      lineThickness: 1.5,
      decimalPlaces: 3,
      chartTheme: 'professional'
    });
  };
  
  return (
    <DashboardContext.Provider value={{
      layout,
      updateLayout,
      editMode,
      toggleEditMode,
      activeWidgets,
      addWidget,
      removeWidget,
      dashboardConfig,
      updateDashboardConfig,
      resetDashboard
    }}>
      {children}
    </DashboardContext.Provider>
  );
};
