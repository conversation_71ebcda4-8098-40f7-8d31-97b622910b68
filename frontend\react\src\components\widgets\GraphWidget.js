import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  Filler
} from 'chart.js';
import 'chartjs-adapter-luxon';
import { DateTime } from 'luxon';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  Filler
);

// Parameter-specific min/max values with industrial standards
const parameterRanges = {
  voltage: { min: 0, max: 500, nominal: 415, alertLow: 380, alertHigh: 450 },
  current: { min: 0, max: 100, nominal: 50, alertHigh: 80 },
  pf: { min: -0.3, max: 1, nominal: 0.95, alertLow: 0.8 },
  kva: { min: 0, max: 50, nominal: 25, alertHigh: 40 },
  totalPower: { min: 0, max: 100, nominal: 50, alertHigh: 85 },
  frequency: { min: 30, max: 60, nominal: 50, alertLow: 49.5, alertHigh: 50.5 }
};

// Enhanced professional color schemes for industrial monitoring systems
const colorSchemes = {
  professional: {
    primary: ['#2563eb', '#10b981', '#f43f5e', '#f59e0b'],
    secondary: ['#60a5fa', '#34d399', '#fb7185', '#fbbf24'],
    gradients: [
      'linear-gradient(90deg, #2563eb, #60a5fa)',
      'linear-gradient(90deg, #10b981, #34d399)',
      'linear-gradient(90deg, #f43f5e, #fb7185)',
      'linear-gradient(90deg, #f59e0b, #fbbf24)'
    ]
  }
};

const GraphWidget = ({
  id,
  title,
  icon,
  chartId,
  labels,
  data,
  latestData,
  dataKeys,
  yAxisLabel,
  parameterType,
  settings,
  isPaused,
  setIsPaused,
  toggleFullscreenWidget,
  formatValue
}) => {
  const [autoScroll, setAutoScroll] = useState(true);
  const chartRef = useRef(null);
  const [instantValues, setInstantValues] = useState([]);

  // Toggle pause state
  const handleTogglePause = () => {
    setIsPaused(prev => !prev);
  };

  // Toggle auto-scroll
  const handleToggleAutoScroll = () => {
    setAutoScroll(prev => !prev);
  };

  // Scroll chart backward
  const handleScrollBackward = () => {
    if (chartRef.current) {
      const chart = chartRef.current;

      // Get current time range
      const currentMin = chart.options.scales.x.min;
      const currentMax = chart.options.scales.x.max;

      if (!currentMin || !currentMax) return;

      // Move backward by 30 seconds
      const newMin = new Date(currentMin.getTime() - (30 * 1000));
      const newMax = new Date(currentMax.getTime() - (30 * 1000));

      // Update chart options
      chart.options.scales.x.min = newMin;
      chart.options.scales.x.max = newMax;
      chart.update();
    }
  };

  // Scroll chart forward
  const handleScrollForward = () => {
    if (chartRef.current) {
      const chart = chartRef.current;

      // Get current time range
      const currentMin = chart.options.scales.x.min;
      const currentMax = chart.options.scales.x.max;

      if (!currentMin || !currentMax) return;

      // Move forward by 30 seconds
      const newMin = new Date(currentMin.getTime() + (30 * 1000));
      const newMax = new Date(currentMax.getTime() + (30 * 1000));

      // Don't scroll past the latest data point
      if (data && data.length > 0) {
        const latestPoint = data[0];
        const latestTime = new Date(latestPoint.timestamp).getTime();

        if (newMax > latestTime) {
          // If we're already at the latest data, don't scroll further
          if (currentMax >= latestTime) return;

          // Adjust to show the latest data
          const timeWindow = currentMax - currentMin;
          const newMin = new Date(latestTime - timeWindow);
          const newMax = new Date(latestTime);

          // Update chart options
          chart.options.scales.x.min = newMin;
          chart.options.scales.x.max = newMax;
          chart.update();
          return;
        }
      }

      // Update chart options
      chart.options.scales.x.min = newMin;
      chart.options.scales.x.max = newMax;
      chart.update();
    }
  };

  // Update instant values
  useEffect(() => {
    if (latestData) {
      const values = dataKeys.map((key, index) => ({
        label: labels[index],
        value: formatValue(latestData[key], key)
      }));
      setInstantValues(values);
    }
  }, [latestData, dataKeys, labels, formatValue]);

  // Prepare chart data
  const chartData = {
    datasets: dataKeys.map((key, index) => ({
      label: labels[index],
      data: data ? data.map(item => ({
        x: new Date(item.timestamp),
        y: parseFloat(item[key])
      })) : [],
      borderColor: colorSchemes.professional.primary[index % colorSchemes.professional.primary.length],
      backgroundColor: 'transparent',
      borderWidth: settings.lineThickness,
      tension: 0.2,
      pointRadius: 0,
      pointHoverRadius: 8,
      pointHoverBackgroundColor: colorSchemes.professional.primary[index % colorSchemes.professional.primary.length],
      pointHoverBorderColor: '#fff',
      pointHoverBorderWidth: 2,
      cubicInterpolationMode: 'monotone',
      capBezierPoints: true,
      spanGaps: true,
      clip: 5,
      borderJoinStyle: 'round',
      borderCapStyle: 'round',
      fill: false,
      shadowColor: colorSchemes.professional.primary[index % colorSchemes.professional.primary.length] + '40',
      shadowBlur: 5,
      shadowOffsetX: 0,
      shadowOffsetY: 4
    }))
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        type: 'time',
        time: {
          unit: 'second',
          displayFormats: {
            second: 'HH:mm:ss',
            minute: 'HH:mm',
            hour: 'HH:mm',
            day: 'MMM d'
          },
          tooltipFormat: 'MMM d, yyyy HH:mm:ss'
        },
        adapters: {
          date: {
            locale: 'en-IN',
            zone: 'Asia/Kolkata' // Set timezone to India
          }
        },
        grid: {
          color: 'rgba(203, 213, 225, 0.2)',
          borderDash: [4, 4],
          drawBorder: false,
          display: settings.showGridLines
        },
        ticks: {
          font: {
            size: 10,
            family: 'Inter, sans-serif',
            weight: '500'
          },
          padding: 8,
          maxRotation: 0,
          autoSkip: true,
          maxTicksLimit: 6,
          color: 'var(--text-secondary)'
        }
      },
      y: {
        beginAtZero: parameterType !== 'pf',
        min: parameterType === 'pf' ? -0.3 :
             parameterType === 'frequency' ? 30 :
             parameterRanges[parameterType]?.min || 0,
        max: parameterType === 'pf' ? 1 :
             parameterType === 'frequency' ? 60 :
             parameterRanges[parameterType]?.max,
        grid: {
          color: 'rgba(203, 213, 225, 0.2)',
          borderDash: [4, 4],
          drawBorder: false,
          z: -1,
          display: settings.showGridLines
        },
        ticks: {
          font: {
            size: 10,
            family: 'Inter, sans-serif',
            weight: '500'
          },
          padding: 6,
          precision: settings.decimalPlaces,
          color: 'var(--text-secondary)'
        }
      }
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        mode: 'nearest',
        intersect: true,
        position: 'nearest',
        caretPadding: 10,
        displayColors: true,
        backgroundColor: 'var(--chart-tooltip-bg)',
        titleColor: 'var(--text-color)',
        bodyColor: 'var(--text-secondary)',
        borderColor: 'var(--chart-tooltip-border)',
        borderWidth: 1,
        padding: 10,
        cornerRadius: 8,
        boxShadow: 'var(--chart-tooltip-shadow)',
        titleFont: {
          family: 'Inter, sans-serif',
          size: 12,
          weight: '600'
        },
        bodyFont: {
          family: 'Inter, sans-serif',
          size: 11,
          weight: '500'
        },
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            return label + formatValue(context.parsed.y, dataKeys[context.datasetIndex]);
          },
          title: function(context) {
            const date = new Date(context[0].parsed.x);
            return date.toLocaleTimeString('en-IN', {
              timeZone: 'Asia/Kolkata',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              day: '2-digit',
              month: 'short'
            });
          },
          labelTextColor: function(context) {
            return colorSchemes.professional.primary[context.datasetIndex % colorSchemes.professional.primary.length];
          }
        }
      }
    },
    animation: {
      duration: 0
    },
    elements: {
      line: {
        tension: 0.1,
        borderWidth: settings.lineThickness,
        capBezierPoints: true,
        borderJoinStyle: 'round',
        fill: false
      },
      point: {
        radius: 0,
        hitRadius: 10,
        hoverRadius: 4
      }
    }
  };

  return (
    <GraphWidgetContainer id={id}>
      <WidgetHeader>
        <WidgetTitle>
          <span className="material-icons-round">{icon}</span>
          <h3>{title}</h3>
        </WidgetTitle>
        <WidgetControls>
          <WidgetControl
            onClick={() => toggleFullscreenWidget(true, title, icon, chartId, chartId)}
            title="Fullscreen"
          >
            <span className="material-icons-round">fullscreen</span>
          </WidgetControl>
          <WidgetControl
            onClick={handleScrollBackward}
            title="Scroll Backward"
          >
            <span className="material-icons-round">arrow_back</span>
          </WidgetControl>
          <WidgetControl
            onClick={handleScrollForward}
            title="Scroll Forward"
          >
            <span className="material-icons-round">arrow_forward</span>
          </WidgetControl>
          <WidgetControl
            onClick={handleTogglePause}
            className={isPaused ? 'paused' : ''}
            title={isPaused ? 'Resume' : 'Pause'}
          >
            <span className="material-icons-round">
              {isPaused ? 'play_arrow' : 'pause'}
            </span>
          </WidgetControl>
          <WidgetControl
            onClick={handleToggleAutoScroll}
            className={autoScroll ? 'active' : ''}
            title={autoScroll ? 'Disable Auto-scroll' : 'Enable Auto-scroll'}
          >
            <span className="material-icons-round">autorenew</span>
          </WidgetControl>
        </WidgetControls>
      </WidgetHeader>
      <WidgetContent>
        <Line
          data={chartData}
          options={chartOptions}
          ref={chartRef}
        />
        <InstantValues>
          {instantValues.map((item, index) => (
            <div key={index}>
              {item.label}: {item.value}
            </div>
          ))}
        </InstantValues>
      </WidgetContent>
    </GraphWidgetContainer>
  );
};

const GraphWidgetContainer = styled.div`
  background-color: var(--card-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-colored);
  overflow: hidden;
  transition: all var(--animation-medium) cubic-bezier(0.34, 1.56, 0.64, 1);
  height: 420px;
  border: 1px solid var(--border-color);
  position: relative;
  animation: fadeIn var(--animation-medium) ease forwards;
  opacity: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0.25rem;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--animation-medium) ease;
    z-index: 1;
  }

  &:hover {
    box-shadow: var(--shadow-colored-lg);
    transform: translateY(-3px);
  }

  &:hover::before {
    opacity: 1;
  }

  @media (max-width: 1400px) {
    height: 380px;
  }

  @media (max-width: 768px) {
    height: 340px;
  }

  @media (max-width: 576px) {
    height: 300px;
  }
`;

const WidgetHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(241, 245, 249, 0.3);
  position: relative;
  z-index: 2;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);

  body.dark-theme & {
    background-color: rgba(30, 41, 59, 0.3);
  }
`;

const WidgetTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;

  span {
    color: var(--primary-color);
    font-size: 1.25rem;
    width: 2.25rem;
    height: 2.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(37, 99, 235, 0.1);
    transition: all var(--animation-medium) ease;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(37, 99, 235, 0.1);
  }

  h3 {
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    letter-spacing: -0.01em;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -0.25rem;
      left: 0;
      width: 0;
      height: 0.125rem;
      background: var(--primary-color);
      border-radius: 9999px;
      transition: width var(--animation-medium) ease;
      opacity: 0.5;
    }
  }

  ${GraphWidgetContainer}:hover & {
    span {
      transform: scale(1.1);
      background-color: rgba(37, 99, 235, 0.15);
      box-shadow: var(--shadow-colored);
    }

    h3::after {
      width: 100%;
    }
  }
`;

const WidgetControls = styled.div`
  display: flex;
  gap: 0.375rem;
  background-color: var(--glass-bg);
  padding: 0.375rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(var(--backdrop-blur));
  -webkit-backdrop-filter: blur(var(--backdrop-blur));
  box-shadow: var(--shadow-sm);
  transition: all var(--animation-medium) ease;

  ${GraphWidgetContainer}:hover & {
    box-shadow: var(--shadow-md);
  }

  @media (max-width: 576px) {
    flex-wrap: wrap;
  }
`;

const WidgetControl = styled.button`
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--animation-medium) cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: currentColor;
    opacity: 0;
    transition: opacity var(--animation-medium) ease;
    z-index: -1;
    border-radius: 50%;
    transform: scale(0.8);
  }

  &:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
  }

  &:hover::before {
    opacity: 0.1;
    transform: scale(1);
  }

  &:active {
    transform: translateY(1px);
  }

  &.paused {
    color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(244, 63, 94, 0.2);
  }

  &.paused::before {
    opacity: 0.1;
    background-color: var(--accent-color);
  }

  &.active {
    color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
  }

  &.active::before {
    opacity: 0.1;
    background-color: var(--primary-color);
  }
`;

const WidgetContent = styled.div`
  height: calc(100% - 4.5rem);
  position: relative;
  padding: 0.75rem;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.05));

  body.dark-theme & {
    background: linear-gradient(to bottom, rgba(15, 23, 42, 0.02), rgba(15, 23, 42, 0.05));
  }
`;

const InstantValues = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: var(--glass-bg);
  padding: 0.875rem 1.125rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-colored);
  z-index: 10;
  font-size: 0.8125rem;
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(var(--backdrop-blur));
  -webkit-backdrop-filter: blur(var(--backdrop-blur));
  max-width: 14rem;
  transition: all var(--animation-medium) cubic-bezier(0.34, 1.56, 0.64, 1);
  transform: translateY(0);
  opacity: 0.9;

  &:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-colored-lg);
    opacity: 1;
  }

  body.dark-theme & {
    background-color: var(--glass-bg);
    border-color: var(--glass-border);
  }

  div {
    display: flex;
    justify-content: space-between;
    gap: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px dashed var(--border-color);
    font-weight: var(--font-weight-medium);

    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }

    &:first-child {
      color: var(--primary-color);
      font-weight: var(--font-weight-semibold);
    }

    &:nth-child(2) {
      color: var(--secondary-color);
    }

    &:nth-child(3) {
      color: var(--accent-color);
    }
  }
`;

export default GraphWidget;
