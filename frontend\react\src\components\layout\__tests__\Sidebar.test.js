import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Sidebar from '../Sidebar';
import { ThemeProvider } from '../../../contexts/ThemeContext';

// Mock the ThemeContext
jest.mock('../../../contexts/ThemeContext', () => ({
  useTheme: () => ({
    darkMode: false,
    toggleDarkMode: jest.fn()
  }),
  ThemeProvider: ({ children }) => <div>{children}</div>
}));

describe('Sidebar', () => {
  const mockProps = {
    onSettingsClick: jest.fn(),
    onCustomizeClick: jest.fn(),
    onAnalyticsClick: jest.fn(),
    onThemeBuilderClick: jest.fn(),
    isMobileView: false
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders correctly', () => {
    render(<Sidebar {...mockProps} />);
    
    // Logo should be rendered
    expect(screen.getByAltText('PowerMonitor Pro')).toBeInTheDocument();
    
    // Navigation items should be rendered
    expect(screen.getByText('Live Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Historical Data')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Customize')).toBeInTheDocument();
    expect(screen.getByText('Theme Builder')).toBeInTheDocument();
    expect(screen.getByText('System Settings')).toBeInTheDocument();
    
    // Footer elements should be rendered
    expect(screen.getByText('Dark Mode')).toBeInTheDocument();
    expect(screen.getByText('System Online')).toBeInTheDocument();
  });
  
  it('calls the correct handlers when navigation items are clicked', () => {
    render(<Sidebar {...mockProps} />);
    
    // Click on Analytics
    fireEvent.click(screen.getByText('Analytics'));
    expect(mockProps.onAnalyticsClick).toHaveBeenCalledTimes(1);
    
    // Click on Customize
    fireEvent.click(screen.getByText('Customize'));
    expect(mockProps.onCustomizeClick).toHaveBeenCalledTimes(1);
    
    // Click on Theme Builder
    fireEvent.click(screen.getByText('Theme Builder'));
    expect(mockProps.onThemeBuilderClick).toHaveBeenCalledTimes(1);
    
    // Click on System Settings
    fireEvent.click(screen.getByText('System Settings'));
    expect(mockProps.onSettingsClick).toHaveBeenCalledTimes(1);
  });
  
  it('collapses in mobile view', () => {
    const { rerender } = render(<Sidebar {...mockProps} isMobileView={true} />);
    
    // Sidebar should have collapsed class
    const sidebar = screen.getByAltText('PowerMonitor Pro').closest('aside');
    expect(sidebar).toHaveClass('sidebar-collapsed');
    
    // Rerender with desktop view
    rerender(<Sidebar {...mockProps} isMobileView={false} />);
    
    // Sidebar should not have collapsed class
    expect(sidebar).not.toHaveClass('sidebar-collapsed');
  });
  
  it('toggles collapse state when collapse button is clicked', () => {
    render(<Sidebar {...mockProps} />);
    
    // Find and click the collapse button
    const collapseButton = screen.getByText('menu_open').closest('button');
    fireEvent.click(collapseButton);
    
    // Sidebar should have collapsed class
    const sidebar = screen.getByAltText('PowerMonitor Pro').closest('aside');
    expect(sidebar).toHaveClass('sidebar-collapsed');
    
    // Icon should change
    expect(screen.getByText('menu')).toBeInTheDocument();
  });
});
