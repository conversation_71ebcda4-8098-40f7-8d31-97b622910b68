/**
 * Styles for AI Analysis UI
 */

/* AI Analysis Modal */
.ai-analysis-modal {
    max-width: 900px;
    width: 90%;
    max-height: 80vh;
}

.ai-analysis-modal .modal-header h2 {
    display: flex;
    align-items: center;
    gap: 10px;
}

.ai-analysis-modal .modal-header .material-icons-round {
    color: #2563eb;
}

.ai-analysis-modal .modal-body {
    max-height: calc(80vh - 140px);
    overflow-y: auto;
}

/* Loading State */
.ai-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.ai-loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(37, 99, 235, 0.2);
    border-radius: 50%;
    border-top-color: #2563eb;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.ai-loading p {
    font-size: 16px;
    color: #64748b;
    margin: 0;
}

.error-icon {
    font-size: 48px;
    color: #ef4444;
    margin-bottom: 20px;
}

.retry-button {
    margin-top: 20px;
    padding: 8px 16px;
    background-color: #2563eb;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.retry-button:hover {
    background-color: #1d4ed8;
}

/* AI Content */
.ai-content {
    padding: 0 10px;
}

.ai-summary-section {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #2563eb;
}

.dark-theme .ai-summary-section {
    background-color: #1e293b;
    border-left: 4px solid #3b82f6;
}

.ai-summary-section h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0f172a;
    font-size: 18px;
}

.dark-theme .ai-summary-section h3 {
    color: #f8fafc;
}

.ai-summary-section p {
    margin: 0;
    line-height: 1.6;
    color: #334155;
}

.dark-theme .ai-summary-section p {
    color: #cbd5e1;
}

/* Tabs */
.ai-tabs {
    margin-bottom: 20px;
}

.ai-tab-header {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.dark-theme .ai-tab-header {
    border-bottom: 1px solid #334155;
}

.ai-tab-button {
    padding: 10px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: #64748b;
    transition: all 0.2s ease;
}

.ai-tab-button:hover {
    color: #2563eb;
}

.ai-tab-button.active {
    color: #2563eb;
    border-bottom-color: #2563eb;
}

.dark-theme .ai-tab-button {
    color: #94a3b8;
}

.dark-theme .ai-tab-button:hover,
.dark-theme .ai-tab-button.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
}

.ai-tab-content {
    display: none;
}

.ai-tab-content.active {
    display: block;
}

/* Issues List */
.issues-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.issue-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 15px;
    border-left: 4px solid #64748b;
}

.dark-theme .issue-item {
    background-color: #1e293b;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.issue-item.high {
    border-left-color: #ef4444;
}

.issue-item.medium {
    border-left-color: #f59e0b;
}

.issue-item.low {
    border-left-color: #10b981;
}

.issue-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.issue-severity {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
    color: white;
}

.issue-item.high .issue-severity {
    background-color: #ef4444;
}

.issue-item.medium .issue-severity {
    background-color: #f59e0b;
}

.issue-item.low .issue-severity {
    background-color: #10b981;
}

.issue-parameter {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

.dark-theme .issue-parameter {
    color: #94a3b8;
}

.issue-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: #0f172a;
}

.dark-theme .issue-title {
    color: #f8fafc;
}

.issue-details {
    font-size: 14px;
    color: #64748b;
}

.dark-theme .issue-details {
    color: #94a3b8;
}

.no-issues {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    color: #64748b;
    background-color: #f8fafc;
    border-radius: 8px;
}

.dark-theme .no-issues {
    background-color: #1e293b;
    color: #94a3b8;
}

/* Recommendations List */
.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 15px;
}

.dark-theme .recommendation-item {
    background-color: #1e293b;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.recommendation-number {
    background-color: #2563eb;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.dark-theme .recommendation-number {
    background-color: #3b82f6;
}

.recommendation-text {
    flex: 1;
    line-height: 1.6;
    color: #334155;
}

.dark-theme .recommendation-text {
    color: #cbd5e1;
}

.no-recommendations {
    text-align: center;
    padding: 30px;
    color: #64748b;
    background-color: #f8fafc;
    border-radius: 8px;
}

.dark-theme .no-recommendations {
    background-color: #1e293b;
    color: #94a3b8;
}

/* Detailed Analysis */
.analysis-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.dark-theme .analysis-section {
    background-color: #1e293b;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.analysis-section h4 {
    margin: 0;
    padding: 15px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    font-size: 16px;
    color: #0f172a;
}

.dark-theme .analysis-section h4 {
    background-color: #0f172a;
    border-bottom: 1px solid #334155;
    color: #f8fafc;
}

.analysis-content {
    padding: 15px;
}

.analysis-stat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.analysis-stat {
    background-color: #f8fafc;
    padding: 10px;
    border-radius: 6px;
}

.dark-theme .analysis-stat {
    background-color: #0f172a;
}

.analysis-stat-label {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 5px;
}

.dark-theme .analysis-stat-label {
    color: #94a3b8;
}

.analysis-stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #0f172a;
}

.dark-theme .analysis-stat-value {
    color: #f8fafc;
}

.analysis-detail-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.analysis-detail-item {
    padding: 10px 0;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
}

.dark-theme .analysis-detail-item {
    border-bottom: 1px solid #334155;
}

.analysis-detail-item:last-child {
    border-bottom: none;
}

.analysis-detail-label {
    color: #64748b;
}

.dark-theme .analysis-detail-label {
    color: #94a3b8;
}

.analysis-detail-value {
    font-weight: 500;
    color: #0f172a;
}

.dark-theme .analysis-detail-value {
    color: #f8fafc;
}
