import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from './Modal';
import { useTheme } from '../../contexts/ThemeContext';
import { generateTheme, applyTheme } from '../../utils/uiUtils';

const ThemeBuilderModal = ({ isOpen, onClose, addNotification }) => {
  const { darkMode } = useTheme();
  
  const [primaryColor, setPrimaryColor] = useState('#2563eb');
  const [customTheme, setCustomTheme] = useState(null);
  const [previewDarkMode, setPreviewDarkMode] = useState(darkMode);
  const [presetThemes, setPresetThemes] = useState([
    { name: 'Default Blue', color: '#2563eb' },
    { name: 'Emerald', color: '#10b981' },
    { name: 'Ruby', color: '#e11d48' },
    { name: 'Amber', color: '#f59e0b' },
    { name: 'Violet', color: '#8b5cf6' },
    { name: 'Slate', color: '#475569' }
  ]);
  
  // Generate theme preview when primary color changes
  useEffect(() => {
    const theme = generateTheme(primaryColor, previewDarkMode);
    setCustomTheme(theme);
  }, [primaryColor, previewDarkMode]);
  
  // Handle color change
  const handleColorChange = (e) => {
    setPrimaryColor(e.target.value);
  };
  
  // Handle preset theme selection
  const handlePresetSelect = (color) => {
    setPrimaryColor(color);
  };
  
  // Handle dark mode toggle for preview
  const handleDarkModeToggle = () => {
    setPreviewDarkMode(prev => !prev);
  };
  
  // Apply theme
  const handleApplyTheme = () => {
    // Generate and apply theme
    const theme = generateTheme(primaryColor, darkMode);
    applyTheme(theme);
    
    // Save theme to localStorage
    localStorage.setItem('customTheme', JSON.stringify({
      primaryColor,
      timestamp: Date.now()
    }));
    
    addNotification('Custom theme applied successfully', 'success');
  };
  
  // Reset theme to default
  const handleResetTheme = () => {
    // Remove custom theme from localStorage
    localStorage.removeItem('customTheme');
    
    // Apply default theme
    const defaultTheme = generateTheme('#2563eb', darkMode);
    applyTheme(defaultTheme);
    setPrimaryColor('#2563eb');
    
    addNotification('Theme reset to default', 'info');
  };
  
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      title="Theme Builder"
    >
      <ThemeBuilderContent>
        <ThemeSection>
          <h3>Choose Primary Color</h3>
          <ColorPickerContainer>
            <ColorPicker 
              type="color" 
              value={primaryColor}
              onChange={handleColorChange}
            />
            <ColorCode>
              {primaryColor}
            </ColorCode>
          </ColorPickerContainer>
        </ThemeSection>
        
        <ThemeSection>
          <h3>Preset Themes</h3>
          <PresetContainer>
            {presetThemes.map(theme => (
              <PresetColor 
                key={theme.name}
                style={{ backgroundColor: theme.color }}
                onClick={() => handlePresetSelect(theme.color)}
                className={primaryColor === theme.color ? 'active' : ''}
                title={theme.name}
              />
            ))}
          </PresetContainer>
        </ThemeSection>
        
        <ThemeSection>
          <h3>Preview</h3>
          <PreviewToggle>
            <span>Light Mode</span>
            <ToggleSwitch 
              type="checkbox"
              checked={previewDarkMode}
              onChange={handleDarkModeToggle}
            />
            <span>Dark Mode</span>
          </PreviewToggle>
          
          <ThemePreview style={{
            backgroundColor: customTheme?.backgroundColor,
            color: customTheme?.textColor,
            borderColor: customTheme?.borderColor
          }}>
            <PreviewHeader style={{
              borderColor: customTheme?.borderColor
            }}>
              <h4>Theme Preview</h4>
            </PreviewHeader>
            
            <PreviewContent>
              <PreviewCard style={{
                backgroundColor: customTheme?.cardColor,
                color: customTheme?.textColor,
                borderColor: customTheme?.borderColor
              }}>
                <PreviewCardTitle style={{
                  color: customTheme?.primaryColor
                }}>
                  Card Title
                </PreviewCardTitle>
                <PreviewCardContent style={{
                  color: customTheme?.textSecondary
                }}>
                  This is a preview of your custom theme.
                </PreviewCardContent>
                <PreviewButton style={{
                  backgroundColor: customTheme?.primaryColor,
                  color: 'white'
                }}>
                  Button
                </PreviewButton>
              </PreviewCard>
              
              <PreviewElements>
                <PreviewElement>
                  <span>Primary:</span>
                  <ColorSwatch style={{ backgroundColor: customTheme?.primaryColor }} />
                </PreviewElement>
                <PreviewElement>
                  <span>Secondary:</span>
                  <ColorSwatch style={{ backgroundColor: customTheme?.secondaryColor }} />
                </PreviewElement>
                <PreviewElement>
                  <span>Accent:</span>
                  <ColorSwatch style={{ backgroundColor: customTheme?.accentColor }} />
                </PreviewElement>
                <PreviewElement>
                  <span>Background:</span>
                  <ColorSwatch style={{ backgroundColor: customTheme?.backgroundColor }} />
                </PreviewElement>
                <PreviewElement>
                  <span>Card:</span>
                  <ColorSwatch style={{ backgroundColor: customTheme?.cardColor }} />
                </PreviewElement>
                <PreviewElement>
                  <span>Text:</span>
                  <ColorSwatch style={{ backgroundColor: customTheme?.textColor }} />
                </PreviewElement>
              </PreviewElements>
            </PreviewContent>
          </ThemePreview>
        </ThemeSection>
      </ThemeBuilderContent>
      
      <ModalFooter>
        <ModalButton 
          className="secondary"
          onClick={handleResetTheme}
        >
          Reset to Default
        </ModalButton>
        <ModalButton 
          onClick={handleApplyTheme}
        >
          Apply Theme
        </ModalButton>
      </ModalFooter>
    </Modal>
  );
};

const ThemeBuilderContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const ThemeSection = styled.div`
  h3 {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    letter-spacing: -0.01em;
  }
`;

const ColorPickerContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const ColorPicker = styled.input`
  width: 4rem;
  height: 4rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  overflow: hidden;
  
  &::-webkit-color-swatch-wrapper {
    padding: 0;
  }
  
  &::-webkit-color-swatch {
    border: none;
    border-radius: var(--border-radius);
  }
`;

const ColorCode = styled.div`
  font-family: monospace;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  background-color: var(--hover-color);
  border-radius: var(--border-radius);
  color: var(--text-color);
  border: 1px solid var(--border-color);
`;

const PresetContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
`;

const PresetColor = styled.div`
  width: 3rem;
  height: 3rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  border: 2px solid transparent;
  
  &:hover {
    transform: scale(1.1);
  }
  
  &.active {
    border-color: var(--text-color);
    box-shadow: 0 0 0 2px white, 0 0 0 4px var(--border-color);
  }
`;

const PreviewToggle = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.25rem;
  
  span {
    font-size: 0.875rem;
    color: var(--text-secondary);
  }
`;

const ToggleSwitch = styled.input`
  width: 3rem;
  height: 1.5rem;
  appearance: none;
  background-color: var(--border-color);
  border-radius: 9999px;
  position: relative;
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:checked {
    background-color: var(--primary-color);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 1.25rem;
    height: 1.25rem;
    background-color: white;
    border-radius: 50%;
    transition: all var(--animation-medium) ease;
  }
  
  &:checked::before {
    left: 1.625rem;
  }
`;

const ThemePreview = styled.div`
  border: 1px solid;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
`;

const PreviewHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid;
  
  h4 {
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
  }
`;

const PreviewContent = styled.div`
  padding: 1.5rem;
  display: flex;
  gap: 1.5rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const PreviewCard = styled.div`
  flex: 1;
  padding: 1.25rem;
  border-radius: var(--border-radius);
  border: 1px solid;
`;

const PreviewCardTitle = styled.h5`
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: 0.75rem;
`;

const PreviewCardContent = styled.p`
  font-size: 0.875rem;
  margin-bottom: 1.25rem;
`;

const PreviewButton = styled.button`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
`;

const PreviewElements = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const PreviewElement = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  span {
    font-size: 0.875rem;
  }
`;

const ColorSwatch = styled.div`
  width: 2rem;
  height: 1.25rem;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
`;

const ModalButton = styled.button`
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--animation-medium) ease;
  
  &:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  &.secondary {
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    
    &:hover {
      background-color: var(--hover-color);
      border-color: var(--text-secondary);
    }
  }
`;

export default ThemeBuilderModal;
