// THIS FILE IS AUTO GENERATED
import { IconTree, IconType } from '../lib'
export declare const RxAccessibility: IconType;
export declare const RxActivityLog: IconType;
export declare const RxAlignBaseline: IconType;
export declare const RxAlignBottom: IconType;
export declare const RxAlignCenterHorizontally: IconType;
export declare const RxAlignCenterVertically: IconType;
export declare const RxAlignLeft: IconType;
export declare const RxAlignRight: IconType;
export declare const RxAlignTop: IconType;
export declare const RxAllSides: IconType;
export declare const RxAngle: IconType;
export declare const RxArchive: IconType;
export declare const RxArrowBottomLeft: IconType;
export declare const RxArrowBottomRight: IconType;
export declare const RxArrowDown: IconType;
export declare const RxArrowLeft: IconType;
export declare const RxArrowRight: IconType;
export declare const RxArrowTopLeft: IconType;
export declare const RxArrowTopRight: IconType;
export declare const RxArrowUp: IconType;
export declare const RxAspectRatio: IconType;
export declare const RxAvatar: IconType;
export declare const RxBackpack: IconType;
export declare const RxBadge: IconType;
export declare const RxBarChart: IconType;
export declare const RxBell: IconType;
export declare const RxBlendingMode: IconType;
export declare const RxBookmarkFilled: IconType;
export declare const RxBookmark: IconType;
export declare const RxBorderAll: IconType;
export declare const RxBorderBottom: IconType;
export declare const RxBorderDashed: IconType;
export declare const RxBorderDotted: IconType;
export declare const RxBorderLeft: IconType;
export declare const RxBorderNone: IconType;
export declare const RxBorderRight: IconType;
export declare const RxBorderSolid: IconType;
export declare const RxBorderSplit: IconType;
export declare const RxBorderStyle: IconType;
export declare const RxBorderTop: IconType;
export declare const RxBorderWidth: IconType;
export declare const RxBoxModel: IconType;
export declare const RxBox: IconType;
export declare const RxButton: IconType;
export declare const RxCalendar: IconType;
export declare const RxCamera: IconType;
export declare const RxCardStackMinus: IconType;
export declare const RxCardStackPlus: IconType;
export declare const RxCardStack: IconType;
export declare const RxCaretDown: IconType;
export declare const RxCaretLeft: IconType;
export declare const RxCaretRight: IconType;
export declare const RxCaretSort: IconType;
export declare const RxCaretUp: IconType;
export declare const RxChatBubble: IconType;
export declare const RxCheckCircled: IconType;
export declare const RxCheck: IconType;
export declare const RxCheckbox: IconType;
export declare const RxChevronDown: IconType;
export declare const RxChevronLeft: IconType;
export declare const RxChevronRight: IconType;
export declare const RxChevronUp: IconType;
export declare const RxCircleBackslash: IconType;
export declare const RxCircle: IconType;
export declare const RxClipboardCopy: IconType;
export declare const RxClipboard: IconType;
export declare const RxClock: IconType;
export declare const RxCode: IconType;
export declare const RxCodesandboxLogo: IconType;
export declare const RxColorWheel: IconType;
export declare const RxColumnSpacing: IconType;
export declare const RxColumns: IconType;
export declare const RxCommit: IconType;
export declare const RxComponent1: IconType;
export declare const RxComponent2: IconType;
export declare const RxComponentBoolean: IconType;
export declare const RxComponentInstance: IconType;
export declare const RxComponentNone: IconType;
export declare const RxComponentPlaceholder: IconType;
export declare const RxContainer: IconType;
export declare const RxCookie: IconType;
export declare const RxCopy: IconType;
export declare const RxCornerBottomLeft: IconType;
export declare const RxCornerBottomRight: IconType;
export declare const RxCornerTopLeft: IconType;
export declare const RxCornerTopRight: IconType;
export declare const RxCorners: IconType;
export declare const RxCountdownTimer: IconType;
export declare const RxCounterClockwiseClock: IconType;
export declare const RxCrop: IconType;
export declare const RxCross1: IconType;
export declare const RxCross2: IconType;
export declare const RxCrossCircled: IconType;
export declare const RxCrosshair1: IconType;
export declare const RxCrosshair2: IconType;
export declare const RxCrumpledPaper: IconType;
export declare const RxCube: IconType;
export declare const RxCursorArrow: IconType;
export declare const RxCursorText: IconType;
export declare const RxDash: IconType;
export declare const RxDashboard: IconType;
export declare const RxDesktop: IconType;
export declare const RxDimensions: IconType;
export declare const RxDisc: IconType;
export declare const RxDiscordLogo: IconType;
export declare const RxDividerHorizontal: IconType;
export declare const RxDividerVertical: IconType;
export declare const RxDotFilled: IconType;
export declare const RxDot: IconType;
export declare const RxDotsHorizontal: IconType;
export declare const RxDotsVertical: IconType;
export declare const RxDoubleArrowDown: IconType;
export declare const RxDoubleArrowLeft: IconType;
export declare const RxDoubleArrowRight: IconType;
export declare const RxDoubleArrowUp: IconType;
export declare const RxDownload: IconType;
export declare const RxDragHandleDots1: IconType;
export declare const RxDragHandleDots2: IconType;
export declare const RxDragHandleHorizontal: IconType;
export declare const RxDragHandleVertical: IconType;
export declare const RxDrawingPinFilled: IconType;
export declare const RxDrawingPin: IconType;
export declare const RxDropdownMenu: IconType;
export declare const RxEnterFullScreen: IconType;
export declare const RxEnter: IconType;
export declare const RxEnvelopeClosed: IconType;
export declare const RxEnvelopeOpen: IconType;
export declare const RxEraser: IconType;
export declare const RxExclamationTriangle: IconType;
export declare const RxExitFullScreen: IconType;
export declare const RxExit: IconType;
export declare const RxExternalLink: IconType;
export declare const RxEyeClosed: IconType;
export declare const RxEyeNone: IconType;
export declare const RxEyeOpen: IconType;
export declare const RxFace: IconType;
export declare const RxFigmaLogo: IconType;
export declare const RxFileMinus: IconType;
export declare const RxFilePlus: IconType;
export declare const RxFileText: IconType;
export declare const RxFile: IconType;
export declare const RxFontBold: IconType;
export declare const RxFontFamily: IconType;
export declare const RxFontItalic: IconType;
export declare const RxFontRoman: IconType;
export declare const RxFontSize: IconType;
export declare const RxFontStyle: IconType;
export declare const RxFrame: IconType;
export declare const RxFramerLogo: IconType;
export declare const RxGear: IconType;
export declare const RxGithubLogo: IconType;
export declare const RxGlobe: IconType;
export declare const RxGrid: IconType;
export declare const RxGroup: IconType;
export declare const RxHalf1: IconType;
export declare const RxHalf2: IconType;
export declare const RxHamburgerMenu: IconType;
export declare const RxHand: IconType;
export declare const RxHeading: IconType;
export declare const RxHeartFilled: IconType;
export declare const RxHeart: IconType;
export declare const RxHeight: IconType;
export declare const RxHobbyKnife: IconType;
export declare const RxHome: IconType;
export declare const RxIconjarLogo: IconType;
export declare const RxIdCard: IconType;
export declare const RxImage: IconType;
export declare const RxInfoCircled: IconType;
export declare const RxInput: IconType;
export declare const RxInstagramLogo: IconType;
export declare const RxKeyboard: IconType;
export declare const RxLapTimer: IconType;
export declare const RxLaptop: IconType;
export declare const RxLayers: IconType;
export declare const RxLayout: IconType;
export declare const RxLetterCaseCapitalize: IconType;
export declare const RxLetterCaseLowercase: IconType;
export declare const RxLetterCaseToggle: IconType;
export declare const RxLetterCaseUppercase: IconType;
export declare const RxLetterSpacing: IconType;
export declare const RxLightningBolt: IconType;
export declare const RxLineHeight: IconType;
export declare const RxLink1: IconType;
export declare const RxLink2: IconType;
export declare const RxLinkBreak1: IconType;
export declare const RxLinkBreak2: IconType;
export declare const RxLinkNone1: IconType;
export declare const RxLinkNone2: IconType;
export declare const RxLinkedinLogo: IconType;
export declare const RxListBullet: IconType;
export declare const RxLockClosed: IconType;
export declare const RxLockOpen1: IconType;
export declare const RxLockOpen2: IconType;
export declare const RxLoop: IconType;
export declare const RxMagicWand: IconType;
export declare const RxMagnifyingGlass: IconType;
export declare const RxMargin: IconType;
export declare const RxMaskOff: IconType;
export declare const RxMaskOn: IconType;
export declare const RxMinusCircled: IconType;
export declare const RxMinus: IconType;
export declare const RxMix: IconType;
export declare const RxMixerHorizontal: IconType;
export declare const RxMixerVertical: IconType;
export declare const RxMobile: IconType;
export declare const RxModulzLogo: IconType;
export declare const RxMoon: IconType;
export declare const RxMove: IconType;
export declare const RxNotionLogo: IconType;
export declare const RxOpacity: IconType;
export declare const RxOpenInNewWindow: IconType;
export declare const RxOverline: IconType;
export declare const RxPadding: IconType;
export declare const RxPaperPlane: IconType;
export declare const RxPause: IconType;
export declare const RxPencil1: IconType;
export declare const RxPencil2: IconType;
export declare const RxPerson: IconType;
export declare const RxPieChart: IconType;
export declare const RxPilcrow: IconType;
export declare const RxPinBottom: IconType;
export declare const RxPinLeft: IconType;
export declare const RxPinRight: IconType;
export declare const RxPinTop: IconType;
export declare const RxPlay: IconType;
export declare const RxPlusCircled: IconType;
export declare const RxPlus: IconType;
export declare const RxQuestionMarkCircled: IconType;
export declare const RxQuestionMark: IconType;
export declare const RxQuote: IconType;
export declare const RxRadiobutton: IconType;
export declare const RxReader: IconType;
export declare const RxReload: IconType;
export declare const RxReset: IconType;
export declare const RxResume: IconType;
export declare const RxRocket: IconType;
export declare const RxRotateCounterClockwise: IconType;
export declare const RxRowSpacing: IconType;
export declare const RxRows: IconType;
export declare const RxRulerHorizontal: IconType;
export declare const RxRulerSquare: IconType;
export declare const RxScissors: IconType;
export declare const RxSection: IconType;
export declare const RxSewingPinFilled: IconType;
export declare const RxSewingPin: IconType;
export declare const RxShadowInner: IconType;
export declare const RxShadowNone: IconType;
export declare const RxShadowOuter: IconType;
export declare const RxShadow: IconType;
export declare const RxShare1: IconType;
export declare const RxShare2: IconType;
export declare const RxShuffle: IconType;
export declare const RxSize: IconType;
export declare const RxSketchLogo: IconType;
export declare const RxSlash: IconType;
export declare const RxSlider: IconType;
export declare const RxSpaceBetweenHorizontally: IconType;
export declare const RxSpaceBetweenVertically: IconType;
export declare const RxSpaceEvenlyHorizontally: IconType;
export declare const RxSpaceEvenlyVertically: IconType;
export declare const RxSpeakerLoud: IconType;
export declare const RxSpeakerModerate: IconType;
export declare const RxSpeakerOff: IconType;
export declare const RxSpeakerQuiet: IconType;
export declare const RxSquare: IconType;
export declare const RxStack: IconType;
export declare const RxStarFilled: IconType;
export declare const RxStar: IconType;
export declare const RxStitchesLogo: IconType;
export declare const RxStop: IconType;
export declare const RxStopwatch: IconType;
export declare const RxStretchHorizontally: IconType;
export declare const RxStretchVertically: IconType;
export declare const RxStrikethrough: IconType;
export declare const RxSun: IconType;
export declare const RxSwitch: IconType;
export declare const RxSymbol: IconType;
export declare const RxTable: IconType;
export declare const RxTarget: IconType;
export declare const RxTextAlignBottom: IconType;
export declare const RxTextAlignCenter: IconType;
export declare const RxTextAlignJustify: IconType;
export declare const RxTextAlignLeft: IconType;
export declare const RxTextAlignMiddle: IconType;
export declare const RxTextAlignRight: IconType;
export declare const RxTextAlignTop: IconType;
export declare const RxTextNone: IconType;
export declare const RxText: IconType;
export declare const RxThickArrowDown: IconType;
export declare const RxThickArrowLeft: IconType;
export declare const RxThickArrowRight: IconType;
export declare const RxThickArrowUp: IconType;
export declare const RxTimer: IconType;
export declare const RxTokens: IconType;
export declare const RxTrackNext: IconType;
export declare const RxTrackPrevious: IconType;
export declare const RxTransform: IconType;
export declare const RxTransparencyGrid: IconType;
export declare const RxTrash: IconType;
export declare const RxTriangleDown: IconType;
export declare const RxTriangleLeft: IconType;
export declare const RxTriangleRight: IconType;
export declare const RxTriangleUp: IconType;
export declare const RxTwitterLogo: IconType;
export declare const RxUnderline: IconType;
export declare const RxUpdate: IconType;
export declare const RxUpload: IconType;
export declare const RxValueNone: IconType;
export declare const RxValue: IconType;
export declare const RxVercelLogo: IconType;
export declare const RxVideo: IconType;
export declare const RxViewGrid: IconType;
export declare const RxViewHorizontal: IconType;
export declare const RxViewNone: IconType;
export declare const RxViewVertical: IconType;
export declare const RxWidth: IconType;
export declare const RxZoomIn: IconType;
export declare const RxZoomOut: IconType;
