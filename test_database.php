<?php
// Test script to verify database connection and table structure

echo "<h1>Database Connection Test</h1>";

// Database configuration
$servername = "localhost";
$username = "root";  // Change this to your database username
$password = "";      // Change this to your database password
$dbname = "esp32_data";  // Change this to your database name

// Create connection
$conn = new mysqli($servername, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("<p style='color:red'>Connection to MySQL server failed: " . $conn->connect_error . "</p>");
}

echo "<p style='color:green'>Connected to MySQL server successfully.</p>";

// Check if database exists
$result = $conn->query("SHOW DATABASES LIKE '$dbname'");
if ($result->num_rows > 0) {
    echo "<p style='color:green'>Database '$dbname' exists.</p>";
} else {
    echo "<p style='color:red'>Database '$dbname' does not exist.</p>";
    echo "<p>Creating database...</p>";
    
    $sql = "CREATE DATABASE IF NOT EXISTS $dbname";
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color:green'>Database created successfully.</p>";
    } else {
        echo "<p style='color:red'>Error creating database: " . $conn->error . "</p>";
        $conn->close();
        exit;
    }
}

// Select the database
$conn->select_db($dbname);

// Check if table exists
$result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
if ($result->num_rows > 0) {
    echo "<p style='color:green'>Table 'electrical_data' exists.</p>";
} else {
    echo "<p style='color:red'>Table 'electrical_data' does not exist.</p>";
    echo "<p>Creating table...</p>";
    
    $sql = "CREATE TABLE IF NOT EXISTS electrical_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        voltage_1 FLOAT,
        voltage_2 FLOAT,
        voltage_3 FLOAT,
        current_1 FLOAT,
        current_2 FLOAT,
        current_3 FLOAT,
        pf_1 FLOAT,
        pf_2 FLOAT,
        pf_3 FLOAT,
        kva_1 FLOAT,
        kva_2 FLOAT,
        kva_3 FLOAT,
        total_kva FLOAT,
        total_kw FLOAT,
        total_kvar FLOAT,
        frequency FLOAT,
        timestamp DATETIME
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color:green'>Table created successfully.</p>";
    } else {
        echo "<p style='color:red'>Error creating table: " . $conn->error . "</p>";
    }
}

// Check table structure
echo "<h2>Table Structure</h2>";
$result = $conn->query("DESCRIBE electrical_data");
if ($result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row["Field"] . "</td>";
        echo "<td>" . $row["Type"] . "</td>";
        echo "<td>" . $row["Null"] . "</td>";
        echo "<td>" . $row["Key"] . "</td>";
        echo "<td>" . $row["Default"] . "</td>";
        echo "<td>" . $row["Extra"] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red'>Error getting table structure.</p>";
}

// Check if there's any data in the table
$result = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
$row = $result->fetch_assoc();
echo "<h2>Data Count</h2>";
echo "<p>Number of records in the table: " . $row['count'] . "</p>";

// Insert test data if the table is empty
if ($row['count'] == 0) {
    echo "<p>Inserting test data...</p>";
    
    // Current timestamp
    $timestamp = date('Y-m-d H:i:s');
    
    // Sample data
    $sql = "INSERT INTO electrical_data (
        voltage_1, voltage_2, voltage_3,
        current_1, current_2, current_3,
        pf_1, pf_2, pf_3,
        kva_1, kva_2, kva_3,
        total_kva, total_kw, total_kvar,
        frequency, timestamp
    ) VALUES 
    (400.5, 401.2, 399.8, 50.1, 49.8, 50.3, 0.92, 0.93, 0.91, 20.1, 19.8, 20.3, 60.2, 55.4, 24.8, 50.1, '$timestamp')";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color:green'>Test data inserted successfully.</p>";
    } else {
        echo "<p style='color:red'>Error inserting test data: " . $conn->error . "</p>";
    }
}

// Display the latest record
$result = $conn->query("SELECT * FROM electrical_data ORDER BY timestamp DESC LIMIT 1");
if ($result->num_rows > 0) {
    echo "<h2>Latest Record</h2>";
    $row = $result->fetch_assoc();
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    foreach ($row as $key => $value) {
        echo "<tr>";
        echo "<td>" . $key . "</td>";
        echo "<td>" . $value . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red'>No records found in the table.</p>";
}

// Test the receive_data.php endpoint
echo "<h2>Testing receive_data.php Endpoint</h2>";
$url = "http://localhost/online%20data%20logger/backend/receive_data.php";
$data = [
    "voltage_1" => 230.5,
    "voltage_2" => 231.2,
    "voltage_3" => 229.8,
    "current_1" => 5.1,
    "current_2" => 5.2,
    "current_3" => 5.0,
    "pf_1" => 0.92,
    "pf_2" => 0.93,
    "pf_3" => 0.91,
    "kva_1" => 1150.5,
    "kva_2" => 1151.2,
    "kva_3" => 1149.8,
    "total_kva" => 3450.5,
    "total_kw" => 3277.8,
    "total_kvar" => 1076.5,
    "frequency" => 50.1
];

$options = [
    'http' => [
        'header'  => "Content-type: application/json\r\n",
        'method'  => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
try {
    $result = file_get_contents($url, false, $context);
    echo "<p>Endpoint response: " . $result . "</p>";
} catch (Exception $e) {
    echo "<p style='color:red'>Error testing endpoint: " . $e->getMessage() . "</p>";
}

$conn->close();
echo "<p>Database connection closed.</p>";
?>
