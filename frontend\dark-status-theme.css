/* Dark Status Theme - Modern Dark UI for Status Section */

:root {
    /* Dark theme colors */
    --dark-bg: #0f172a;
    --dark-card-bg: #1e293b;
    --dark-status-bg: rgba(30, 41, 59, 0.7);
    --dark-border: #334155;
    --dark-text: #f1f5f9;
    --dark-text-secondary: #cbd5e1;
    --dark-text-muted: #94a3b8;
    --dark-accent: #5ECCE9;
    --dark-accent-dark: #007A8C;
    --dark-success: #10b981;
    --dark-success-glow: rgba(16, 185, 129, 0.15);
    --dark-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

/* Dark status header */
.dark-status-header {
    background-color: var(--dark-bg);
    color: var(--dark-text);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: var(--dark-shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Add subtle gradient overlay */
.dark-status-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 122, 140, 0.15), transparent);
    pointer-events: none;
}

/* Title section */
.dark-status-title {
    display: flex;
    flex-direction: column;
}

.header-logo-container {
    margin-bottom: 8px;
}

.header-logo {
    height: 60px;
    max-width: 100%;
}

.dark-status-title .subtitle {
    font-size: 0.875rem;
    color: var(--dark-text-muted);
    font-weight: 400;
    margin-top: 4px;
}

/* Status boxes container */
.dark-status-boxes {
    display: flex;
    gap: 16px;
}

/* Individual status box */
.dark-status-box {
    background-color: var(--dark-status-bg);
    border: 1px solid var(--dark-border);
    border-radius: 8px;
    padding: 12px 16px;
    min-width: 180px;
    backdrop-filter: blur(8px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s ease;
}

.dark-status-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* System status box with success styling */
.dark-status-box.system {
    border-color: rgba(94, 204, 233, 0.3);
    box-shadow: 0 0 15px rgba(94, 204, 233, 0.15);
}

/* Status icon */
.dark-status-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(30, 41, 59, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.dark-status-icon .material-icons-round {
    font-size: 18px;
    color: var(--dark-text-secondary);
}

.dark-status-box.system .dark-status-icon .material-icons-round {
    color: var(--dark-accent);
}

/* Status text */
.dark-status-text {
    display: flex;
    flex-direction: column;
}

.dark-status-label {
    font-size: 0.75rem;
    color: var(--dark-text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 2px;
}

.dark-status-value {
    font-size: 0.9375rem;
    color: var(--dark-text);
    font-weight: 600;
}

.dark-status-box.system .dark-status-value {
    color: var(--dark-accent);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dark-status-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .dark-status-boxes {
        width: 100%;
        justify-content: space-between;
    }

    .dark-status-box {
        min-width: 0;
        flex: 1;
    }
}

@media (max-width: 480px) {
    .dark-status-boxes {
        flex-direction: column;
    }
}
