/**
 * Utility functions for performance optimization
 */

/**
 * Debounce function to limit how often a function is called
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
export const debounce = (func, wait = 300) => {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttle function to limit how often a function is called
 * @param {Function} func - Function to throttle
 * @param {number} limit - Limit in milliseconds
 * @returns {Function} - Throttled function
 */
export const throttle = (func, limit = 300) => {
  let inThrottle;
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
};

/**
 * Memoize function to cache results of expensive calculations
 * @param {Function} func - Function to memoize
 * @returns {Function} - Memoized function
 */
export const memoize = (func) => {
  const cache = new Map();
  
  return function memoized(...args) {
    const key = JSON.stringify(args);
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = func(...args);
    cache.set(key, result);
    return result;
  };
};

/**
 * Aggregate data points to reduce dataset size
 * @param {Array} data - Array of data points
 * @param {number} maxPoints - Maximum number of points to return
 * @param {string} valueKey - Key to access the value in data objects
 * @returns {Array} - Aggregated data points
 */
export const aggregateData = (data, maxPoints = 100, valueKey = 'y') => {
  if (!data || data.length <= maxPoints) return data;
  
  const aggregationFactor = Math.ceil(data.length / maxPoints);
  const result = [];
  
  for (let i = 0; i < data.length; i += aggregationFactor) {
    const chunk = data.slice(i, i + aggregationFactor);
    
    // Calculate average for this chunk
    const sum = chunk.reduce((acc, point) => acc + parseFloat(point[valueKey] || 0), 0);
    const avg = sum / chunk.length;
    
    // Use the first timestamp in the chunk
    result.push({
      ...chunk[0],
      [valueKey]: avg,
      aggregated: true,
      count: chunk.length
    });
  }
  
  return result;
};

/**
 * Create a worker for heavy computations
 * @param {Function} workerFunction - Function to run in worker
 * @returns {Worker} - Web worker
 */
export const createWorker = (workerFunction) => {
  const blob = new Blob(
    [`(${workerFunction.toString()})()`],
    { type: 'application/javascript' }
  );
  return new Worker(URL.createObjectURL(blob));
};

/**
 * Example worker function for data processing
 */
export const dataProcessingWorker = () => {
  self.onmessage = (e) => {
    const { data, operation, params } = e.data;
    
    let result;
    switch (operation) {
      case 'aggregate':
        result = aggregateDataInWorker(data, params.maxPoints, params.valueKey);
        break;
      case 'movingAverage':
        result = calculateMovingAverageInWorker(data, params.window, params.valueKey);
        break;
      case 'detectAnomalies':
        result = detectAnomaliesInWorker(data, params.threshold, params.valueKey);
        break;
      default:
        result = { error: 'Unknown operation' };
    }
    
    self.postMessage(result);
  };
  
  // Worker implementations
  function aggregateDataInWorker(data, maxPoints = 100, valueKey = 'y') {
    if (!data || data.length <= maxPoints) return data;
    
    const aggregationFactor = Math.ceil(data.length / maxPoints);
    const result = [];
    
    for (let i = 0; i < data.length; i += aggregationFactor) {
      const chunk = data.slice(i, i + aggregationFactor);
      
      // Calculate average for this chunk
      const sum = chunk.reduce((acc, point) => acc + parseFloat(point[valueKey] || 0), 0);
      const avg = sum / chunk.length;
      
      // Use the first timestamp in the chunk
      result.push({
        ...chunk[0],
        [valueKey]: avg,
        aggregated: true,
        count: chunk.length
      });
    }
    
    return result;
  }
  
  function calculateMovingAverageInWorker(data, window = 5, valueKey = 'y') {
    if (!data || data.length === 0) return [];
    if (window <= 1) return data;
  
    const result = [];
    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - window + 1);
      const end = i + 1;
      const windowData = data.slice(start, end);
      
      const sum = windowData.reduce((acc, point) => acc + parseFloat(point[valueKey] || 0), 0);
      const avg = sum / windowData.length;
      
      result.push({
        ...data[i],
        [valueKey]: avg
      });
    }
    
    return result;
  }
  
  function detectAnomaliesInWorker(data, threshold = 2.5, valueKey = 'y') {
    if (!data || data.length < 3) return [];
    
    // Calculate mean and standard deviation
    const values = data.map(point => parseFloat(point[valueKey] || 0));
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const variance = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    // Find anomalies
    const anomalies = [];
    for (let i = 0; i < data.length; i++) {
      const value = values[i];
      const zScore = Math.abs((value - mean) / stdDev);
      
      if (zScore > threshold) {
        anomalies.push(i);
      }
    }
    
    return anomalies;
  }
};
